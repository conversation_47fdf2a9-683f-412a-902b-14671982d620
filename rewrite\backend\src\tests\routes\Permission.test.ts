import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { permissionsList } from '../data/Permissions';
import Permission from '../mocks/models/permissionSimple.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Permission', () => require('../mocks/models/permissionSimple.mock'));

describe('Permission API', () => {
    describe('GET /api/permissions', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/permissions');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch all permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Permission as any).find.mockResolvedValueOnce(permissionsList);

                    const res = await request(app)
                        .get('/api/permissions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(permissionsList);
                    expect((Permission as any).find).toHaveBeenCalledWith();
                });

                it('should return 200 with empty array when no permissions found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Permission as any).find.mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/permissions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual([]);
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Permission as any).find.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/permissions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});