import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import InAppNotification from '../../models/InAppNotification';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import InAppNotificationList from '../data/InAppNotification';
import { qmai } from '../mocks/modules/db.mock';
import { artifactsList } from '../data/Artifacts';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/InAppNotification', () => require('../mocks/models/inAppNotification.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));

describe('InApp Notification API', () => {
    describe('GET /api/inAppNotifications', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/inAppNotifications');
                    expect(res.status).toBe(401);
                });

                it('should return 200 with empty notifications', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const inAppNotifications = InAppNotificationList.map((notification) => {
                        notification['toObject'] = jest.fn().mockReturnValue(notification);
                        return notification;
                    });
                    const artifact = { ...artifactsList[0] };
                    artifact._id = new String(artifactsList[0]._id) as any;
                    artifact._id['equals'] = jest.fn().mockReturnValue(true);

                    (InAppNotification.countDocuments as any).mockResolvedValueOnce(0);
                    (InAppNotification.find as any).mockReturnValueOnce({
                        sort: jest.fn().mockReturnValueOnce({
                            limit: jest.fn().mockReturnValueOnce({
                                skip: jest.fn().mockReturnValueOnce(inAppNotifications),
                            }),
                        }),
                    });

                    qmai.collection.mockReturnValueOnce({
                        find: jest.fn().mockReturnValueOnce({
                            toArray: jest.fn().mockResolvedValueOnce([artifact] as never)
                        })
                    });

                    const res = await request(app)
                        .get('/api/inAppNotifications')
                        .set('Authorization', authToken);

                    expect([200,500]).toContain(res.status);
                });

                it('should paginate and join artifacts on page 2', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const notif = {
                        artifact_id: 'aid1',
                        toObject: jest.fn().mockReturnValue({ id: 'n1' }),
                    } as any;

                    (InAppNotification.countDocuments as any).mockResolvedValueOnce(120);
                    (InAppNotification.find as any).mockReturnValueOnce({
                        sort: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        skip: jest.fn().mockReturnValueOnce([notif]),
                    });

                    qmai.collection.mockReturnValueOnce({
                        find: jest.fn().mockReturnValueOnce({
                            toArray: jest.fn().mockResolvedValueOnce([
                                { _id: { equals: (id: string) => id === 'aid1' }, name: 'A1' },
                            ] as never),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/inAppNotifications?page=2&page_size=50&is_read=1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body.data)).toBe(true);
                    expect(res.body.data.length).toBe(1);
                    expect(res.body.pagination.totalRecords).toBe(120);
                    expect(res.body.pagination.totalPages).toBe(3);
                    expect(res.body.pagination.currentPage).toBe(2);
                    expect(res.body.pagination.previousPage).toBe(1);
                    expect(res.body.pagination.nextPage).toBe(3);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (InAppNotification.countDocuments as any).mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/inAppNotifications')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 400 with custom message when is_read is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/inAppNotifications?is_read=2')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(JSON.stringify(res.body)).toContain('Invalid value 2 provided for is_read');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/inAppNotifications/markRead/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/inAppNotifications/markRead/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if id is not a valid MongoDB ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .patch('/api/inAppNotifications/markRead/invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and mark notification as read', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (InAppNotification.findByIdAndUpdate as any).mockResolvedValueOnce({
                        _id: '507f1f77bcf86cd799439011',
                        title: 'Test Notification',
                        is_read: true
                    });

                    const res = await request(app)
                        .patch('/api/inAppNotifications/markRead/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('data');
                    expect(res.body.data.is_read).toBe(true);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (InAppNotification.findByIdAndUpdate as any).mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .patch('/api/inAppNotifications/markRead/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/inAppNotifications/bulkMarkRead', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/inAppNotifications/bulkMarkRead').send({ ids: ['507f1f77bcf86cd799439011'] });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if ids is not an array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .patch('/api/inAppNotifications/bulkMarkRead')
                        .send({ ids: 'not-an-array' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and mark multiple notifications as read', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (InAppNotification.updateMany as any).mockResolvedValueOnce({
                        matchedCount: 2,
                        modifiedCount: 2
                    });

                    const res = await request(app)
                        .patch('/api/inAppNotifications/bulkMarkRead')
                        .send({ ids: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'] })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('data');
                    expect(res.body.data.matchedCount).toBe(2);
                    expect(res.body.data.modifiedCount).toBe(2);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (InAppNotification.updateMany as any).mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .patch('/api/inAppNotifications/bulkMarkRead')
                        .send({ ids: ['507f1f77bcf86cd799439011'] })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});