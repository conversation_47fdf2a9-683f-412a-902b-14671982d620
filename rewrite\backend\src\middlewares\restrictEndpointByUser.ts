import { userEndpointRestrictions } from "../utils/userEndpointRestrictions";
import { Request, Response, NextFunction } from "express";

const restrictEndpointByUser = (req: Request, res: Response, next: NextFunction) => {
    const endpointId = req._endpoint_id;
    if (endpointId && userEndpointRestrictions[endpointId]) {
        if (process.env.NODE_ENV !== "portal") {
            return res.status(403).json({ message: "Access denied. Operation restricted to portal environment." });
        }
        const allowedUsers = userEndpointRestrictions[endpointId];
        if (!allowedUsers.includes(req.user._id.toString())) {
            return res.status(403).json({ message: "Access denied. Operation restricted to specific user(s)." });
        }
    }
    next();
};

export default restrictEndpointByUser;
