import express, { Request, Response, NextFunction } from "express";
import { validateData } from "../middlewares/validator";
import { body, query } from "express-validator";
import { validateError, canAccessVessel, splitByMonthsUTC, getLocationsCollections } from "../utils/functions";
import limitPromise from "../modules/pLimit";
import mongoose, { isValidObjectId } from "mongoose";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import db from "../modules/db";
import compression from "compression";
import vesselService from "../services/Vessel.service";
import vesselLocationService from "../services/VesselLocation.service";
import { IVessel, IVesselWithUserDetails } from "../interfaces/Vessel";
import { ILocationOptimized } from "../interfaces/VesselLocation";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselName",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES),
    isAuthenticated,
    (req: Request, res: Response, next: NextFunction) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("lastKnown")
                    .customSanitizer((v: string | number) => Number(v))
                    .isInt({ min: 0, max: 1 })
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v: string | number) => Number(v))
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v: string | number) => Number(v))
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v: string[]) => v.map((id: string) => new mongoose.Types.ObjectId(id)))
                    .withMessage((value: string[], { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselName } = req.params;
            const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
            console.log(`/vesselLocations ${vesselName}`, startTimestamp, endTimestamp, lastKnown);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findByAssignedUnitId({ unitId: vesselName });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for '${vesselName}'` });
            }

            if (lastKnown) {
                // const lastLocation = await db.lookups.collection("last_locations_lookup").findOne({ vessel_id: vessel._id }, {
                //     projection: {
                //         _id: '$last_location_id',
                //         longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                //         latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                //         timestamp: '$data.timestamp',
                //         groundSpeed: '$data.groundSpeed',
                //         isStationary: '$data.isStationary'
                //     }
                // });

                const lastLocation = await vesselLocationService.findLastKnownLocation({
                    vesselId: vessel._id,
                    projection: {
                        _id: "$last_location_id",
                        longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                        latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                        timestamp: "$data.timestamp",
                        groundSpeed: "$data.groundSpeed",
                        isStationary: "$data.isStationary",
                    },
                });

                console.log(`/vesselLocations lastKnown ${vesselName} time taken to query ${new Date().getTime() - ts}`);
                console.log(`/vesselLocations lastKnown ${vesselName} received ${lastLocation ? 1 : 0} coordinates`);
                console.log(`/vesselLocations lastKnown ${vesselName} time taken to respond ${new Date().getTime() - ts}`);

                return res.json(lastLocation);
            }

            const collections = await getLocationsCollections(db.locationsOptimized, startTimestamp, endTimestamp);
            if (!collections.length) return res.status(400).json({ message: "No location collections found for the specified time range" });

            const query: Record<string, string | mongoose.Types.ObjectId | { $gt: Date; $lt: Date } | { $nin: mongoose.Types.ObjectId[] }> = {
                "metadata.onboardVesselId": new mongoose.Types.ObjectId(vessel._id),
            };
            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            var locations = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/vesselLocations ${vesselName} querying DB`);
                const resultPromises = collections.map((collection: mongoose.Collection) => {
                    const cursor = collection.aggregate([
                        { $match: query },
                        {
                            $project: {
                                _id: 1,
                                timestamp: 1,
                                groundSpeed: 1,
                                isStationary: 1,
                                latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                                longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                            },
                        },
                    ]);

                    if (isSwagger) {
                        cursor.limit(20);
                    }

                    return cursor.toArray();
                });

                const allResults = await Promise.all(resultPromises);
                const flattenedResults = allResults.flat();
                return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            });

            console.log(`/vesselLocations ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselLocations ${vesselName} received ${(Array.isArray(locations) && locations.length) || 1} coordinates`);
            console.log(`/vesselLocations ${vesselName} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_BULK),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim()))
            .custom((v: string[]) => v.every((id: string) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
        query("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    ]),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
            console.log(`/vesselLocations/bulk ${vesselIds}`, startTimestampISO, endTimestampISO);

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestamp is provided" });
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds as string[] } });

            const assignedVessels = vessels.filter((vessel: IVessel) => canAccessVessel(req, vessel));

            const query: Record<string, { $gt: Date; $lt: Date }> = {};
            if (startTimestampISO) {
                const endTime = endTimestampISO || Date.now();
                query.timestamp = { $gt: new Date(startTimestampISO as string), $lt: new Date(endTime as string) };
            }

            const locations = await limitPromise(async () => {
                // const allLocations = {};

                console.log(`/vesselLocations/bulk querying DB`);
                const ts = new Date().getTime();

                const vesselIds = assignedVessels.map((vessel: IVessel) => vessel._id.toString());
                const dateRanges = splitByMonthsUTC(startTimestampISO as string, endTimestampISO as string);
                const locations: ILocationOptimized[] = (
                    await Promise.all(
                        dateRanges.map(async (range) =>
                            vesselLocationService.findByDateRange({
                                dateRange: [range.start, range.end],
                                vesselIds,
                                projection: { _id: 1, location: 1, isStationary: 1, timestamp: 1, "metadata.onboardVesselId": 1 },
                            }),
                        ),
                    )
                ).flat() as ILocationOptimized[];

                const groupedLocations: { [vesselId: string]: [mongoose.Types.ObjectId, Date, number, number, boolean][] } = locations.reduce(
                    (acc: { [vesselId: string]: [mongoose.Types.ObjectId, Date, number, number, boolean][] }, loc: ILocationOptimized) => {
                        const vesselId = loc.metadata.onboardVesselId.toString();
                        if (!acc[vesselId]) {
                            acc[vesselId] = [];
                        }
                        acc[vesselId].push([loc._id, loc.timestamp, loc.location.coordinates[1], loc.location.coordinates[0], loc.isStationary]);
                        return acc;
                    },
                    {},
                );

                if (isSwagger) {
                    Object.keys(groupedLocations).forEach((vesselId) => {
                        groupedLocations[vesselId] = groupedLocations[vesselId].slice(0, 1);
                    });
                }

                assignedVessels.forEach((vessel: IVessel) => {
                    const vesselId = vessel._id.toString();
                    if (!groupedLocations[vesselId]) {
                        groupedLocations[vesselId] = [];
                    }
                });

                // await Promise.all(
                //     assignedVessels.map(async (vessel) => {
                //         const currentVesselId = vessel._id.toString();

                //         // const historicalUnitIds = getUnitIdsFromVessel(vessel);
                //         // if (historicalUnitIds.length === 0) {
                //         //     allLocations[currentVesselId] = [];
                //         //     return;
                //         // }

                //         // console.log(`/vesselLocations/bulk historicalUnitIds ${historicalUnitIds}`);

                //         const collections =[ db.locationsOptimized.collection(`2025-07`)];

                //         if (isClosed) return res.end();
                //         const vesselLocations = (
                //             await Promise.all(
                //                 collections.map(async (collection) => {
                //                     const ts = new Date().getTime();
                //                     const cursor = collection.find(
                //                         { ...query, 'metadata.onboardVesselId': new mongoose.Types.ObjectId(currentVesselId) },
                //                         {
                //                             projection: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 },
                //                         },
                //                     );

                //                     if (isSwagger) {
                //                         cursor.limit(20);
                //                     }

                //                     const result = (await cursor.toArray()).map(obj => ([obj._id, obj.timestamp, obj.latitude, obj.longitude, obj.isStationary]));
                //                     // const explain = await cursor.explain();
                //                     // console.log(explain);
                //                     console.log(
                //                         `/vesselLocations/bulk ${currentVesselId} time taken to query collection ${collection.name} ${new Date().getTime() - ts}`,
                //                     );
                //                     return result;
                //                 }),
                //             )
                //         )
                //             .flat()
                //             .sort((a, b) => new Date(a[1]).getTime() - new Date(b[1]).getTime());

                //         allLocations[currentVesselId] = vesselLocations;
                //     }),
                // );
                console.log(`/vesselLocations/bulk time taken to query ${new Date().getTime() - ts}`);

                return groupedLocations;
            });

            // console.log(`/vesselLocations/bulk received ${(locations && locations.length) || 1} coordinates`);
            console.log(`/vesselLocations/bulk time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/latest",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_ALL_LATEST),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim()))
            .custom((v: string[]) => v.every((id: string) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const ts = new Date().getTime();
            const { vesselIds } = req.query;
            console.log(`/vesselLocations/latest ${vesselIds}`);

            const vessels = await vesselService.find({ _id: { $in: vesselIds as string[] } });
            const assignedVessels = vessels.filter((vessel: IVessel) => canAccessVessel(req, vessel));

            const locations = await limitPromise(async () => {
                console.log(`/vesselLocations/latest querying DB`);
                const ts = new Date().getTime();

                const lastLocations = await vesselLocationService.findLastKnownLocation({
                    vesselIds: assignedVessels.map((vessel) => vessel._id),
                    projection: {
                        _id: "$last_location_id",
                        location: "$data.location",
                        timestamp: "$data.timestamp",
                        groundSpeed: "$data.groundSpeed",
                        isStationary: "$data.isStationary",
                        unitId: "$data.metadata.unitId",
                        vessel_id: 1,
                    },
                });

                console.log(`/vesselLocations/latest time taken to query ${new Date().getTime() - ts}`);
                return lastLocations;
            });

            console.log(`/vesselLocations/latest time taken to respond ${new Date().getTime() - ts}`);
            res.json(locations);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:vesselId/closest",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_CLOSEST),
    isAuthenticated,
    validateData.bind(this, [body("timestampISO").isISO8601().withMessage("timestampISO must be a valid ISO 8601 date")]),
    async (req: Request, res: Response) => {
        try {
            const { vesselId } = req.params;
            const { timestampISO } = req.body;

            const vessel: IVesselWithUserDetails | null = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel as unknown as IVessel)) {
                return res.status(403).json({ message: "Cannot access coordinates for this vessel" });
            }

            const closestLocation = await vesselLocationService.findClosestLocation({
                vesselId,
                timestampISO,
                timeWindowMs: 60000,
            });

            if (!closestLocation) {
                return res.status(404).json({ message: "No coordinate found" });
            }

            res.json(closestLocation);
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: The ground speed of the vessel in knots
 *           example: 0.023
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the location data was recorded
 *           example: "2024-09-15T22:29:20.555Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */

/**
 * @swagger
 * /vesselLocations/{vesselName}:
 *   post:
 *     summary: Fetch vessel location data. (This route is deprecated, use v2 instead)
 *     description: Fetch vessel location data for a given vessel, with optional parameters for filtering by timestamp range, excluding specific IDs, and fetching the last known location.<br/>Rate limited to 20 requests every 5 seconds
 *     tags: [Vessel Locations]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         description: Name of the vessel to fetch location data for
 *         schema:
 *           type: string
 *           example: prototype-37
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 required: false
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1748785613000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1750945613000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           text/html:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/VesselLocation'
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /vesselLocations/bulk:
 *   get:
 *     summary: Fetch vessel locations in bulk for multiple vessels
 *     description: Retrieves location coordinates for multiple vessels in a single request. Supports filtering by time range and vessel IDs. Rate limited to 10 requests every 60 seconds.
 *     tags: [Vessel Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: vesselIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of vessel IDs to fetch locations for
 *         example: "683df473073245cf0fd62be8"
 *       - in: query
 *         name: startTimestampISO
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start timestamp in ISO 8601 format for filtering locations
 *         example: "2025-07-01T00:00:00.000Z"
 *       - in: query
 *         name: endTimestampISO
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End timestamp in ISO 8601 format for filtering locations
 *         example: "2025-07-31T23:59:59.999Z"
 *     responses:
 *       200:
 *         description: Successfully retrieved vessel locations grouped by vessel
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 "683df473073245cf0fd62be8":
 *                   type: array
 *                   description: Array of location data for vessel 683df473073245cf0fd62be8
 *                   items:
 *                     type: array
 *                     description: Location data array with fixed structure [ID, timestamp, latitude, longitude, isStationary]
 *                     minItems: 5
 *                     maxItems: 5
 *                     example: ["686fc289d59e1bb750a27dc8", "2025-07-10T13:39:21.557Z", 7.9140789, -87.8643798, false]
 *       400:
 *         description: Bad request - invalid vessel IDs or timestamp format
 *       401:
 *         description: Unauthorized - authentication required
 *       403:
 *         description: Forbidden - insufficient permissions to access vessels
 *       500:
 *         description: Internal server error
 */
