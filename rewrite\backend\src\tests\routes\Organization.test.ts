import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import Organization from '../../models/Organization';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Organization', () => require('../mocks/models/organization.mock'));

describe('Organization API', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('GET /api/organizations', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 for non-authenticated request', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).get('/api/organizations');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and list organizations for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.aggregate as jest.Mock).mockResolvedValueOnce([
                        {
                            _id: '507f1f77bcf86cd799439012',
                            name: 'Test Organization',
                            domain: 'test.com',
                            user: { name: 'Test User', username: 'testuser' },
                            user_count: 5,
                        },
                    ] as never);
                    const res = await request(app).get('/api/organizations').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 and list only user organization for non-authorized user', async () => {
                    if (authMethod === 'user') {
                        const userWithoutPermission = { ...nonAuthorizedUser, organization_id: '507f1f77bcf86cd799439012' };
                        setupAuthorizedAuthMocks(authMethod, { authorized: userWithoutPermission, nonAuthorized: nonAuthorizedUser }, authToken, User, ApiKey);
                        (Organization.aggregate as jest.Mock).mockResolvedValueOnce([
                            {
                                _id: '507f1f77bcf86cd799439012',
                                name: 'User Organization',
                                domain: 'user.com',
                                user: { name: 'Test User', username: 'testuser' },
                                user_count: 1,
                            },
                        ] as never);
                        const res = await request(app).get('/api/organizations').set('Authorization', authToken);
                        expect(res.status).toBe(200);
                        expect(Array.isArray(res.body)).toBe(true);
                    } else {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (Organization.aggregate as jest.Mock).mockResolvedValueOnce([
                            {
                                _id: '507f1f77bcf86cd799439012',
                                name: 'Test Organization',
                                domain: 'test.com',
                                user: { name: 'Test User', username: 'testuser' },
                                user_count: 5,
                            },
                        ] as never);
                        const res = await request(app).get('/api/organizations').set('Authorization', authToken);
                        expect(res.status).toBe(200);
                        expect(Array.isArray(res.body)).toBe(true);
                    }
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.aggregate as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    const res = await request(app).get('/api/organizations').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/organizations/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 for non-authenticated request', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid organization ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/organizations/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 403 for non-authorized user accessing different organization', async () => {
                    if (authMethod === 'user') {
                        const userWithoutPermission = { ...nonAuthorizedUser, organization_id: '507f1f77bcf86cd799439013' };
                        setupAuthorizedAuthMocks(authMethod, { authorized: userWithoutPermission, nonAuthorized: nonAuthorizedUser }, authToken, User, ApiKey);
                        const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                        expect(res.status).toBe(403);
                    } else {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (Organization.aggregate as jest.Mock).mockResolvedValueOnce([
                            {
                                _id: '507f1f77bcf86cd799439012',
                                name: 'Test Organization',
                                domain: 'test.com',
                                user: { name: 'Test User', username: 'testuser' },
                                user_count: 5,
                            },
                        ] as never);
                        const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                        expect(res.status).toBe(200);
                        expect(res.body).toHaveProperty('_id');
                    }
                });

                it('should return 200 and organization for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.aggregate as jest.Mock).mockResolvedValueOnce([
                        {
                            _id: '507f1f77bcf86cd799439012',
                            name: 'Test Organization',
                            domain: 'test.com',
                            user: { name: 'Test User', username: 'testuser' },
                            user_count: 5,
                        },
                    ] as never);
                    const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });

                it('should return 404 for non-existent organization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.aggregate as jest.Mock).mockResolvedValueOnce([] as never);
                    const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.aggregate as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    const res = await request(app).get('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/organizations', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 for non-authenticated request', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').send({ name: 'Test Org', domain: 'test.com' });
                    expect(res.status).toBe(401);
                });

                it('should return 403 for non-authorized user', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Org', domain: 'test.com' });
                    expect(res.status).toBe(403);
                });

                it('should return 400 if name is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ domain: 'test.com' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if domain is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Org' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if name is not string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 123, domain: 'test.com' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if domain is not string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Org', domain: 123 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if organization with domain already exists', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findOne as jest.Mock).mockResolvedValueOnce({ _id: '507f1f77bcf86cd799439012', name: 'Existing Org', domain: 'test.com' } as never);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Org', domain: 'test.com' });
                    expect(res.status).toBe(400);
                });

                it('should return 201 and create organization for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (Organization.create as jest.Mock).mockResolvedValueOnce({
                        _id: '507f1f77bcf86cd799439012',
                        name: 'Test Organization',
                        domain: 'test.com',
                        created_by: authMethod === 'user' ? '507f1f77bcf86cd799439011' : undefined,
                    } as never);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Organization', domain: 'test.com' });
                    expect(res.status).toBe(201);
                    expect(res.body).toHaveProperty('message');
                    expect(res.body).toHaveProperty('organization');
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findOne as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    const res = await request(app).post('/api/organizations').set('Authorization', authToken).send({ name: 'Test Org', domain: 'test.com' });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/organizations/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 for non-authenticated request', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').send({ name: 'Updated Org' });
                    expect(res.status).toBe(401);
                });

                it('should return 403 for non-authorized user', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ name: 'Updated Org' });
                    expect(res.status).toBe(403);
                });

                it('should return 400 for invalid organization ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/organizations/invalid-id').set('Authorization', authToken).send({ name: 'Updated Org' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if name is not string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ name: 123 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if domain is not string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ domain: 123 });
                    expect(res.status).toBe(400);
                });

                it('should return 404 for non-existent organization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findByIdAndUpdate as jest.Mock).mockResolvedValueOnce(null as never);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ name: 'Updated Org' });
                    expect(res.status).toBe(404);
                });

                it('should return 200 and update organization for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findByIdAndUpdate as jest.Mock).mockResolvedValueOnce({
                        _id: '507f1f77bcf86cd799439012',
                        name: 'Updated Organization',
                        domain: 'updated.com',
                        created_by: '507f1f77bcf86cd799439011',
                    } as never);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ name: 'Updated Organization' });
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('message');
                    expect(res.body).toHaveProperty('organization');
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Organization.findByIdAndUpdate as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    const res = await request(app).patch('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken).send({ name: 'Updated Org' });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/organizations/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 for non-authenticated request', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012');
                    expect(res.status).toBe(401);
                });

                it('should return 403 for non-authorized user', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 for invalid organization ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/organizations/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if organization has users', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.countDocuments as jest.Mock).mockResolvedValueOnce(5 as never);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else {
                        expect(res.status).toBe(400);
                    }
                });

                it('should return 404 for non-existent organization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.countDocuments as jest.Mock).mockResolvedValueOnce(0 as never);
                    (Organization.findByIdAndDelete as jest.Mock).mockResolvedValueOnce(null as never);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else {
                        expect(res.status).toBe(404);
                    }
                });

                it('should return 200 and delete organization for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.countDocuments as jest.Mock).mockResolvedValueOnce(0 as never);
                    (Organization.findByIdAndDelete as jest.Mock).mockResolvedValueOnce({
                        _id: '507f1f77bcf86cd799439012',
                        name: 'Deleted Organization',
                        domain: 'deleted.com',
                    } as never);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else {
                        expect(res.status).toBe(200);
                    }
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.countDocuments as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    const res = await request(app).delete('/api/organizations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
