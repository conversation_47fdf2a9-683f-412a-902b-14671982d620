import {
    OTP_EMAIL_CONTENT,
    INVITE_EMAIL_CONTENT,
    WELCOME_EMAIL_CONTENT,
    FORGET_PASSWORD_EMAIL_CONTENT,
    ARTIFACT_NOTIFICATION_EMAIL_CONTENT,
    NOTIFICATION_SUMMARY_EMAIL_CONTENT,
    NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT,
    SUMMARY_SUBSCRIPTION_EMAIL_CONTENT,
} from '../../utils/Email';
import { describe, it, expect } from '@jest/globals';

describe('Email templates', () => {
    it('OTP_EMAIL_CONTENT returns HTML including name and OTP', () => {
        const html = OTP_EMAIL_CONTENT(123456, 'John', '2025-01-01');
        expect(html).toContain('Dear <PERSON>');
        expect(html).toContain('123456');
        expect(html).toContain('2025-01-01');
    });

    it('INVITE_EMAIL_CONTENT returns HTML including invite link and date', () => {
        const html = INVITE_EMAIL_CONTENT('https://invite', '2025-01-01');
        expect(html).toContain('https://invite');
        expect(html).toContain('2025-01-01');
    });

    it('WELCOME_EMAIL_CONTENT returns HTML with name and date', () => {
        const html = WELCOME_EMAIL_CONTENT('Alice', '2025-01-01');
        expect(html).toContain('Welcome Alice');
        expect(html).toContain('2025-01-01');
    });

    it('FORGET_PASSWORD_EMAIL_CONTENT returns HTML with reset link', () => {
        const html = FORGET_PASSWORD_EMAIL_CONTENT('https://reset', '2025-01-01');
        expect(html).toContain('https://reset');
    });

    it('ARTIFACT_NOTIFICATION_EMAIL_CONTENT returns HTML containing provided value and link', () => {
        const html = ARTIFACT_NOTIFICATION_EMAIL_CONTENT('VAL', '2025-01-01', 'https://portal');
        expect(html).toContain('VAL');
        expect(html).toContain('https://portal');
    });

    it('ARTIFACT_NOTIFICATION_EMAIL_CONTENT returns HTML containing provided value', () => {
        const html = ARTIFACT_NOTIFICATION_EMAIL_CONTENT('VAL', '2025-01-01');
        expect(html).toContain('VAL');
        expect(html).toContain('2025-01-01');
    });

    it('NOTIFICATION_SUMMARY_EMAIL_CONTENT returns HTML containing summary data', () => {
        const html = NOTIFICATION_SUMMARY_EMAIL_CONTENT({
            type: 'Daily',
            superCtg: 'Super',
            flags: 'Flags',
            highDetection: 'High',
            incursions: false,
            totalDetection: 10,
            link: 'https://portal',
            vessels: [{ name: 'Vessel A', value: 5 }]
        }, '2025-01-01');
        expect(html).toContain('Daily');
        expect(html).toContain('Super');
        expect(html).toContain('Vessel A');
        expect(html).toContain('5');
    });

    it('SUMMARY_SUBSCRIPTION_EMAIL_CONTENT returns HTML containing subscription info', () => {
        const html = SUMMARY_SUBSCRIPTION_EMAIL_CONTENT({
            addBy: 'Bob',
            vessel: 'QM-1',
            preference: 'Daily',
            link: 'https://unsub'
        }, '2025-01-01');
        expect(html).toContain('Bob');
        expect(html).toContain('QM-1');
        expect(html).toContain('Daily');
        expect(html).toContain('https://unsub');
    });

    it('NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT returns HTML containing alert subscription info', () => {
        const html = NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT({
            addBy: 'Carol',
            vessel: 'QM-2',
            super: 'Cargo',
            sub: 'Container',
            flag: 'PH',
            preference: 'Daily',
            link: 'https://unsub'
        }, '2025-01-01');
        expect(html).toContain('Carol');
        expect(html).toContain('QM-2');
        expect(html).toContain('Cargo');
        expect(html).toContain('Container');
        expect(html).toContain('PH');
        expect(html).toContain('Daily');
        expect(html).toContain('https://unsub');
    });
});


