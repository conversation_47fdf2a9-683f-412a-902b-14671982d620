import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('InAppNotification Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create InAppNotification model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/InAppNotification')];

        const InAppNotificationModule = await import('../../models/InAppNotification');
        const InAppNotification = InAppNotificationModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('InAppNotification', expect.any(Object), 'in_app_notifications');
        expect(InAppNotification).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.title).toBeDefined();
        expect(schemaArg.paths.title.type).toBe(String);
        expect(schemaArg.paths.title.required).toBe(true);

        expect(schemaArg.paths.message).toBeDefined();
        expect(schemaArg.paths.message.type).toBe(String);
        expect(schemaArg.paths.message.required).toBe(true);

        expect(schemaArg.paths.receiver).toBeDefined();
        expect(schemaArg.paths.receiver.type).toBeDefined();
        expect(schemaArg.paths.receiver.required).toBe(true);

        expect(schemaArg.paths.is_read).toBeDefined();
        expect(schemaArg.paths.is_read.type).toBe(Boolean);
        expect(schemaArg.paths.is_read.default).toBe(false);

        expect(schemaArg.paths.artifact_id).toBeDefined();
        expect(schemaArg.paths.artifact_id.type).toBeDefined();
        expect(schemaArg.paths.artifact_id.required).toBe(false);

        expect(schemaArg.paths.created_at).toBeDefined();
        expect(schemaArg.paths.created_at.type).toBe(Date);
        expect(schemaArg.paths.created_at.default).toBeDefined();

        const createdAtDefault = schemaArg.paths.created_at.default();
        expect(typeof createdAtDefault).toBe('string');
        expect(createdAtDefault).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.paths.updated_at).toBeDefined();
        expect(schemaArg.paths.updated_at.type).toBe(Date);
        expect(schemaArg.paths.updated_at.default).toBeDefined();

        const updatedAtDefault = schemaArg.paths.updated_at.default();
        expect(typeof updatedAtDefault).toBe('string');
        expect(updatedAtDefault).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});
