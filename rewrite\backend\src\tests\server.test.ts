import { describe, it, beforeEach, afterEach, expect, jest } from '@jest/globals';
import request from 'supertest';
import { start } from '../server';

jest.mock('../modules/db', () => ({
    qm: { model: jest.fn(() => ({})) },
    connect: jest.fn()
}));

jest.mock('../modules/processLogs', () => ({}));

jest.mock('../routes/index', () => {
    const express = require('express');
    const router = express.Router();
    router.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API'));
    return router;
});

jest.mock('../routes/v2/index.v2', () => {
    const express = require('express');
    const router = express.Router();
    router.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API v2'));
    return router;
});

jest.mock('../modules/swagger', () => {
    const swaggerUi = {
        serve: (_req: any, _res: any, next: any) => next(),
        setup: () => (_req: any, res: any) => res.status(200).send('swagger'),
    };
    return {
        swaggerUi,
        swaggerDocs: {},
        swaggerConfig: {},
    };
});

jest.mock('socket.io', () => {
    class FakeServer {
        constructor() { }
        use(cb: any) {
            cb({
                handshake: {
                    auth: {
                        jwt_token: 'test-token',
                        device: 'test-device',
                        browser: 'test-browser',
                        user_id: 'test-user-id',
                    },
                },
            }, jest.fn()); return this;
        }
        on(_event: string, cb: any) {
            cb({
                id: '123',
                handshake: {
                    auth: {
                        jwt_token: 'test-token',
                        device: 'test-device',
                        browser: 'test-browser',
                        user_id: 'test-user-id',
                    },
                },
                on: jest.fn((_e, h: any) => h())
            } as any); return this;
        }
        emit = jest.fn();
        get sockets() { return { sockets: new Map() }; }
        adapter() { return this; }
    }
    return { Server: FakeServer };
});

jest.mock('../models/SessionLog', () => ({
    updateOne: jest.fn().mockResolvedValue(true as never),
    create: jest.fn().mockResolvedValue(true as never),
}));

jest.mock('jsonwebtoken', () => ({
    __esModule: true,
    default: { verify: jest.fn().mockReturnValue({ user_id: 'test-user-id' }) },
}));

jest.mock('../modules/ioEmitter', () => {
    const emitter = {
        on: jest.fn((_e, h: any) => h({ name: 'test-event', data: { test: 'test-data' } })),
        emit: jest.fn(),
    };
    return { __esModule: true, default: emitter };
});

jest.mock('redis', () => ({
    createClient: jest.fn().mockReturnValue({
        on: jest.fn((_e, h: any) => h()),
        connect: jest.fn().mockResolvedValue(true as never),
        duplicate: jest.fn().mockReturnValue({
            on: jest.fn((_e, h: any) => h()),
            connect: jest.fn().mockResolvedValue(true as never),
        }),
    }),
}));

jest.mock('@socket.io/redis-adapter', () => ({
    createAdapter: jest.fn()
}));

jest.mock('../modules/notifyLog', () => ({
    postLogToSlack: jest.fn()
}));

const ORIGINAL_ENV = process.env;

describe('server.ts', () => {
    beforeEach(() => {
        jest.resetModules();
        jest.clearAllMocks();
        process.env = { ...ORIGINAL_ENV, NODE_ENV: 'test', PORT: '3333', JWT_SECRET: 'secret' };
    });

    afterEach(() => {
        process.env = ORIGINAL_ENV;
    });

    it('sets deviceId cookie and serves welcome/404 routes', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const resWelcome = await request(app).get('/api');
        expect(resWelcome.status).toBe(200);
        expect(resWelcome.headers['set-cookie']).toBeDefined();

        const res404 = await request(app).get('/api/unknown/path');
        expect(res404.status).toBe(404);
    });

    it('serves swagger docs', async () => {
        const mod = await import('../server');
        const app = mod.default;
        const res = await request(app).get('/api/docs');
        expect(res.status).toBe(200);
    });

    it('serves frontend catch-all route', async () => {
        const mod = await import('../server');
        const app = mod.default;
        const res = await request(app).get('/some-random-path');
        expect([200, 304]).toContain(res.status);
    });

    it('logs API requests without authorization', async () => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation((...args: any[]) => args[0]);
        const mod = await import('../server');
        const app = mod.default;

        await request(app).get('/api/test');
        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('[GET: /api/test] user: public')
        );

        consoleSpy.mockRestore();
    });

    it('sets deviceId cookie when not present', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app).get('/api/test');
        expect(res.headers['set-cookie']).toBeDefined();
        expect(res.headers['set-cookie']?.[0]).toContain('deviceId=');
    });

    it('does not set deviceId cookie when already present', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app)
            .get('/api/test')
            .set('Cookie', 'deviceId=existing-device-id');

        const setCookieHeader = res.headers['set-cookie'];
        if (setCookieHeader) {
            expect(setCookieHeader.includes('deviceId=')).toBe(false);
        }
    });

    it('covers frontend catch-all route with static file serving', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app).get('/non-api-route');
        expect([200, 304]).toContain(res.status);
    });

    it('covers uncaught exception handler', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation((...args: any[]) => args[0]);
        const processSpy = jest.spyOn(process, 'on').mockImplementation((...args: any[]) => args[0]);

        await import('../server');

        const uncaughtHandler = processSpy.mock.calls.find(call => call[0] === 'uncaughtException')?.[1];
        expect(uncaughtHandler).toBeDefined();

        if (uncaughtHandler) {
            const testError = new Error('test uncaught exception');
            uncaughtHandler(testError);
            expect(consoleSpy).toHaveBeenCalledWith('(FATAL ERROR) Uncaught Exception:', testError);
        }

        consoleSpy.mockRestore();
        processSpy.mockRestore();
    });

    it('should throw error if SHARED_REDIS_URL is not defined', async () => {
        process.env.SHARED_REDIS_URL = undefined;
        await expect(start()).rejects.toThrow('SHARED_REDIS_URL is not defined');
        process.env.SHARED_REDIS_URL = 'redis://localhost:6379';
    });

    it('should resolve if SHARED_REDIS_URL is defined', async () => {
        process.on = jest.fn((_e, h: any) => h());
        process.exit = jest.fn() as any;
        await expect(start()).resolves.toBe(undefined);
    });

});