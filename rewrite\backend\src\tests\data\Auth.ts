import { jest } from '@jest/globals';
import jwt from 'jsonwebtoken'
import mongoose from 'mongoose';
import { IAuthUser } from '../../interfaces/User';
import { endpointIds } from '../../utils/endpointIds';
import { permissionsList } from './Permissions';

const generateUserToken = (user_id: string) => {
    const token = jwt.sign({ user_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
    authorizedUser.jwt_tokens.push(token);
    nonAuthorizedUser['jwt_tokens'].push(token);
    return token;
}

const generateApiToken = (api_key_id: string) => {
    return jwt.sign({ api_key_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
}

const authorizedUser: IAuthUser & { save: Function } = {
    _id: '66e2e452bca74bbdb726369c',
    name: 'test-admin',
    username: 'test',
    email: '<EMAIL>',
    role_id: 1,
    deletable: false,
    creation_timestamp: new Date('2024-09-12T12:53:38.665Z'),
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
    organization_id: '66e2e452bca74bbdb726369c',
    role: {
        _id: '66cf1154ee65876e64371c9d',
        role_id: 1,
        role_name: 'Admin',
        hierarchy_number: 1,
        denied_permissions: [],
        deletable: false,
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z'),
        editable: false,
    },
    permissions: permissionsList,
    organization: {
        _id: '66e2e452bca74bbdb726369c',
        name: 'Test Organization',
        domain: 'test.com',
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z')
    },
    save: jest.fn().mockResolvedValue({} as never)
};

const nonAuthorizedUser: IAuthUser & { save: Function } = {
    _id: "66f4288532250d3bfa2e64bc",
    name: "test-user",
    username: "testuser",
    role_id: 2,
    deletable: true,
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    creation_timestamp: new Date("2024-09-25T15:13:09.826Z"),
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
    organization_id: "66e2e452bca74bbdb726369c",
    role: {
        _id: "66cf1178ee65876e64371c9e",
        role_id: 2,
        role_name: "User",
        hierarchy_number: 1,
        denied_permissions: [
            100,
            200,
            300,
            400,
            500,
            600
        ],
        deletable: false,
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z"),
        editable: false
    },
    permissions: [],
    organization: {
        _id: "66e2e452bca74bbdb726369c",
        name: "Test Organization",
        domain: "test.com",
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z")
    },
    save: jest.fn().mockResolvedValue({} as never)
}

const authorizedApiKey = {
    "_id": "66f2c43345fbceb6fc036b34",
    "description": "This is a test key",
    "allowed_endpoints": Object.values(endpointIds),
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "d21e1a57f2de39b3f4fbd42cf871d9bc",
    "__v": 18,
    "requests": 140,
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
    "creation_timestamp": {
        "$date": "2024-09-24T13:52:51.178Z"
    },
    "save": jest.fn().mockResolvedValue({} as never),
    "markModified": jest.fn(),
    "requests_endpoints": {},
    "toObject": () => ({
        _id: "66f2c43345fbceb6fc036b34",
        description: "This is a test key",
        allowed_endpoints: Object.values(endpointIds),
        is_deleted: false,
        is_revoked: false,
        api_key: "d21e1a57f2de39b3f4fbd42cf871d9bc",
        __v: 18,
        requests: 140,
        jwt_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
        creation_timestamp: {
            "$date": "2024-09-24T13:52:51.178Z"
        },
        save: jest.fn().mockResolvedValue({} as never),
        markModified: jest.fn(),
        requests_endpoints: {},
        toObject: jest.fn().mockReturnValue({})
    }) as any
}

const nonAuthorizedApiKey = {
    "_id": "66f2c6e945fbceb6fc036b90",
    "description": "Test key 2",
    "allowed_endpoints": [],
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "079fc9e755a8245654c1c768787ee24c",
    "__v": 15,
    "requests": 0,
    "jwt_token": null,
    "creation_timestamp": {
        "$date": "2024-09-24T14:04:25.153Z"
    },
    "save": jest.fn().mockResolvedValue({} as never),
    "markModified": jest.fn(),
    "toObject": () => ({
        _id: "66f2c6e945fbceb6fc036b90",
        description: "Test key 2",
        allowed_endpoints: [],
        is_deleted: false,
        is_revoked: false,
        api_key: "079fc9e755a8245654c1c768787ee24c",
        __v: 15,
        requests: 0,
        jwt_token: null,
        creation_timestamp: {
            "$date": "2024-09-24T14:04:25.153Z"
        },
        save: jest.fn().mockResolvedValue({} as never),
        markModified: jest.fn(),
        requests_endpoints: {},
        toObject: jest.fn().mockReturnValue({})
    }) as any
}

const reqAuthorizedUser = {
    user: authorizedUser,
} as any;

const reqNonAuthorizedUser = {
    user: nonAuthorizedUser,
} as any;

export {
    generateUserToken,
    generateApiToken,
    authorizedUser,
    nonAuthorizedUser,
    authorizedApiKey,
    nonAuthorizedApiKey,
    reqAuthorizedUser,
    reqNonAuthorizedUser,
}