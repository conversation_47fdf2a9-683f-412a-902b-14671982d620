import { jest } from '@jest/globals';

const SessionLog = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    deleteOne: jest.fn(),
    deleteMany: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
    distinct: jest.fn(),
};

export default SessionLog;
