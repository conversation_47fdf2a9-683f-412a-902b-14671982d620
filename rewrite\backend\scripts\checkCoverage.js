/*
 Checks average coverage across Statements, Branches, Functions, Lines >= 95%.
 Exits with non-zero code if threshold not met.
*/
const fs = require("fs");
const path = require("path");

const COVERAGE_FILE = path.join(__dirname, "..", "coverage", "coverage-summary.json");
const REQUIRED_AVG = 95;

function fail(message) {
    console.error(message);
    process.exit(1);
}

try {
    if (!fs.existsSync(COVERAGE_FILE)) {
        fail(`Coverage file not found at ${COVERAGE_FILE}. Run tests with coverage first.`);
    }
    const summary = JSON.parse(fs.readFileSync(COVERAGE_FILE, "utf-8"));
    const total = summary.total;
    if (!total) {
        fail("Invalid coverage summary format: missing total.");
    }

    const metrics = ["statements", "branches", "functions", "lines"];
    const percents = metrics.map((m) => {
        const metric = total[m];
        if (!metric || typeof metric.pct !== "number") {
            fail(`Invalid coverage summary: missing pct for ${m}`);
        }
        return metric.pct;
    });

    const average = percents.reduce((a, b) => a + b, 0) / percents.length;

    if (average < REQUIRED_AVG) {
        fail(
            `Average coverage ${average.toFixed(2)}% is below required ${REQUIRED_AVG}%. ` +
                `Details -> S:${percents[0]} B:${percents[1]} F:${percents[2]} L:${percents[3]}`,
        );
    }

    console.log(`Coverage OK. Average ${average.toFixed(2)}% (S:${percents[0]} B:${percents[1]} F:${percents[2]} L:${percents[3]})`);
} catch (err) {
    fail(`Error reading coverage summary: ${err.message}`);
}
