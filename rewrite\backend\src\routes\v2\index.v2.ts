import express from "express";
import userRouter from "./User.route";
import artifactRouter from "./Artifact.route";
import notificationSummaryRouter from "./NotificationSummary.route";
import kinesisRouter from "./Kinesis.route";
import vesselLocationRouter from "./VesselLocation.route";

const router = express.Router();

router.use("/users", userRouter);
router.use("/artifacts", artifactRouter);
router.use("/summaryReports", notificationSummaryRouter);
router.use("/kinesis", kinesisRouter);
router.use("/vesselLocations", vesselLocationRouter);

export default router;
