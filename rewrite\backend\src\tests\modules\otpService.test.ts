import { jest, beforeEach, afterEach, describe, it, expect } from '@jest/globals';
import { sendOtp, verifyOtp, cleanupExpiredOtps, startCleanupTimeout, stopCleanupTimeout, otpStore, cleanupTimeout } from '../../modules/otpService';

describe('OTP Service Module', () => {
    const email = '<EMAIL>';
    const name = 'Test User';
    let sendEmail: jest.Mock;

    beforeEach(async () => {
        jest.clearAllMocks();
        sendEmail = jest.fn().mockResolvedValue(undefined as never);
    });

    afterEach(() => {
        jest.clearAllMocks();
        // Clear Redis store after each test
        if ((global as any).__redisTestStore) {
            (global as any).__redisTestStore.clear();
        }
    });

    describe('OTP Generation and Sending', () => {
        it('should successfully generate and send OTP via email', async () => {
            const response = await sendOtp(email, name, sendEmail as any);

            expect(response).toEqual({ message: 'OTP sent successfully' });
            expect(sendEmail).toHaveBeenCalledWith({
                to: email,
                subject: 'Your OTP Code',
                html: expect.stringContaining('OTP'),
            });
            
            // Verify OTP was stored in Redis
            const store = (global as any).__redisTestStore;
            const otpKey = `otp:${email}`;
            const storedEntry = store?.get(otpKey);
            expect(storedEntry).toBeDefined();
            expect(storedEntry?.value).toBeDefined();
        });

        it('should handle email sending failures during OTP delivery', async () => {
            sendEmail.mockRejectedValueOnce(new Error('Failed to send OTP') as never);

            await expect(sendOtp(email, name, sendEmail as any)).rejects.toThrow('Failed to send OTP');
            
            // Verify that OTP was removed from Redis after email failure
            // The catch block calls client.del(otpKey).catch(() => {}), so we need to ensure that's covered
            const result = await verifyOtp(email, 123456);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should handle Redis deletion errors gracefully when email fails', async () => {
            // Mock sendEmail to fail
            sendEmail.mockRejectedValueOnce(new Error('Failed to send OTP') as never);
            
            // Mock Redis client.del to fail (to test the .catch(() => {}) handler)
            const { getSharedRedisClient } = await import('../../modules/sharedRedis');
            const client = await getSharedRedisClient();
            const originalDel = client.del;
            (client as any).del = jest.fn().mockRejectedValueOnce(new Error('Redis del error'));
            
            // Should still throw the email error, not the Redis error
            await expect(sendOtp(email, name, sendEmail as any)).rejects.toThrow('Failed to send OTP');
            
            // Restore original del method
            (client as any).del = originalDel;
        });

        it('should generate OTP codes for multiple requests and overwrite previous OTPs', async () => {
            const firstResponse = await sendOtp(email, name, sendEmail as any);
            const secondResponse = await sendOtp(email, name, sendEmail as any);

            // Both should succeed
            expect(firstResponse).toEqual({ message: 'OTP sent successfully' });
            expect(secondResponse).toEqual({ message: 'OTP sent successfully' });
            
            // Get OTPs from Redis store (more reliable than parsing email HTML)
            const store = (global as any).__redisTestStore;
            const otpKey = `otp:${email}`;
            
            // After second OTP, only the second one should be in store
            const storedEntry = store?.get(otpKey);
            expect(storedEntry).toBeDefined();
            const secondOtp = parseInt(storedEntry.value, 10);
            
            // Get first OTP from first email call
            const firstEmailCall = sendEmail.mock.calls[0][0] as { html: string };
            // Find the OTP in the email - it should be in a specific format
            // The OTP appears as ${opt} in the template, so we need to find it
            const firstOtpMatch = firstEmailCall.html.match(/<strong>(\d{6})<\/strong>/);
            expect(firstOtpMatch).toBeTruthy();
            const firstOtp = parseInt(firstOtpMatch![1], 10);
            
            // The second OTP should overwrite the first one in Redis (same email)
            // So only the latest OTP should work
            // Check first OTP before verifying second (since second will delete it)
            const firstOtpResult = await verifyOtp(email, firstOtp);
            // First OTP should fail because it was overwritten by the second one
            expect(firstOtpResult.valid).toBe(false);
            
            // Verify the second OTP works (it's the latest and should be in Redis)
            const secondOtpResult = await verifyOtp(email, secondOtp);
            expect(secondOtpResult).toEqual({ valid: true });
        });
    });

    describe('OTP Verification Process', () => {
        it('should successfully verify valid OTP codes', async () => {
            // Send OTP first
            await sendOtp(email, name, sendEmail as any);

            // Get the OTP from Redis store (more reliable than parsing email HTML)
            const store = (global as any).__redisTestStore;
            const otpKey = `otp:${email}`;
            const storedEntry = store?.get(otpKey);
            expect(storedEntry).toBeDefined();
            expect(storedEntry?.value).toBeDefined();
            const otp = parseInt(storedEntry.value, 10);
            
            // Verify OTP is a valid 6-digit number
            expect(otp).toBeGreaterThanOrEqual(100000);
            expect(otp).toBeLessThanOrEqual(999999);

            // Verify the OTP
            const result = await verifyOtp(email, otp);
            expect(result).toEqual({ valid: true });
        });

        it('should reject invalid OTP codes with appropriate error message', async () => {
            // Send OTP first
            await sendOtp(email, name, sendEmail as any);

            // Try to verify with wrong OTP
            const result = await verifyOtp(email, 999999);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should reject expired OTP codes', async () => {
            // This test verifies that expired OTPs are rejected
            // Since Redis handles TTL automatically, we test by waiting for expiration
            // or by directly testing that non-existent OTPs are rejected
            const result = await verifyOtp(email, 123456);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should handle verification for non-existent email addresses', async () => {
            const result = await verifyOtp('<EMAIL>', 100000);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should delete OTP after successful verification (one-time use)', async () => {
            // Send OTP first
            await sendOtp(email, name, sendEmail as any);

            // Get the OTP from Redis store (more reliable than parsing email HTML)
            const store = (global as any).__redisTestStore;
            const otpKey = `otp:${email}`;
            const storedEntry = store?.get(otpKey);
            expect(storedEntry).toBeDefined();
            const otp = parseInt(storedEntry.value, 10);
            
            // Ensure OTP is a valid 6-digit number
            expect(otp).toBeGreaterThanOrEqual(100000);
            expect(otp).toBeLessThanOrEqual(999999);

            // Verify the OTP first time - should succeed
            const firstResult = await verifyOtp(email, otp);
            expect(firstResult).toEqual({ valid: true });

            // Try to verify the same OTP again - should fail (already used/deleted)
            const secondResult = await verifyOtp(email, otp);
            expect(secondResult).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });
    });

    describe('Legacy Functions (Backward Compatibility)', () => {
        it('should have cleanupExpiredOtps as a no-op function', () => {
            // These are legacy functions kept for backward compatibility
            // They should not throw and should be callable
            expect(() => cleanupExpiredOtps()).not.toThrow();
        });

        it('should have startCleanupTimeout as a no-op function', () => {
            expect(() => startCleanupTimeout()).not.toThrow();
        });

        it('should have stopCleanupTimeout as a no-op function', () => {
            expect(() => stopCleanupTimeout()).not.toThrow();
        });

        it('should export otpStore as an empty array for backward compatibility', () => {
            expect(otpStore).toEqual([]);
            expect(Array.isArray(otpStore)).toBe(true);
        });

        it('should export cleanupTimeout as null for backward compatibility', () => {
            expect(cleanupTimeout).toBeNull();
        });
    });
});
