import { expect, jest } from '@jest/globals';

const mockJest = jest as any;

export const createFsMock = () => ({
    readFileSync: mockJest.fn(),
    readFile: mockJest.fn(),
    writeFileSync: mockJest.fn(),
    writeFile: mockJest.fn(),
    existsSync: mockJest.fn(),
    mkdirSync: mockJest.fn(),
    rmdirSync: mockJest.fn(),
    unlinkSync: mockJest.fn(),
    statSync: mockJest.fn(),
    readdirSync: mockJest.fn(),
    copyFileSync: mockJest.fn(),
    renameSync: mockJest.fn(),
    access: mockJest.fn(),
    constants: {
        F_OK: 0,
        R_OK: 4,
        W_OK: 2,
        X_OK: 1
    }
});

export const setupFsScenarios = () => {
    const mockFs = createFsMock();
    
    return {
        mockFs,
        fileReadSuccess: (filePath: string, content: string, encoding = 'utf-8') => {
            mockFs.readFileSync.mockReturnValue(content);
            mockFs.readFile.mockResolvedValue(content);
            return { filePath, content, encoding };
        },
        fileReadFailure: (filePath: string, errorMessage = 'File not found') => {
            const error = new Error(errorMessage);
            mockFs.readFileSync.mockImplementation(() => {
                throw error;
            });
            mockFs.readFile.mockRejectedValue(error);
            return { filePath, error };
        },
        fileWriteSuccess: (filePath: string, content: string, encoding = 'utf-8') => {
            mockFs.writeFileSync.mockReturnValue(undefined);
            mockFs.writeFile.mockResolvedValue(undefined);
            return { filePath, content, encoding };
        },
        fileWriteFailure: (filePath: string, errorMessage = 'Write failed') => {
            const error = new Error(errorMessage);
            mockFs.writeFileSync.mockImplementation(() => {
                throw error;
            });
            mockFs.writeFile.mockRejectedValue(error);
            return { filePath, error };
        },
        fileExists: (filePath: string, exists = true) => {
            mockFs.existsSync.mockReturnValue(exists);
            return { filePath, exists };
        },
        directoryCreation: (dirPath: string, recursive = false) => {
            mockFs.mkdirSync.mockReturnValue(undefined);
            return { dirPath, recursive };
        },
        fileDeletion: (filePath: string) => {
            mockFs.unlinkSync.mockReturnValue(undefined);
            return { filePath };
        },
        directoryListing: (dirPath: string, files: string[] = []) => {
            mockFs.readdirSync.mockReturnValue(files);
            return { dirPath, files };
        }
    };
};

export const createFsTestHelpers = () => ({
    createTestFileContent: (type: 'text' | 'json' | 'binary' = 'text', size = 100) => {
        switch (type) {
            case 'json':
                return JSON.stringify({ test: 'data', timestamp: Date.now() });
            case 'binary':
                return Buffer.alloc(size, 'test-data');
            default:
                return 'test file content '.repeat(Math.ceil(size / 18));
        }
    },
    createTestFilePath: (filename = 'test.txt', directory = './test') => 
        `${directory}/${filename}`,
    createTestDirectory: (name = 'test-dir', parent = './') => 
        `${parent}${name}`,
    createFileStats: (isFile = true, size = 1024) => ({
        isFile: () => isFile,
        isDirectory: () => !isFile,
        size,
        mtime: new Date(),
        atime: new Date(),
        ctime: new Date(),
        birthtime: new Date()
    })
});

export const setupFsTest = () => {
    const mockFs = createFsMock();
    const scenarios = setupFsScenarios();
    const helpers = createFsTestHelpers();
    
    return {
        mockFs,
        scenarios,
        helpers
    };
};

export const expectFileRead = (mockFs: any, filePath: string, encoding = 'utf-8') => {
    expect(mockFs.readFileSync).toHaveBeenCalledWith(filePath, encoding);
};

export const expectFileWritten = (mockFs: any, filePath: string, content: string, encoding = 'utf-8') => {
    expect(mockFs.writeFileSync).toHaveBeenCalledWith(filePath, content, encoding);
};

export const expectFileExists = (mockFs: any, filePath: string) => {
    expect(mockFs.existsSync).toHaveBeenCalledWith(filePath);
};

export const expectDirectoryCreated = (mockFs: any, dirPath: string, options?: any) => {
    expect(mockFs.mkdirSync).toHaveBeenCalledWith(dirPath, options);
};

export const expectFileDeleted = (mockFs: any, filePath: string) => {
    expect(mockFs.unlinkSync).toHaveBeenCalledWith(filePath);
};

export const cleanupFsTest = () => {
    jest.clearAllMocks();
};
