import { afterAll, afterEach, jest } from "@jest/globals";
import { NextFunction, Request, Response } from "express";

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.MONGO_URI = 'mongodb://localhost:27017/test';
process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';
process.env.AWS_REGION = 'us-east-1';
process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test-webhook-url';
process.env.SHARED_REDIS_URL = 'redis://localhost:6379';

// Silence console globally during tests (including during module imports)
const originalConsole = global.console;
global.console = {
    ...originalConsole,
    log: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
} as any;

// mock intervals
// global.setInterval = jest.fn((callback: () => void, interval: number) => callback()) as any;
// global.setTimeout = jest.fn((callback: () => void, timeout: number) => callback()) as any;
// global.clearInterval = jest.fn((interval: NodeJS.Timeout) => clearInterval(interval)) as any;
// global.clearTimeout = jest.fn((timeout: NodeJS.Timeout) => clearTimeout(timeout)) as any;
// global.setImmediate = jest.fn((callback: () => void) => callback()) as any;
// global.clearImmediate = jest.fn((immediate: NodeJS.Immediate) => clearImmediate(immediate)) as any;

const mockJest = jest.fn() as any;
// Mock external modules that cause memory issues
jest.mock('express-rate-limit', () => jest.fn(() => (_req: Request, _res: Response, next: NextFunction) => next()));

// Mock heavy AWS modules to reduce memory usage
jest.mock('@aws-sdk/client-kinesis-video', () => ({
    KinesisVideoClient: jest.fn(),
}));

jest.mock('@aws-sdk/client-kinesis-video-archived-media', () => ({
    KinesisVideoArchivedMediaClient: jest.fn(),
}));

jest.mock('@aws-sdk/client-cloudfront', () => ({
    CloudFrontClient: jest.fn(),
}));

// Mock AWS SDK v2
jest.mock('aws-sdk', () => ({
    S3: jest.fn(() => ({
        upload: jest.fn().mockReturnValue({
            promise: mockJest.mockResolvedValue({ Location: 'test-location' })
        }),
        deleteObject: jest.fn().mockReturnValue({
            promise: mockJest.mockResolvedValue({})
        }),
        getSignedUrl: jest.fn().mockReturnValue('test-signed-url')
    })),
    config: {
        update: jest.fn()
    }
}));

// Mock Google Maps
jest.mock('@googlemaps/google-maps-services-js', () => ({
    Client: jest.fn(() => ({
        geocode: mockJest.mockResolvedValue({
            data: {
                results: [{
                    formatted_address: 'Test Address',
                    plus_code: { global_code: 'TEST123' }
                }]
            }
        })
    }))
}));

// Mock Mongoose to prevent actual database connections
const DEFAULT_OBJECT_ID = '507f1f77bcf86cd799439011';
const mockObjectId: any = function(this: any, id?: any) {
    if (!(this instanceof mockObjectId)) {
        return new (mockObjectId as any)(id);
    }
    const value = typeof id === 'string' && id.length ? id : DEFAULT_OBJECT_ID;
    this._id = value;
};
mockObjectId.isValid = (id: any) => typeof id === 'string' && /^[a-f\d]{24}$/i.test(id);
mockObjectId.prototype.toString = function() { return this._id; };
mockObjectId.prototype.toHexString = function() { return this._id; };
mockObjectId.prototype.valueOf = function() { return this._id; };
mockObjectId.prototype.equals = function(other: any) {
    const otherStr = other && typeof other.toString === 'function' ? other.toString() : other;
    return otherStr === this._id;
};
const mockIsValidObjectId = (id: any) => mockObjectId.isValid(id);
const mockSchema: any = jest.fn(() => ({
    pre: jest.fn(),
    post: jest.fn(),
    index: jest.fn(),
    methods: {},
    statics: {},
}));

// Add Types property to the mock schema
mockSchema.Types = {
    ObjectId: mockObjectId,
    String: String,
    Number: Number,
    Boolean: Boolean,
    Array: Array,
    Date: Date,
};

// Mock mongoose connection
const mockConnection = {
    on: jest.fn(),
    once: jest.fn(),
    close: jest.fn(),
    readyState: 1
};

jest.mock('mongoose', () => ({
    connect: mockJest.mockResolvedValue(mockConnection),
    connection: mockConnection,
    createConnection: jest.fn().mockReturnValue(mockConnection),
    set: jest.fn(),
    isValidObjectId: mockIsValidObjectId,
    Schema: mockSchema,
    model: mockJest.mockImplementation((_name: string, schema: any) => {
        const MockModel = function(this: any, data: any) {
            Object.assign(this, data);
        };
        MockModel.prototype = {};
        MockModel.find = jest.fn();
        MockModel.findOne = jest.fn();
        MockModel.findById = jest.fn();
        MockModel.findOneAndDelete = jest.fn();
        MockModel.findOneAndUpdate = jest.fn();
        MockModel.create = jest.fn();
        MockModel.updateOne = jest.fn();
        MockModel.deleteOne = jest.fn();
        MockModel.aggregate = jest.fn();
        MockModel.schema = schema;
        return MockModel;
    }),
    Types: {
        ObjectId: mockObjectId,
    },
    default: {
        connect: mockJest.mockResolvedValue(mockConnection),
        connection: mockConnection,
        createConnection: jest.fn().mockReturnValue(mockConnection),
        set: jest.fn(),
        isValidObjectId: mockIsValidObjectId,
        Schema: mockSchema,
        model: mockJest.mockImplementation((_name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.prototype = {};
            MockModel.find = jest.fn();
            MockModel.findOne = jest.fn();
            MockModel.findById = jest.fn();
            MockModel.findOneAndDelete = jest.fn();
            MockModel.findOneAndUpdate = jest.fn();
            MockModel.create = jest.fn();
            MockModel.updateOne = jest.fn();
            MockModel.deleteOne = jest.fn();
            MockModel.aggregate = jest.fn();
            MockModel.schema = schema;
            return MockModel;
        }),
        Types: {
            ObjectId: mockObjectId,
        },
    }
}));


// Mock JWT
jest.mock('jsonwebtoken', () => ({
    sign: jest.fn().mockReturnValue('mockedToken'),
    verify: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    decode: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    JsonWebTokenError: class JsonWebTokenError extends Error {
        constructor(message: string) {
            super(message);
            this.name = 'JsonWebTokenError';
        }
    }
}));

// Mock nodemailer
jest.mock('nodemailer', () => ({
    createTransport: jest.fn().mockReturnValue({
        sendMail: mockJest.mockResolvedValue({ messageId: 'test-message-id' })
    })
}));

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
    hash: mockJest.mockResolvedValue('hashedPassword'),
    compare: mockJest.mockResolvedValue(true),
    genSalt: mockJest.mockResolvedValue('salt')
}));

// Mock socket.io
jest.mock('socket.io-client', () => ({
    io: jest.fn(() => ({
        connect: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        once: jest.fn(),
        removeAllListeners: jest.fn(),
        connected: true,
        disconnected: false,
        id: 'mock-socket-id',
        io: {
            uri: 'http://localhost:3001',
            opts: {
                autoConnect: true,
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionAttempts: 5,
                timeout: 20000
            }
        }
    }))
}));

// Mock Redis with in-memory store for testing
// Create a shared store that persists across all client instances
const createRedisMock = () => {
    // Store is created in the closure and shared across all clients
    const store = new Map<string, { value: string; expiresAt?: number }>();
    
    return {
        createClient: jest.fn(() => {
            const client: any = {
                isOpen: false,
                connect: jest.fn(async () => {
                    client.isOpen = true;
                    return Promise.resolve();
                }),
                disconnect: mockJest.mockResolvedValue(undefined),
                quit: mockJest.mockResolvedValue(undefined),
                on: jest.fn(),
                setEx: async (key: string, seconds: number, value: string) => {
                    const expiresAt = Date.now() + seconds * 1000;
                    store.set(key, { value, expiresAt });
                    return 'OK';
                },
                get: async (key: string) => {
                    const entry = store.get(key);
                    if (!entry) {
                        return null;
                    }
                    // Check if expired
                    if (entry.expiresAt && entry.expiresAt < Date.now()) {
                        store.delete(key);
                        return null;
                    }
                    return entry.value;
                },
                del: async (key: string) => {
                    store.delete(key);
                    return 1;
                },
                duplicate: jest.fn(() => ({
                    connect: mockJest.mockResolvedValue(undefined),
                    disconnect: mockJest.mockResolvedValue(undefined),
                    quit: mockJest.mockResolvedValue(undefined),
                    on: jest.fn(),
                }))
            };
            return client;
        }),
        // Expose store for clearing in tests
        __getStore: () => store
    };
};

const redisMock = createRedisMock();
(global as any).__redisTestStore = redisMock.__getStore();

// Mock Redis
jest.mock('redis', () => redisMock);

// Mock socket.io redis adapter
jest.mock('@socket.io/redis-adapter', () => ({
    createAdapter: jest.fn(() => ({}))
}));

// Global cleanup after each test
afterEach(() => {
    // Clear Redis store between tests
    if ((global as any).__redisTestStore) {
        (global as any).__redisTestStore.clear();
    }
    // Restore all mocks
    jest.restoreAllMocks();

    // Clear all timers
    jest.clearAllTimers();

    // Force garbage collection if available
    if (global.gc) {
        global.gc();
    }
});

// Global cleanup after all tests
afterAll(() => {
    // Final cleanup
    jest.restoreAllMocks();
    jest.clearAllTimers();
});