import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('Geolocation Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create Geolocation model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Geolocation')];

        const GeolocationModule = await import('../../models/Geolocation');
        const Geolocation = GeolocationModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Geolocation', expect.any(Object), 'geolocations');
        expect(Geolocation).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.location).toBeDefined();
        expect(schemaArg.paths.location.type).toBeDefined();
        expect(schemaArg.paths.location.type.type).toBe(String);
        expect(schemaArg.paths.location.type.enum).toEqual(["Point"]);
        expect(schemaArg.paths.location.type.default).toBe("Point");
        expect(schemaArg.paths.location.coordinates).toBeDefined();
        expect(schemaArg.paths.location.coordinates.type).toEqual([Number]);
        expect(schemaArg.paths.location.coordinates.required).toBe(true);

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.index).toHaveBeenCalledWith({ location: "2dsphere" });
    });
});
