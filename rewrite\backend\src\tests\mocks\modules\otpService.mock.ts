import { jest } from '@jest/globals';

export const otpStore: Array<{ email: string; otp: number; expiresAt: number }> = [];

export const sendOtp = (jest.fn() as any).mockImplementation(async (email: string) => {
    const otp = 123456;
    const expiresAt = Date.now() + 10 * 60 * 1000;
    otpStore.push({ email, otp, expiresAt });
    return { message: 'OTP sent successfully' };
});

export const verifyOtp = (jest.fn() as any).mockImplementation(async (email: string, otp: number) => {
    const idx = otpStore.findIndex(e => e.email === email && e.otp === otp);
    if (idx === -1) return { valid: false, message: 'Invalid OTP or email' };
    const entry = otpStore[idx];
    if (entry.expiresAt < Date.now()) {
        otpStore.splice(idx, 1);
        return { valid: false, message: '<PERSON>TP expired' };
    }
    otpStore.splice(idx, 1);
    return { valid: true };
});

export default { sendOtp, verifyOtp, otpStore };
