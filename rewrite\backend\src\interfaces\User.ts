import mongoose from "mongoose";
import { IPermission } from "./Permission";
import { IRole } from "./Role";
import { IOrganization } from "./Organization";

// Common fields
export interface IBaseUser {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    email?: string;
    username?: string;
    date_time_format?: string;
    jwt_tokens: string[];
    email_verification_enabled: boolean;
    email_verified_device_ids: string[];
    role_id: number;
    deletable: boolean;
    is_deleted: boolean;
    allowed_vessels: string[];
    created_by: mongoose.Types.ObjectId;
    creation_timestamp: Date;
    organization_id: mongoose.Types.ObjectId | string;
}

// Full User
export interface IUser extends IBaseUser {
    password: string;
    reset_password_token: string | null;
    reset_password_expire: number | null;
    use_MGRS?: boolean;
    home_port_filter_mode: "ALL" | "ONLY_HOME_PORTS" | "ONLY_NON_HOME_PORTS";
}

// Authenticated User
export interface IAuthUser extends IBaseUser {
    permissions: IPermission[];
    role: IRole;
    organization: IOrganization;
}

export interface IJwtPayload {
    email: string;
    role_id: number;
    role?: string;
    organization_id: string;
    organization_name?: string;
    allowed_vessels?: string[];
    admin_id?: string;
    timestamp?: number;
}
