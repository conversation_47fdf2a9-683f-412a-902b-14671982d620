import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext, expectNextCalled, expectNextNotCalled } from '../mocks/middlewares/auth.mock';
import { validateData } from '../../middlewares/validator';
import { validationResult } from 'express-validator';
import { NextFunction } from 'express';

jest.mock('express-validator', () => ({
    validationResult: jest.fn(),
    query: jest.fn(),
    body: jest.fn()
}));

describe('validateData middleware', () => {
    let req: any;
    let res: any;
    let next: NextFunction;
    let mockValidationResult: any;

    beforeEach(() => {
        req = createMockRequest();
        res = createMockResponse();
        next = createMockNext();
        
        mockValidationResult = {
            isEmpty: jest.fn(),
            array: jest.fn()
        };
        
        (validationResult as any).mockReturnValue(mockValidationResult);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should call next if there are no validation errors', async () => {
        mockValidationResult.isEmpty.mockReturnValue(true);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(mockRule.run).toHaveBeenCalledWith(req);
        expectNextCalled(next as any);
        expect(res.status).not.toHaveBeenCalled();
        expect(res.json).not.toHaveBeenCalled();
    });

    it('should respond with 400 if there are validation errors', async () => {
        mockValidationResult.isEmpty.mockReturnValue(false);
        mockValidationResult.array.mockReturnValue([
            { msg: 'streamName is required' }
        ]);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(mockRule.run).toHaveBeenCalledWith(req);
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({ message: 'streamName is required' });
        expectNextNotCalled(next as any);
    });

    it('should handle multiple validation rules', async () => {
        mockValidationResult.isEmpty.mockReturnValue(true);
        
        const mockRule1 = {
            run: (jest.fn() as any).mockResolvedValue(undefined)
        };
        const mockRule2 = {
            run: (jest.fn() as any).mockResolvedValue(undefined)
        };
        
        const rules = [mockRule1, mockRule2 as any];

        await validateData(rules, req, res, next);

        expect(mockRule1.run).toHaveBeenCalledWith(req);
        expect(mockRule2.run).toHaveBeenCalledWith(req);
        expectNextCalled(next as any);
    });

    it('should handle validation rule that throws error', async () => {
        const mockRule = {
            run: jest.fn().mockRejectedValue(new Error('Validation rule error') as never)
        };
        
        const rules = [mockRule as any];

        await expect(validateData(rules, req, res, next)).rejects.toThrow('Validation rule error');
    });

    it('should handle empty validation rules array', async () => {
        mockValidationResult.isEmpty.mockReturnValue(true);
        
        const rules: any[] = [];

        await validateData(rules, req, res, next);

        expectNextCalled(next as any);
    });

    it('should handle validation errors with multiple error messages', async () => {
        mockValidationResult.isEmpty.mockReturnValue(false);
        mockValidationResult.array.mockReturnValue([
            { msg: 'First error' },
            { msg: 'Second error' }
        ]);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({ message: 'First error' });
        expectNextNotCalled(next as any);
    });

    it('should handle validation errors with empty message', async () => {
        mockValidationResult.isEmpty.mockReturnValue(false);
        mockValidationResult.array.mockReturnValue([
            { msg: '' }
        ]);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({ message: '' });
        expectNextNotCalled(next as any);
    });

    it('should handle validation errors with undefined message', async () => {
        mockValidationResult.isEmpty.mockReturnValue(false);
        mockValidationResult.array.mockReturnValue([
            { msg: undefined }
        ]);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({ message: undefined });
        expectNextNotCalled(next as any);
    });

    it('should handle validation errors with null message', async () => {
        mockValidationResult.isEmpty.mockReturnValue(false);
        mockValidationResult.array.mockReturnValue([
            { msg: null }
        ]);
        
        const mockRule = {
            run: jest.fn().mockResolvedValue(undefined as never)
        };
        
        const rules = [mockRule as any];

        await validateData(rules, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({ message: null });
        expectNextNotCalled(next as any);
    });
});
