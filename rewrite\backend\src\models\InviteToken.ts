import { IInviteToken } from "src/interfaces/InviteToken";
import db from "../modules/db";
import mongoose, { Schema as scheme } from "mongoose";

const inviteTokenSchema = new scheme(
    {
        token: {
            type: String,
            required: true,
        },
        invited_by: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            required: true,
        },
        email: {
            type: String,
            required: true,
        },
        organization_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Organization",
        },
        role_id: {
            type: Number,
            required: true,
        },
        role: {
            type: String,
            required: true,
        },
        allowed_vessels: {
            type: [mongoose.Schema.Types.ObjectId],
            default: [],
        },
        short_token: {
            type: String,
            required: true,
            unique: true,
        },
        is_used: {
            type: Boolean,
            default: false,
        },
        is_deleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);

const InviteToken = db.qm.model<IInviteToken>("InviteToken", inviteTokenSchema, "invite_tokens");

export default InviteToken;
