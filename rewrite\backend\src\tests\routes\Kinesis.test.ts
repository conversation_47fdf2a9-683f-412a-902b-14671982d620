import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { streamsList, regionsList, vesselsList, streamData, streamTags, screenshotData, streamingUrl } from '../data/Kinesis';
import awsKinesis from '../../modules/awsKinesis';
import Region from '../../models/Region';
import vesselService from '../../services/Vessel.service';
import vesselLocationService from '../../services/VesselLocation.service';
import streamService from '../../services/Stream.service';
import { validateError, canAccessVessel, fileNameTimestamp, generateZip, removeSpecialCharsFromFilename, userHasPermissions, getLocationsCollections } from '../../utils/functions';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/awsKinesis', () => require('../mocks/modules/awsKinesis.mock'));
jest.mock('../../models/Region', () => require('../mocks/models/region.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../services/VesselLocation.service');
jest.mock('../../services/Stream.service', () => require('../mocks/services/stream.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Kinesis API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
        (canAccessVessel as jest.Mock).mockReturnValue(true);
        (userHasPermissions as jest.Mock).mockReturnValue(false);
        (fileNameTimestamp as jest.Mock).mockReturnValue('2024-01-01T00:00:00Z');
        (removeSpecialCharsFromFilename as jest.Mock).mockImplementation((name) => name);
        (generateZip as jest.Mock).mockReturnValue({
            generateNodeStream: jest.fn().mockReturnValue({
                pipe: jest.fn().mockReturnValue({
                    on: jest.fn().mockImplementation((event, callback: any) => {
                        if (event === 'finish') callback();
                        return { on: jest.fn() };
                    }),
                }),
                on: jest.fn(),
            }),
        });
        (getLocationsCollections as jest.Mock).mockReturnValue([
            {
                aggregate: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([] as never),
                }),
            },
        ]);
        (vesselLocationService.findClosestLocation as jest.Mock).mockResolvedValue({
            latitude: 10,
            longitude: 20,
            timestamp: new Date('2024-01-01T00:00:00Z'),
            groundSpeed: 5,
        } as never);
    });

    describe('GET /api/kinesis/listStreams', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                });

                it('should return 401 if not authenticated', async () => {
                    const res = await request(app).get('/api/kinesis/listStreams');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch streams with region parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (awsKinesis.listStreams as jest.Mock).mockResolvedValueOnce(streamsList as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vesselsList as never);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    expect(awsKinesis.listStreams).toHaveBeenCalledWith({ region: 'us-east-1' });
                });

                it('should return 200 and fetch streams from all regions when no region specified', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Region.find as jest.Mock).mockResolvedValueOnce(regionsList as never);
                    (awsKinesis.listStreams as jest.Mock)
                        .mockResolvedValueOnce([streamsList[0]] as never)
                        .mockResolvedValueOnce([streamsList[1]] as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vesselsList as never);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    expect(Region.find).toHaveBeenCalled();
                });

                it('should handle AWS Kinesis errors gracefully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Region.find as jest.Mock).mockResolvedValueOnce(regionsList as never);
                    (awsKinesis.listStreams as jest.Mock)
                        .mockResolvedValueOnce([streamsList[0]] as never)
                        .mockRejectedValueOnce(new Error('AWS Error') as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vesselsList as never);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                });

                it('should filter streams based on vessel access', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (awsKinesis.listStreams as jest.Mock).mockResolvedValueOnce(streamsList as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vesselsList as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                });

                it('should allow access to all vessels for users with permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (awsKinesis.listStreams as jest.Mock).mockResolvedValueOnce(streamsList as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vesselsList as never);
                    (userHasPermissions as jest.Mock).mockReturnValueOnce(true);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                });

                it('should return 500 if internal error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (awsKinesis.listStreams as jest.Mock).mockRejectedValueOnce(new Error('Internal error') as never);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 400 if region validation fails', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should use authUserApiLimiter for user authentication', async () => {
                    setupAuthorizedAuthMocks('user', { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, authToken, User, ApiKey);
                    const vessels = vesselsList.map(vessel => ({ ...vessel, thumbnail_compressed_s3_key: null }));
                    (awsKinesis.listStreams as jest.Mock).mockResolvedValueOnce(streamsList as never);
                    (vesselService.find as jest.Mock).mockResolvedValueOnce(vessels as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/kinesis/dashStreamingSessionURL', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                });

                it('should return 401 if not authenticated', async () => {
                    const res = await request(app).get('/api/kinesis/dashStreamingSessionURL');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if streamName is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=INVALID&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if minutes is not numeric', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=invalid')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if required parameters are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=&region=us-east-1&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 with "Missing required parameters" message when parameters are undefined', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Missing required parameters');

                    require('../../middlewares/validator').validateData = originalValidateData;
                });

                it('should return 404 if stream does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Stream does not exist');
                });

                it('should return 403 if vessel not found for API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 403 if user cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and generate DASH streaming URL', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getDashStreamingSessionURL as jest.Mock).mockResolvedValueOnce(streamingUrl as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.url).toBe(streamingUrl);
                });

                it('should return 200 with undefined minutes', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getDashStreamingSessionURL as jest.Mock).mockResolvedValueOnce(streamingUrl as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.url).toBe(streamingUrl);
                });

                it('should return 500 if internal error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockRejectedValueOnce(new Error('Internal error') as never);

                    const res = await request(app)
                        .get('/api/kinesis/dashStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/kinesis/getScreenShot', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);
                });

                it('should return 401 if not authenticated', async () => {
                    const res = await request(app).get('/api/kinesis/getScreenShot');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if streamName is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?region=us-east-1&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if timestamp is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=invalid')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if required parameters are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=&region=us-east-1&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 403 if vessel not found for API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 403 if user cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(null as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);
                    (userHasPermissions as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 500 if screenshot data is null', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getScreenshot as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 500 if cant process the request', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getScreenshot as jest.Mock).mockResolvedValueOnce(screenshotData as never);
                    (awsKinesis.getStreamTags as jest.Mock).mockResolvedValueOnce(streamTags as never);
                    (getLocationsCollections as jest.Mock).mockResolvedValueOnce([
                        {
                            aggregate: jest.fn().mockReturnValue({
                                toArray: jest.fn()
                                    .mockResolvedValueOnce([] as never)
                                    .mockResolvedValueOnce([{
                                        latitude: 10,
                                        longitude: 20,
                                        timestamp: new Date('2024-01-01T00:00:00Z').getTime(),
                                        groundSpeed: 5
                                    }] as never),
                            }),
                        },
                    ] as never);
                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, callback: any) => {
                                if (event === 'error') {
                                    setTimeout(() => callback(new Error('Stream error')), 10);
                                }
                            }),
                            pipe: jest.fn().mockReturnValue({
                                on: jest.fn((event, callback: any) => {
                                    if (event === 'finish') {
                                        callback();
                                    }
                                }).mockReturnValue({
                                    on: jest.fn((event, callback: any) => {
                                        if (event === 'error') {
                                            setTimeout(() => callback(new Error('Pipe error')), 10);
                                        }
                                    }),
                                } as never),
                            }),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle stream error in zip generation', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getScreenshot as jest.Mock).mockResolvedValueOnce(screenshotData as never);
                    (awsKinesis.getStreamTags as jest.Mock).mockResolvedValueOnce(streamTags as never);

                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, callback: any) => {
                                if (event === 'error') {
                                    setTimeout(() => callback(new Error('Stream error')), 10);
                                }
                            }),
                            pipe: jest.fn().mockReturnThis(),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle stream pipe error in zip generation', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getScreenshot as jest.Mock).mockResolvedValueOnce(screenshotData as never);
                    (awsKinesis.getStreamTags as jest.Mock).mockResolvedValueOnce(streamTags as never);

                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, _callback: any) => {
                                if (event === 'error') {
                                }
                            }),
                            pipe: jest.fn().mockReturnValue({
                                on: jest.fn((event, callback: any) => {
                                    if (event === 'finish') {
                                    } else if (event === 'error') {
                                        setTimeout(() => callback(new Error('Pipe error')), 10);
                                    }
                                }),
                            }),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user cannot access vessel', async () => {
                    jest.resetAllMocks();
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (userHasPermissions as jest.Mock).mockReturnValueOnce(false);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should handle ScreenshotNotFoundError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Screenshot not found');
                    error.name = 'ScreenshotNotFoundError';
                    (awsKinesis.getScreenshot as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should handle ResourceNotFoundException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Resource not found');
                    error.name = 'ResourceNotFoundException';
                    (awsKinesis.getScreenshot as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should handle InvalidArgumentException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Invalid argument');
                    error.name = 'InvalidArgumentException';
                    (awsKinesis.getScreenshot as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should handle NotAuthorizedException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Not authorized');
                    error.name = 'NotAuthorizedException';
                    (awsKinesis.getScreenshot as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should handle general errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getScreenshot as jest.Mock).mockRejectedValueOnce(new Error('General error') as never);

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 400 with "Missing required parameters" message when parameters are undefined', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getScreenShot')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Missing required parameters');

                    require('../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/kinesis/getClip', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);
                });

                it('should return 401 if not authenticated', async () => {
                    const res = await request(app).get('/api/kinesis/getClip');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if streamName is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getClip?region=us-east-1&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if timestamp is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=invalid')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if required parameters are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=&region=us-east-1&timestamp=2024-01-01T00:00:00Z')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 403 if vessel not found for API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 403 if user cannot access vessel', async () => {
                    jest.resetAllMocks();
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 500 if payload is not Buffer', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getClip as jest.Mock).mockResolvedValueOnce({ Payload: 'not-a-buffer', ContentType: 'video/mp4' } as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 500 if cant process the request', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const clipData = {
                        Payload: Buffer.from('fake-clip-data'),
                        ContentType: 'video/'
                    };
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getClip as jest.Mock).mockResolvedValueOnce(clipData as never);
                    (awsKinesis.getStreamTags as jest.Mock).mockResolvedValueOnce(streamTags as never);
                    (getLocationsCollections as jest.Mock).mockResolvedValueOnce([
                        {
                            aggregate: jest.fn().mockReturnValue({
                                toArray: jest.fn()
                                    .mockResolvedValueOnce([] as never)
                                    .mockResolvedValueOnce([{
                                        latitude: 10,
                                        longitude: 20,
                                        timestamp: new Date('2024-01-01T00:00:00Z').getTime(),
                                        groundSpeed: 5
                                    }] as never),
                            }),
                        },
                    ] as never);
                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, callback: any) => {
                                if (event === 'error') {
                                    setTimeout(() => callback(new Error('Stream error')), 10);
                                }
                            }),
                            pipe: jest.fn().mockReturnValue({
                                on: jest.fn((event, callback: any) => {
                                    if (event === 'finish') {
                                        callback();
                                    }
                                }).mockReturnValue({
                                    on: jest.fn((event, callback: any) => {
                                        if (event === 'error') {
                                            setTimeout(() => callback(new Error('Pipe error')), 10);
                                        }
                                    }),
                                } as never),
                            }),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle ResourceNotFoundException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Resource not found');
                    error.name = 'ResourceNotFoundException';
                    (awsKinesis.getClip as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should handle InvalidArgumentException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Invalid argument');
                    error.name = 'InvalidArgumentException';
                    (awsKinesis.getClip as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should handle NotAuthorizedException', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('Not authorized');
                    error.name = 'NotAuthorizedException';
                    (awsKinesis.getClip as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should handle general errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    const error = new Error('General error');
                    (awsKinesis.getClip as jest.Mock).mockRejectedValueOnce(error as never);

                    const res = await request(app)
                        .get('/api/kinesis/getClip?streamName=testStream&region=us-east-1&timestamp=2024-01-01T00:00:00Z&unitId=testStream&vesselId=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 with "Missing required parameters" message when parameters are undefined', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/kinesis/getClip')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Missing required parameters');

                    require('../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/kinesis/hlsStreamingSessionURL', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(true);
                });

                it('should return 401 if not authenticated', async () => {
                    const res = await request(app).get('/api/kinesis/hlsStreamingSessionURL');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if streamName is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?region=&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&streamMode=&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=&streamMode=INVALID&minutes=60')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if minutes is not numeric', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=&streamMode=LIVE&minutes=invalid')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if required parameters are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=&region=&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if stream does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Stream does not exist');
                });

                it('should return 403 if vessel not found for API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 403 if user cannot access vessel', async () => {
                    jest.resetAllMocks();
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValueOnce(false);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and generate HLS streaming URL', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getHlsStreamingSessionURL as jest.Mock).mockResolvedValueOnce(streamingUrl as never);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.url).toBe(streamingUrl);
                });

                it('should return 200 with undefined minutes', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValueOnce(streamData as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValueOnce(vesselsList[0] as never);
                    (awsKinesis.getHlsStreamingSessionURL as jest.Mock).mockResolvedValueOnce(streamingUrl as never);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.url).toBe(streamingUrl);
                });

                it('should return 500 if internal error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockRejectedValueOnce(new Error('Internal error') as never);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 400 with "Missing required parameters" message when parameters are undefined', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Missing required parameters');

                    require('../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
