import mongoose from "mongoose";
import db from "../modules/db";
import { IInAppNotification } from "../interfaces/InAppNotification";

const InAppNotificationSchema = new mongoose.Schema({
    title: { type: String, required: true },
    message: { type: String, required: true },
    receiver: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    is_read: { type: Boolean, default: false },
    artifact_id: { type: mongoose.Schema.Types.ObjectId, required: false },
    created_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});

const InAppNotification = db.qm.model<IInAppNotification>("InAppNotification", InAppNotificationSchema, "in_app_notifications");

export default InAppNotification;
