import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('UserCompletionLogs Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create UserCompletionLogs model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);
        jest.doMock('../../models/User', () => ({ default: 'MockUser' }));

        delete require.cache[require.resolve('../../models/UserCompletionLogs')];

        const UserCompletionLogsModule = await import('../../models/UserCompletionLogs');
        const UserCompletionLogs = UserCompletionLogsModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('UserCompletionLogs', expect.any(Object), 'logs_users_completions');
        expect(UserCompletionLogs).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const completedAt = schemaArg.paths.completed_at.default();
        expect(typeof completedAt).toBe('string');
        expect(completedAt).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const updatedAt = schemaArg.paths.updated_at.default();
        expect(typeof updatedAt).toBe('string');
        expect(updatedAt).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});
