import React, { useEffect, useMemo, useState } from "react";
import { Grid, Typography, IconButton, Skeleton, Tabs, Tab, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PreviewMedia from "../../../components/PreviewMedia";
import dayjs from "dayjs";
import { defaultValues, handleVesselTimezone, simplifyTimezone, permissions, displayCoordinates } from "../../../utils";
import { UserProvider } from "../../../providers/UserProvider";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import { getSocket } from "../../../socket";
import artifactController from "../../../controllers/Aritfact.controller";
import theme from "../../../theme";

const InfoWindow = ({ artifact, artifactInfowWindow, vesselInfo, user }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);
    const [fullArtifact, setFullArtifact] = useState(null);
    const [loading, setLoading] = useState(true);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const vesselTimezone = useMemo(() => {
        const timezone = handleVesselTimezone(fullArtifact || artifact, vesselInfo);
        return timezone;
    }, [fullArtifact, artifact, vesselInfo]);

    const handleFlagChanged = async () => {
        await artifactFlagController.getUserFlaggedArtifactIds();
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(artifact._id));
    };

    const fetchArtifactDetails = async () => {
        try {
            setLoading(true);
            const response = await artifactController.getArtifactDetail(artifact._id);
            setFullArtifact(response);
        } catch (error) {
            if (error.name === "AbortError" || error.name === "CanceledError" || error.code === "ERR_CANCELED") {
                console.log("Request was aborted for artifact:", artifact._id);
                return;
            }
            console.error("Error fetching artifact details:", error);
            // Fallback to using the minimal artifact data
            setFullArtifact(artifact);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const socket = getSocket();

        handleFlagChanged();
        fetchArtifactDetails();

        socket.on("artifacts_flagged/changed", handleFlagChanged);
        return () => socket.off("artifacts_flagged/changed", handleFlagChanged);
    }, [artifact._id]);

    useEffect(() => {
        if (fullArtifact) {
            const thumbnailUrl = fullArtifact.thumbnail_url;
            const videoUrl = fullArtifact.video_url;
            const imageUrl = fullArtifact.image_url;

            if (fullArtifact.video_path) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [fullArtifact]);

    const handleClose = () => {
        artifactInfowWindow.close();
    };

    const [activeTab, setActiveTab] = useState(0)
    const details = [
        { label: "Location", value: fullArtifact?.location ? displayCoordinates(fullArtifact.location?.coordinates, !!user?.use_MGRS, 4) : "Not available" },
        { label: "Super Category", value: fullArtifact?.super_category || "Not available" },
        { label: "Category", value: fullArtifact?.category || "Not available" },
        { label: "Size", value: fullArtifact?.size || "Not available" },
        { label: "Color", value: fullArtifact?.color || "Not available" },
        { label: "Orientation", value: fullArtifact?.vessel_orientation || "Not available" },
        { label: "Detected Country", value: fullArtifact?.home_country || "Not available" },
        { label: "Bearing Angle", value: fullArtifact?.true_bearing ? `${Number(fullArtifact.true_bearing).toFixed(2)}\u00B0` : "Not available" },
    ]
    const aisInfo = fullArtifact?.portal?.ais_info?.data

    const aisDetails = [
        { label: "MMSI", value: aisInfo?.mmsi || "Not available" },
        { label: "Message Class", value: aisInfo?.sensor_ais_class || "Not available" },
        { label: "Name", value: aisInfo?.name || "Not available" },
        { label: "Ship State", value: aisInfo?.nav_state || "Not available" },
        { label: "Ship Type", value: aisInfo?.design_ais_ship_type_name || "Not available" },
        { label: "Beam", value: aisInfo?.design_beam || "Not available" },
        { label: "Length", value: aisInfo?.design_length || "Not available" },
        { label: "Speed", value: aisInfo?.nav_speed_over_ground || "Not available" },
        { label: "Bearing Angle", value: aisInfo?.portal_true_bearing_deg ? `${Number(aisInfo.portal_true_bearing_deg).toFixed(2)}\u00B0` : "Not available" },
        { label: "Registry Country", value: aisInfo?.portal_registry_country?.country_name || "Not available" },
    ];
    return (
        <Grid
            container
            direction="column"
            style={{
                color: "white",
                padding: "10px",
                background: "#282C39",
                maxWidth: "640px",
            }}
        >

            <style>
                {`
                         .gm-style-iw-chr, .gm-style-iw-tc {
                             display: none !important;
                         }
                         .gm-style .gm-style-iw-c {
                             background-color: #343B44 !important;
                             outline: none;
                             padding: 0;
                         }
                         .gm-style .gm-style-iw-d {
                             overflow: auto !important;
                         }
                         .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                             background-color: #fff !important;
                         }
                         .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                             background: #343B44 !important;
                         }
                     `}
            </style>
            <Grid container width={"100%"} display={"flex"} justifyContent="space-between" alignItems="center" style={{
            }}>
                <Grid
                    sx={{
                        backgroundColor: "#4F5968",
                        borderTop: `1px solid ${theme.palette.custom.borderColor}`,
                        borderLeft: `1px solid ${theme.palette.custom.borderColor}`,
                        borderRight: `1px solid ${theme.palette.custom.borderColor}`,
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                    }}
                >
                    <Tabs value={activeTab} onChange={(_, v) => setActiveTab(v)}
                        indicatorColor="none"
                        sx={{
                            width: "100%",
                            color: "white",
                            "& .MuiButtonBase-root.Mui-selected": {
                                backgroundColor: "#3B4251",
                                color: "white",
                                ...(activeTab === 0 ? { borderTopLeftRadius: "8px" } : { borderTopRightRadius: "8px" })
                            },
                        }}>
                        <Tab label="Detail" sx={{ color: "white", borderTopRightRadius: !aisInfo ? "8px" : "" }} />
                        {
                            aisInfo && <Tooltip title={!aisInfo && "AIS information is not available"}>
                                <Tab label="AIS" sx={{
                                    color: "white",
                                    "&.Mui-disabled": {
                                        color: theme.palette.custom.mediumGrey,
                                        opacity: 1,
                                        cursor: "not-allowed",
                                        pointerEvents: "fill",
                                    },
                                }}
                                    disabled={!aisInfo} />
                            </Tooltip>
                        }
                    </Tabs>
                </Grid>
                <Grid sx={{
                    display: "flex", flexDirection: "row",
                }} >
                    <Typography sx={{ marginRight: "10px", marginTop: "5px" }}>
                        {fullArtifact && fullArtifact.timestamp
                            ? `${dayjs(fullArtifact.timestamp).tz(vesselTimezone).format(defaultValues.dateTimeFormat())} ${vesselTimezone && simplifyTimezone(vesselTimezone)} `
                            : ""}{" "}
                    </Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: "white",
                            border: "1px solid white",
                            "&:hover": {
                                backgroundColor: "white",
                                color: "#4F5968",
                            },
                        }}
                    >
                        <CloseIcon sx={{ fontSize: "16px" }} />
                    </IconButton>
                </Grid>
            </Grid>
            <Grid container sx={{
                display: "flex",
                flexDirection: "row",
                height: "230px",
                backgroundColor: "#3B4251",
                borderBottomLeftRadius: "10px",
                borderBottomRightRadius: "10px",
                borderTopRightRadius: "10px",

            }}>
                <Grid sx={{ height: "230px" }}
                >
                    {/* Image/Video Section */}
                    <Grid
                        sx={{
                            position: "relative",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            height: 230,
                            width: 300,
                            borderRadius: 1,
                            padding: "10px"
                        }}
                    >
                        {loading ? (
                            <Skeleton
                                variant="rectangular"
                                width="100%"
                                height="100%"
                                sx={{
                                    borderRadius: 1,
                                    minHeight: 200,
                                    minWidth: 280,
                                }}
                            />
                        ) : (
                            <PreviewMedia
                                thumbnailLink={thumbnail}
                                originalLink={src}
                                cardId={fullArtifact?._id}
                                isImage={!fullArtifact?.video_path}
                                style={{ borderRadius: 8 }}
                                showFullscreenIcon={true}
                                showFullscreenIconForMap={!fullArtifact?.video_path}
                                // userTest={user}
                                showVideoThumbnail={fullArtifact?.video_path}
                                showArchiveButton={hasManageArtifacts}
                                isArchived={fullArtifact?.portal?.is_archived}
                                vesselId={fullArtifact?.onboard_vessel_id}
                                skeletonStyle={{
                                    minHeight: 200,
                                    minWidth: 280,
                                }}
                                flaggedArtifact={flaggedArtifact}
                                direction="row"
                                helperIconsPosition="bottom-center"
                            />
                        )}
                    </Grid>
                </Grid>
                <Grid
                    sx={{
                        maxHeight: "210px",
                        overflowY: "auto",
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        marginTop: "10px",
                        maxWidth: "320px",
                    }}
                >
                    {loading || !fullArtifact ? (
                        <Grid sx={{
                            width: "315px",
                        }}>
                            <Skeleton variant="text" width="80%" height={24} sx={{ marginTop: "5px" }} />
                            <Skeleton variant="text" width="70%" height={24} />
                            <Skeleton variant="text" width="60%" height={24} />
                            <Skeleton variant="text" width="75%" height={24} />
                            <Skeleton variant="text" width="90%" height={24} />
                            <Skeleton variant="text" width="85%" height={24} />
                            <Skeleton variant="text" width="95%" height={24} />
                            <Skeleton variant="text" width="90%" height={24} />
                        </Grid>
                    ) : (
                        <Grid sx={{
                            width: "320px",
                            minWidth: "320px"
                        }}>
                            {
                                activeTab === 0 ?
                                    details.map((detail, index) => (
                                        <Grid sx={{ display: "flex", width: "100%", flexDirection: "row", backgroundColor: index % 2 === 0 ? "" : "#474D59", padding: "1px 0" }} key={index}>
                                            <Typography sx={{ fontWeight: "bold", minWidth: "150px", paddingLeft: "5px" }} >
                                                {detail.label}:
                                            </Typography>
                                            <Typography sx={{}}>
                                                {detail.value}
                                            </Typography>
                                        </Grid>
                                    ))
                                    :
                                    aisDetails.map((detail, index) => (
                                        <Grid sx={{ display: "flex", width: "100%", flexDirection: "row", backgroundColor: index % 2 === 0 ? "" : "#474D59", padding: "1px 0" }} key={index}>
                                            <Typography sx={{ fontWeight: "bold", minWidth: "150px", paddingLeft: "5px" }} >
                                                {detail.label}:
                                            </Typography>
                                            <Typography sx={{ overflowWrap:"anywhere"}}>
                                                {detail.value}
                                            </Typography>
                                        </Grid>
                                    ))
                            }
                        </Grid>
                    )}
                </Grid>
            </Grid>

        </Grid>
    );
};

const WrappedInfoWindow = (props) => (
    <UserProvider>
        <InfoWindow {...props} />
    </UserProvider>
);

export default WrappedInfoWindow;
