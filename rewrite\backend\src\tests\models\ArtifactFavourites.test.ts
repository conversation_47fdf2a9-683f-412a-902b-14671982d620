import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ArtifactFavourites Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ArtifactFavourites model with proper schema and hooks', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactFavourites')];

        const ArtifactFavouritesModule = await import('../../models/ArtifactFavourites');
        const ArtifactFavourites = ArtifactFavouritesModule.default;

        expect(mockDb.qm.model).toHaveBeenCalled();
        expect(ArtifactFavourites).toBeDefined();

        const artifactFavouritesCall = mockDb.qm.model.mock.calls.find((call: any) => call[0] === 'ArtifactFavourites');
        if (!artifactFavouritesCall) {
            throw new Error('ArtifactFavourites model not found');
        }
        const schemaArg = artifactFavouritesCall[1];

        expect(schemaArg.paths).toBeDefined();

        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockFavourite = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', user_id: 'user-id', artifact_id: 'artifact-id' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(null);
            saveHookFn(mockFavourite);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(null);
            deleteHookFn(mockFavourite);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});
