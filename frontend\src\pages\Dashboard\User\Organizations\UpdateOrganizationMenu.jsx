import { useState } from "react";
import axiosInstance from "../../../../axios";
import { alpha, Menu, MenuItem } from "@mui/material";
import theme from "../../../../theme";
import { permissions } from "../../../../utils";
import ConfirmModal from "../../../../components/ConfirmModal";

const UpdateOrganizationMenu = ({
    updateOrganizationAnchorEl,
    setUpdateOrganizationAnchorEl,
    organizations,
    roles,
    selectedOrganizationUser,
    setUpdatingOrganization,
    onSuccess,
}) => {
    const open = Boolean(updateOrganizationAnchorEl);
    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [confirmModalDetails, setConfirmModalDetails] = useState({ title: "", message: "" });

    const handleClick = async (organization) => {
        const confirmed = await openConfirmationDialog({
            title: "Confirm Organization Update",
            message: (
                <>
                    Are you sure you want to assign <b>{organization.name}</b> organization to <b>{selectedOrganizationUser.name}</b>?
                </>
            ),
        });

        if (!confirmed) {
            handleClose();
            return;
        }

        setUpdatingOrganization(selectedOrganizationUser._id);
        axiosInstance
            .patch(`/users/${selectedOrganizationUser._id}/organization`, { organization_id: organization._id }, { meta: { showSnackbar: true } })
            .then(() => onSuccess && onSuccess())
            .catch(console.error)
            .finally(() => setUpdatingOrganization());
        handleClose();
    };

    const openConfirmationDialog = ({ title, message }) => {
        return new Promise((resolve) => {
            const handleConfirm = () => {
                resolve(true);
                setConfirmModalOpen(false);
            };

            const handleCancel = () => {
                resolve(false);
                setConfirmModalOpen(false);
            };

            setConfirmModalDetails({ title, message, onConfirm: handleConfirm, onCancel: handleCancel });
            setConfirmModalOpen(true);
        });
    };

    const handleClose = () => {
        setUpdateOrganizationAnchorEl(null);
    };

    const isUpdateable = (organization) => {
        const selectedUserRole = roles.find((role) => role.role_id == selectedOrganizationUser.role_id);
        return (
            organization._id === selectedOrganizationUser.organization_id ||
            (organization.is_miscellaneous && !selectedUserRole?.denied_permissions.includes(permissions.manageUsers))
        );
    };

    return (
        <>
            <Menu
                anchorEl={updateOrganizationAnchorEl}
                open={open}
                onClose={handleClose}
                sx={{
                    "& .MuiPaper-root": {
                        minWidth: updateOrganizationAnchorEl ? updateOrganizationAnchorEl.offsetWidth : "auto",
                        backgroundColor: theme.palette.primary.main,
                    },
                }}
                anchorOrigin={{
                    vertical: 35,
                    horizontal: 0,
                }}
            >
                {organizations.map((organization) => (
                    <MenuItem
                        key={organization._id}
                        disabled={isUpdateable(organization)}
                        onClick={() => handleClick(organization)}
                        sx={{
                            gap: 1,
                            "&:hover": {
                                backgroundColor: alpha(theme.palette.custom.darkBlue, 0.3) + " !important",
                            },
                        }}
                    >
                        {organization.name}
                    </MenuItem>
                ))}
            </Menu>
            <ConfirmModal
                title={confirmModalDetails.title}
                message={confirmModalDetails.message}
                initialState={isConfirmModalOpen}
                onClose={confirmModalDetails.onCancel}
                onConfirm={confirmModalDetails.onConfirm}
            />
        </>
    );
};

export default UpdateOrganizationMenu;
