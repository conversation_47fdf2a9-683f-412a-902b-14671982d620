import mongoose from "mongoose";
import db from "../modules/db";
import { IRegion } from "src/interfaces/Region";

const regionSchema = new mongoose.Schema({
    name: { type: String, required: true },
    value: { type: String, required: true, unique: true },
    is_live: { type: Boolean, required: true },
    timezone: { type: String, required: true },
});

const Region = db.qm.model<IRegion>("Region", regionSchema);

module.exports = Region;

export default Region;
