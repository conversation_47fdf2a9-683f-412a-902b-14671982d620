import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { sessionLogsList, sessionLogsWithUser, sessionLogsAggregateResult } from '../data/Logs';
import SessionLog from '../mocks/models/sessionLog.mock';
import { validateError } from '../mocks/utils/functions.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/SessionLog', () => require('../mocks/models/sessionLog.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Log API', () => {
    describe('GET /api/logs/sessions', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/logs/sessions');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch all session logs with pagination', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('logs');
                    expect(res.body).toHaveProperty('totalPages');
                    expect(res.body).toHaveProperty('currentPage');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body.logs).toEqual(sessionLogsList);
                });

                it('should return 200 with pagination parameters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce({
                        total: null,
                        data: null,
                    });

                    const res = await request(app)
                        .get('/api/logs/sessions?page=2&rowsPerPage=5')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.currentPage).toBe(2);
                });

                it('should return 200 with status filter (online)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?status=online')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with status filter (offline)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?status=offline&full_name_or_browser_or_device=Chrome')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with created_after filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?created_after=1640995200000')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with search filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?full_name_or_browser_or_device=Chrome')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with sorting parameters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?sorting[connect_timestamp]=DESC')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with sorting NONE parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?sorting[connect_timestamp]=NONE')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with sorting ASC parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsAggregateResult);

                    const res = await request(app)
                        .get('/api/logs/sessions?sorting[connect_timestamp]=ASC')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with empty results', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce([{ total: 0, data: [] }]);

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.logs).toEqual([]);
                    expect(res.body.totalCount).toBe(0);
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/logs/sessions/user', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/logs/sessions/user');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if userId is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/logs/sessions/user')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('User ID is required');
                });

                it('should return 200 and fetch session logs by user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsWithUser);

                    const res = await request(app)
                        .get('/api/logs/sessions/user?userId=66942a54a7f848634a00990a')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(sessionLogsWithUser);
                });

                it('should return 200 with status filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsWithUser);

                    const res = await request(app)
                        .get('/api/logs/sessions/user?userId=66942a54a7f848634a00990a&status=online')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with search filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsWithUser);

                    const res = await request(app)
                        .get('/api/logs/sessions/user?userId=66942a54a7f848634a00990a&full_name_or_browser_or_device=Chrome')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with empty results', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/logs/sessions/user?userId=66942a54a7f848634a00990a')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual([]);
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/logs/sessions/user?userId=66942a54a7f848634a00990a')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/logs/sessions/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/logs/sessions/66e07be04c55798738bd1c88');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/logs/sessions/invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch session log by id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce(sessionLogsWithUser);

                    const res = await request(app)
                        .get('/api/logs/sessions/66e07be04c55798738bd1c88')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(sessionLogsWithUser[0]);
                });

                it('should return 404 if log not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/logs/sessions/66e07be04c55798738bd1c88')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Log does not exist with that id');
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (SessionLog as any).aggregate.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/logs/sessions/66e07be04c55798738bd1c88')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});