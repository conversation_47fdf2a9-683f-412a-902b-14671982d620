import { describe, it, beforeEach, afterEach, expect, jest } from '@jest/globals';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as any;

const consoleSpy = {
    warn: jest.spyOn(console, 'warn').mockImplementation(() => { }),
    log: jest.spyOn(console, 'log').mockImplementation(() => { })
};

const ORIGINAL_ENV = process.env;

describe('notifyLog.ts', () => {
    beforeEach(() => {
        jest.resetModules();
        jest.clearAllMocks();
        process.env = { ...ORIGINAL_ENV };
    });

    afterEach(() => {
        process.env = ORIGINAL_ENV;
        consoleSpy.warn.mockClear();
        consoleSpy.log.mockClear();
    });

    it('should throw error when SLACK_WEBHOOK_URL is missing', async () => {
        process.env.NODE_ENV = 'production';
        delete process.env.SLACK_LOGS_WEBHOOK_URL;

        await expect(async () => {
            await import('../../modules/notifyLog');
        }).rejects.toThrow('[notifyLog.js] SLACK_WEBHOOK_URL is missing in the .env');
    });

    it('should throw error when NODE_ENV is missing', async () => {
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';
        delete process.env.NODE_ENV;

        await expect(async () => {
            await import('../../modules/notifyLog');
        }).rejects.toThrow('[notifyLog.js] ENVIRONMENT is missing in the .env');
    });

    it('should skip logging for non-cloud environments', async () => {
        process.env.NODE_ENV = 'test';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'error',
            message: 'Test error message',
            stack: 'Error stack trace'
        });

        expect(mockedAxios.post).not.toHaveBeenCalled();
        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] Not in cloud environment, skipping log to Slack');
    });

    it('should warn when required parameters are missing', async () => {
        process.env.NODE_ENV = 'portal';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'error',
            message: '',
            stack: ''
        });

        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] severity, message, stack are required');
    });

    it('should handle missing severity parameter', async () => {
        process.env.NODE_ENV = 'portal';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: undefined as any,
            message: 'Test message',
            stack: 'Test stack'
        });

        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] severity, message, stack are required');
    });

    it('should handle missing message parameter', async () => {
        process.env.NODE_ENV = 'portal';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'error',
            message: undefined as any,
            stack: 'Test stack'
        });

        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] severity, message, stack are required');
    });

    it('should handle missing stack parameter', async () => {
        process.env.NODE_ENV = 'portal';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'fatal' as any,
            message: 'Test message',
            stack: undefined as any
        });
        await postLogToSlack({
            severity: 'warning' as any,
            message: 'Test message',
            stack: undefined as any
        });
        await postLogToSlack({
            severity: 'error' as any,
            message: 'Test message',
            stack: undefined as any
        });

        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] severity, message, stack are required');
    });

    it('should skip logging for production environment', async () => {
        process.env.NODE_ENV = 'production';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'error',
            message: 'Test message',
            stack: 'Test stack'
        });

        expect(mockedAxios.post).not.toHaveBeenCalled();
        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] Not in cloud environment, skipping log to Slack');
    });

    it('should skip logging for development environment', async () => {
        process.env.NODE_ENV = 'development';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        await postLogToSlack({
            severity: 'unknown' as any,
            message: 'Test message',
            stack: 'Test stack'
        });

        expect(mockedAxios.post).not.toHaveBeenCalled();
        expect(consoleSpy.warn).toHaveBeenCalledWith('[postLogToSlack] Not in cloud environment, skipping log to Slack');
    });

    it('should return undefined', async () => {
        process.env.NODE_ENV = 'test';
        process.env.SLACK_LOGS_WEBHOOK_URL = 'https://hooks.slack.com/test';

        const { postLogToSlack } = await import('../../modules/notifyLog');

        const result = await postLogToSlack({
            severity: 'fatal',
            message: 'Test message',
            stack: 'Test stack'
        });

        expect(result).toBeUndefined();
    });
});