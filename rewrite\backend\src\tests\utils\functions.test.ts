import jwt from 'jsonwebtoken';
import {
    isURL,
    isBase64,
    isBase64OrURL,
    validateError,
    isIntStrict,
    generateInvitationLink,
    generateUnsubscribeToken,
    getSessionsByCoordinates,
    canAccessVessel,
    generateZip,
    removeSpecialCharsFromFilename,
    fileNameTimestamp,
    userHasPermissions,
    streamToBuffer,
    getStaticMapOld,
    buildStaticMarkerSignature,
    generateTimeSeries,
    escapeRegExp,
    getUnitIdsFromVessel,
    getContentTypeFromFileExtension,
    normalizeName,
    findVesselByUnitHistory,
    groupArtifactsByDuplicateIndex,
    cleanSuggestion,
    splitByMonthsUTC,
    getSimplifiedCoords,
    getLocationsCollections,
    consoleLogObjectSize,
} from '../../utils/functions';
import { jest, describe, it, expect, beforeEach, afterEach, beforeAll } from '@jest/globals';

jest.mock('jsonwebtoken', () => ({
    __esModule: true,
    default: {
        sign: jest.fn(),
    },
}));


describe('Utility Functions', () => {
    describe('isURL', () => {
        it('should return true for valid URLs', () => {
            expect(isURL('http://example.com')).toBe(true);
            expect(isURL('https://example.com')).toBe(true);
            expect(isURL('ftp://example.com')).toBe(true);
        });

        it('should return false for invalid URLs', () => {
            expect(isURL('invalid-url')).toBe(false);
            expect(isURL('ftp://')).toBe(false);
            expect(isURL('http:/example.com')).toBe(false);
        });
    });

    describe('isBase64', () => {
        it('should return true for valid Base64 strings', () => {
            expect(isBase64('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA')).toBe(true);
        });

        it('should return false for invalid Base64 strings', () => {
            expect(isBase64('invalid-base64')).toBe(false);
            expect(isBase64('data:image/png;base64,')).toBe(false);
        });
    });

    describe('isBase64OrURL', () => {
        it('should return true for a valid URL', () => {
            expect(isBase64OrURL('http://example.com')).toBe(true);
        });

        it('should return true for a valid Base64 string', () => {
            expect(isBase64OrURL('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA')).toBe(true);
        });

        it('should return true for an array containing valid URLs and Base64', () => {
            expect(isBase64OrURL(['http://example.com', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA'])).toBe(true);
        });

        it('should return false for an invalid URL and Base64 string', () => {
            expect(isBase64OrURL('invalid')).toBe(false);
            expect(isBase64OrURL(['invalid-url', 'invalid-base64'])).toBe(false);
        });
    });

    describe('isIntStrict', () => {
        it('should return true for strict integers', () => {
            expect(isIntStrict(5)).toBe(true);
            expect(isIntStrict(-10)).toBe(true);
            expect(isIntStrict(0)).toBe(true);
        });

        it('should return false for non-integers', () => {
            expect(isIntStrict(5.5)).toBe(false);
            expect(isIntStrict('5')).toBe(false);
            expect(isIntStrict(NaN)).toBe(false);
            expect(isIntStrict(null as any)).toBe(false);
            expect(isIntStrict(undefined as any)).toBe(false);
        });
    });

    describe('validateError', () => {
        let res;

        beforeEach(() => {
            res = {
                status: jest.fn().mockReturnThis(),
                json: jest.fn(),
            };
        });

        it('should handle MongoServerError with code 11000', () => {
            const err = {
                name: 'MongoServerError',
                code: 11000,
                keyValue: { email: '<EMAIL>' },
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({ message: 'Value already exists: email = <EMAIL>' });
        });

        it('should handle MongoServerError with other codes', () => {
            const err = {
                name: 'MongoServerError',
                code: 12345,
                message: 'Some error occurred',
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unexpected error occured: Some error occurred' });
        });

        it('should handle MongoServerError with other codes having no message', () => {
            const err = {
                name: 'MongoServerError',
                code: 12345,
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalled();
        });

        it('should handle ResourceNotFoundException', () => {
            const err = { code: 'ResourceNotFoundException', message: 'Resource not found' };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(404);
            expect(res.json).toHaveBeenCalledWith({ message: 'Resource not found' });
        });

        it('should handle other errors', () => {
            const err = new Error('An unknown error occurred');

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unexpected error occured: An unknown error occurred' });
        });

        it('should handle other errors with no message', () => {
            const err = { error: 'Something went wrong' };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalled();
        });
    });

    describe('generateInvitationLink', () => {
        const mockEmail = '<EMAIL>';
        const mockRoleId = 'role123';
        const mockAllowedVessels: string[] = ['v1','v2'];
        const mockAdminId = 'admin456';
        const mockRole = 'user';
        const organization_name = 'Org';
        const organization_id = 'org123';

        const mockJwtSecret = 'mockSecret';

        beforeAll(() => {
            process.env.JWT_SECRET = mockJwtSecret;
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should return a signed JWT token', () => {
            const mockToken = 'mockedToken';
            (jwt as any).sign.mockImplementationOnce(() => mockToken);

            const token = generateInvitationLink(
                mockEmail,
                mockRoleId,
                mockAllowedVessels,
                mockAdminId,
                mockRole,
                organization_name,
                organization_id,
            );

            expect(token).toBe(mockToken);
            expect((jwt as any).sign).toHaveBeenCalledWith(
                {
                    email: mockEmail,
                    role_id: mockRoleId,
                    allowed_vessels: mockAllowedVessels,
                    admin_id: mockAdminId,
                    role: mockRole,
                    organization_name,
                    organization_id,
                    timestamp: expect.any(Number),
                },
                mockJwtSecret,
                { expiresIn: '72h' }
            );
        });

        it('should throw an error if JWT signing fails', () => {
            const error = new Error('JWT sign failed');
            (jwt as any).sign.mockImplementationOnce(() => { throw error; });

            expect(() => {
                generateInvitationLink(
                    mockEmail,
                    mockRoleId,
                    mockAllowedVessels,
                    mockAdminId,
                    mockRole,
                    organization_name,
                    organization_id,
                );
            }).toThrow('JWT sign failed');
        });
    });

    describe('generateUnsubscribeToken', () => {
        beforeEach(() => {
            (jwt.sign as jest.Mock).mockReturnValue('unsubscribe-token');
        });

        it('should generate unsubscribe token with correct payload', () => {
            const result = generateUnsubscribeToken('<EMAIL>', 'alert123');

            expect(jwt.sign).toHaveBeenCalled();
            expect(result).toBe('unsubscribe-token');
        });
    });

    describe('getSessionsByCoordinates', () => {
        it('should group coordinates into sessions based on timestamp', () => {
            const coordinates = [
                { timestamp: '2023-01-01T10:00:00Z' },
                { timestamp: '2023-01-01T10:02:00Z' },
                { timestamp: '2023-01-01T10:10:00Z' },
                { timestamp: '2023-01-01T10:12:00Z' },
            ];
            const result = getSessionsByCoordinates(coordinates);
            expect(Array.isArray(result)).toBe(true);
        });

        it('should return empty array for single coordinate', () => {
            const coordinates = [{ timestamp: '2023-01-01T10:00:00Z' }];
            const result = getSessionsByCoordinates(coordinates);
            expect(result).toHaveLength(0);
        });
    });

    describe('canAccessVessel', () => {
        it('should return true when user has access to vessel', () => {
            const user = { allowed_vessels: ['vessel1', 'vessel2'], permissions: [{ permission_id: 1 }] };
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            const result = canAccessVessel({ user }, vessel);
            expect(result).toBe(true);
        });

        it('should return false when user does not have access to vessel', () => {
            const user = { allowed_vessels: ['vessel1', 'vessel2'], permissions: [{ permission_id: 1 }] };
            const vessel = { _id: 'vessel3', is_active: true, region_group_id: 'region1' };
            const result = canAccessVessel({ user }, vessel);
            expect(result).toBe(false);
        });

        it('should return true when user has accessAllVessels permission', () => {
            const user = { allowed_vessels: [], permissions: [{ permission_id: 300 }] };
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            const result = canAccessVessel({ user }, vessel);
            expect(result).toBe(true);
        });
    });

    describe('removeSpecialCharsFromFilename', () => {
        it('should remove special characters from filename', () => {
            const result = removeSpecialCharsFromFilename('file<>:"|?*.txt');
            expect(result).toBe('file.txt');
        });

        it('should handle filename with no special characters', () => {
            const result = removeSpecialCharsFromFilename('normal-file.txt');
            expect(result).toBe('normal-file.txt');
        });
    });

    describe('fileNameTimestamp', () => {
        it('should return timestamp string', () => {
            const result = fileNameTimestamp();
            expect(result).toMatch(/^\d{4}\d{1,2}\d{1,2}_\d{1,2}\d{1,2}\d{1,2}$/);
        });
    });

    describe('userHasPermissions', () => {
        it('should return true when user has all required permissions', () => {
            const user = { permissions: [{ permission_id: 1 }, { permission_id: 2 }, { permission_id: 3 }] };
            const result = userHasPermissions(user, [1, 2]);
            expect(result).toBe(true);
        });

        it('should return false when user missing required permissions', () => {
            const user = { permissions: [{ permission_id: 1 }] };
            const result = userHasPermissions(user, [1, 2]);
            expect(result).toBe(false);
        });

        it('should return true when no permissions required', () => {
            const user = { permissions: [{ permission_id: 1 }] };
            const result = userHasPermissions(user, []);
            expect(result).toBe(true);
        });
    });

    describe('escapeRegExp', () => {
        it('should escape special regex characters', () => {
            const result = escapeRegExp('test[.*+?^${}()|[]\\');
            expect(result).toBe('test\\[\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]\\\\');
        });
    });

    describe('getContentTypeFromFileExtension', () => {
        it('should return correct content type for known extensions', () => {
            expect(getContentTypeFromFileExtension('jpg')).toBe('image/jpg');
            expect(getContentTypeFromFileExtension('png')).toBe('image/png');
            expect(getContentTypeFromFileExtension('mp4')).toBe('video/mp4');
        });

        it('should return default content type for unknown extensions', () => {
            expect(getContentTypeFromFileExtension('unknown')).toBe('application/octet-stream');
        });
    });

    describe('normalizeName', () => {
        it('should normalize name by trimming and replacing multiple spaces', () => {
            const result = normalizeName('  Test   Name  ');
            expect(result).toBe('Test Name');
        });
    });

    describe('cleanSuggestion', () => {
        it('should clean suggestion text', () => {
            const result = cleanSuggestion('  Test Suggestion  ');
            expect(result).toBe('Test Suggestion');
        });

        it('should handle empty suggestion', () => {
            const result = cleanSuggestion('');
            expect(result).toBe('');
        });
    });

    describe('getSimplifiedCoords', () => {
        it('should simplify coordinates array', () => {
            const coords = [
                { location: { coordinates: [10.123, 20.456] } },
                { location: { coordinates: [10.124, 20.457] } },
                { location: { coordinates: [10.125, 20.458] } }
            ];
            const result = getSimplifiedCoords(coords as any);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('consoleLogObjectSize', () => {
        it('should log object size without throwing', () => {
            const obj = { test: 'data' };
            expect(() => consoleLogObjectSize(obj)).not.toThrow();
        });
    });

    describe('generateZip', () => {
        it('should generate zip file without throwing', async () => {
            const files = [{ name: 'test.txt', content: 'test content' }];
            expect(async () => await generateZip(files)).not.toThrow();
        });
    });

    describe('streamToBuffer', () => {
        it('should convert stream to buffer', async () => {
            const mockStream = {
                on: jest.fn((event, callback: any) => {
                    if (event === 'data') {
                        callback(Buffer.from('test'));
                    }
                    if (event === 'end') {
                        callback();
                    }
                    return mockStream;
                })
            };
            const result = await streamToBuffer(mockStream as any);
            expect(result instanceof ArrayBuffer).toBe(true);
        });
    });

    describe('getStaticMapOld', () => {
        afterEach(() => {
            (global as any).fetch = undefined;
        });

        it('returns static map data when fetch ok and auto center', async () => {
            (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
            const result = await getStaticMapOld(undefined, undefined, undefined);
            expect((result as any).mimeType).toBe('image/png');
        });

        it('includes center when provided and handles non-png format', async () => {
            (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
            const result = await getStaticMapOld(["size:small|color:red|label:1|0,0"], { latitude: 10, longitude: 20 }, '' as any);
            expect((result as any).source).toBeDefined();
            const url = (global as any).fetch.mock.calls[0][0] as string;
            expect(url).toContain('format=png');
        });

        it('returns empty object on http error with text', async () => {
            (global as any).fetch = jest.fn().mockResolvedValue({ ok: false, status: 500, text: async () => 'fail' } as never);
            const result = await getStaticMapOld([], null, 'png');
            expect(result).toEqual({});
        });

        it('returns empty object when fetch rejects', async () => {
            (global as any).fetch = (jest.fn() as any).mockRejectedValue(new Error('network'));
            const result = await getStaticMapOld([], null, 'png');
            expect(result).toEqual({});
        });
    });

    describe('removeSpecialCharsFromFilename edge cases', () => {
        it('returns fallback when filename becomes empty', () => {
            const result = removeSpecialCharsFromFilename('@@@###');
            expect(result).toBe('unnamed_file');
        });
    });

    describe('streamToBuffer error', () => {
        it('propagates stream error', async () => {
            const mockStream = {
                on: jest.fn((event, callback: any) => {
                    if (event === 'error') callback(new Error('boom'));
                    return mockStream;
                })
            };
            await expect(streamToBuffer(mockStream as any)).rejects.toThrow('boom');
        });
    });

    describe('buildStaticMarkerSignature', () => {
        it('should build marker signature', () => {
            const result = buildStaticMarkerSignature(0, 0);
            expect(typeof result).toBe('string');
        });
    });

    describe('generateTimeSeries', () => {
        it('should generate time series data', () => {
            const result = generateTimeSeries(1714857600, 1714857600);
            expect(result).toBeDefined();
        });
    });

    describe('getUnitIdsFromVessel', () => {
        it('should throw error when vessel is undefined', () => {
            expect(() => getUnitIdsFromVessel(undefined as any)).toThrow('Vessel is required');
        });

        it('should throw error when vessel units_history is undefined', () => {
            const vessel = { units_history: undefined };
            expect(() => getUnitIdsFromVessel(vessel as any)).toThrow('Vessel units_history is required and must be an array');
        });

        it('should extract unit IDs from vessel', () => {
            const vessel = { units_history: [{ unit_id: 'unit1' }, { unit_id: 'unit2' }] };
            const result = getUnitIdsFromVessel(vessel as any);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('findVesselByUnitHistory', () => {
        it('should find vessel by unit history', () => {
            const vessels = [{ _id: 'vessel1', unit_ids: ['unit1'] }];
            const result = findVesselByUnitHistory(vessels as any, 1000, 2000, 'unit1');
            expect(result).toBeDefined();
        });
    });

    describe('groupArtifactsByDuplicateIndex branches', () => {
        it('handles missing duplication_index by continue', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: {} },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: { duplication_index: 0.9 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, undefined);
            expect(Array.isArray(res)).toBe(true);
        });

        it('breaks inner loop when duplication_index is 0', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: { duplication_index: 0 } },
                { _id: 'a3', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, 0.5);
            expect(res.length).toBeGreaterThan(0);
        });
    });

    describe('groupArtifactsByDuplicateIndex - full coverage', () => {
        it('pushes group and continues when current duplication_index < threshold', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 0.5 } },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, 0.7);
            expect(res.find((g: any) => g.includes('a1'))).toBeDefined();
            expect(res.find((g: any) => g.includes('a2'))).toBeDefined();
        });

        it('continues inner loop when onboard_vessel_id differs', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
                { _id: 'a2', onboard_vessel_id: 'v2', portal: { duplication_index: 1 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, 0.7);
            expect(res.length).toBe(2);
        });

        it('breaks inner loop when next has no duplication_index', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: {} },
                { _id: 'a3', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, 0.5);
            expect(res.length).toBeGreaterThanOrEqual(2);
        });

        it('includes next at threshold boundary and breaks at lower', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 1.0 } },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: { duplication_index: 0.8 } },
                { _id: 'a3', onboard_vessel_id: 'v1', portal: { duplication_index: 0.6 } },
            ];
            const res = groupArtifactsByDuplicateIndex(artifacts as any, 0.8);
            const group = res.find((g: any) => g.includes('a1'));
            expect(group).toEqual(expect.arrayContaining(['a1','a2']));
        });

        it('skips already processed current artifacts', () => {
            const artifacts = [
                { _id: 'a1', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
                { _id: 'a2', onboard_vessel_id: 'v1', portal: { duplication_index: 1 } },
            ];
            const first = groupArtifactsByDuplicateIndex(artifacts as any, 0.5);
            const second = groupArtifactsByDuplicateIndex(artifacts as any, 0.5);
            expect(first.length).toBeGreaterThan(0);
            expect(second.length).toBe(first.length);
        });
    });

    describe('splitByMonthsUTC', () => {
        it('should split data by months UTC', () => {
            const result = splitByMonthsUTC('2023-01-01', '2023-02-01');
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('getLocationsCollections full path', () => {
        it('lists collections for given date range and filters non-date/system', async () => {
            const collections = [
                { name: 'system.indexes' },
                { name: '2023-01' },
                { name: 'invalid' },
                { name: '2023-02' },
            ];
            const mockConn: any = {
                db: {
                    listCollections: () => ({ toArray: async () => collections }),
                },
                collection: (name: string) => ({ name }),
            };
            const res = await getLocationsCollections(mockConn, '2023-01-01', '2023-03-01');
            expect(Array.isArray(res)).toBe(true);
            expect(res.map((c: any) => c.name)).toEqual(expect.arrayContaining(['2023-01', '2023-02']));
        });

        it('should throw error when locations is undefined', async () => {
            await expect(getLocationsCollections(undefined as any, undefined as any, undefined as any)).rejects.toThrow('Invalid locations provided');
        });

        it('returns all collections when timestamps not provided', async () => {
            const collections = [ { name: '2023-01-01' }, { name: 'system.indexes' } ];
            const mockConn: any = {
                db: { listCollections: () => ({ toArray: async () => collections }) },
                collection: (name: string) => ({ name }),
            };
            const res = await getLocationsCollections(mockConn as any, undefined as any, undefined as any);
            expect(res.length).toBeGreaterThan(0);
        });

        it('throws when invalid timestamps provided', async () => {
            const mockConn: any = { db: { listCollections: () => ({ toArray: async () => [] }) }, collection: (n: string) => ({ n }) };
            await expect(getLocationsCollections(mockConn, 'bad', 'also-bad')).rejects.toThrow('must be valid dates');
        });

        it('throws when start after end', async () => {
            const mockConn: any = { db: { listCollections: () => ({ toArray: async () => [] }) }, collection: (n: string) => ({ n }) };
            await expect(getLocationsCollections(mockConn, '2023-03-01', '2023-01-01')).rejects.toThrow('before endTimestamp');
        });
    });

    describe('number/date helpers boundaries', () => {
        it('fileNameTimestamp returns formatted string', () => {
            const ts = fileNameTimestamp();
            expect(typeof ts).toBe('string');
        });

        it('generateTimeSeries with interval > 1', () => {
            const res = generateTimeSeries(Date.now(), Date.now() + 60000, 2);
            expect(typeof res).toBe('object');
        });
    });

    describe('canAccessVessel error cases', () => {
        it('should throw error when vessel_id is missing', () => {
            const user = { allowed_vessels: [], permissions: [] };
            const vessel = { _id: null, is_active: true, region_group_id: 'region1' };
            expect(() => canAccessVessel({ user }, vessel as any)).toThrow('vessel _id is required');
        });

        it('should throw error when is_active is undefined', () => {
            const user = { allowed_vessels: [], permissions: [] };
            const vessel = { _id: 'vessel1', is_active: undefined, region_group_id: 'region1' };
            expect(() => canAccessVessel({ user }, vessel as any)).toThrow('vessel is_active is required');
        });

        it('should throw error when user is undefined', () => {
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            expect(() => canAccessVessel({ api_key: {} as any }, vessel as any)).toThrow('allowed_vessels is a required array');
        });

        it('should throw error when neither user nor api_key provided', () => {
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            expect(() => canAccessVessel({}, vessel as any)).toThrow('User or api_key is a required object');
        });

        it('should throw error when user permissions is not array', () => {
            const user = { allowed_vessels: [], permissions: 'invalid' };
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            expect(() => canAccessVessel({ user }, vessel as any)).toThrow('User permissions is a required array');
        });

        it('should throw error when allowed_vessels is not array', () => {
            const user = { allowed_vessels: 'invalid', permissions: [] };
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: 'region1' };
            expect(() => canAccessVessel({ user }, vessel as any)).toThrow('allowed_vessels is a required array');
        });

        it('should return false when vessel is inactive', () => {
            const user = { allowed_vessels: ['vessel1'], permissions: [] };
            const vessel = { _id: 'vessel1', is_active: false, region_group_id: 'region1' };
            const result = canAccessVessel({ user }, vessel as any);
            expect(result).toBe(false);
        });

        it('should return false for API key with unallowed region group', () => {
            const api_key = { allowed_vessels: ['vessel1'] };
            const vessel = { _id: 'vessel1', is_active: true, region_group_id: '681c253f9f43051a7748b2c1' };
            const result = canAccessVessel({ api_key }, vessel as any);
            expect(result).toBe(false);
        });
    });

    describe('userHasPermissions error cases', () => {
        it('should throw error when permissions is not array', () => {
            const user = { permissions: 'invalid' };
            expect(() => userHasPermissions(user as any, [1, 2])).toThrow('User permissions is a required array');
        });

        it('should throw error when permissionIds is not array', () => {
            const user = { permissions: [{ permission_id: 1 }] };
            expect(() => userHasPermissions(user, 'invalid' as any)).toThrow('Permission IDs is a required array');
        });

        it('should handle OR operation', () => {
            const user = { permissions: [{ permission_id: 1 }] };
            const result = userHasPermissions(user, [1, 2], 'OR');
            expect(result).toBe(true);
        });
    });

    describe('getSessionsByCoordinates edge cases', () => {
        it('should handle empty coordinates array', () => {
            const result = getSessionsByCoordinates([]);
            expect(result).toHaveLength(0);
        });

        it('should handle coordinates with same timestamp', () => {
            const coordinates = [
                { timestamp: '2023-01-01T10:00:00Z' },
                { timestamp: '2023-01-01T10:00:00Z' }
            ];
            const result = getSessionsByCoordinates(coordinates);
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe('validateError edge cases', () => {
        let res;

        beforeEach(() => {
            res = {
                status: jest.fn().mockReturnThis(),
                json: jest.fn(),
            };
        });

        it('should handle MongoServerError with keyValue but no specific field', () => {
            const err = {
                name: 'MongoServerError',
                code: 11000,
                keyValue: { someField: 'value' },
            };
            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(400);
        });

        it('should handle MongoServerError with message property', () => {
            const err = {
                name: 'MongoServerError',
                code: 12345,
                message: 'Custom error message',
            };
            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unexpected error occured: Custom error message' });
        });
    });

    describe('generateUnsubscribeToken error cases', () => {
        it('should throw error when JWT_SECRET is not set', () => {
            delete process.env.JWT_SECRET;
            expect(() => generateUnsubscribeToken('<EMAIL>', 'alert123')).toThrow('JWT_SECRET is not set');
        });
    });

    describe('generateInvitationLink error cases', () => {
        it('should throw error when JWT_SECRET is not set', () => {
            delete process.env.JWT_SECRET;
            expect(() => generateInvitationLink('<EMAIL>', 'role123', [], 'admin456', 'user', 'Org', 'org123')).toThrow('JWT_SECRET is not set');
        });
    });

    describe('findVesselByUnitHistory - full coverage', () => {
        const UNIT = 'unit-123';

        it('returns null when vessels have no units_history array', () => {
            const vessels = [{ _id: 'v1' }, { _id: 'v2', units_history: 'not-array' }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toBeNull();
        });

        it('matches when numeric timestamps overlap range', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 500, unmount_timestamp: 3000 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });

        it('matches when unmount_timestamp is undefined (ongoing)', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 1000, unmount_timestamp: undefined },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1100, 2000, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });

        it('matches when mount equals toTs (boundary)', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 2000, unmount_timestamp: 3000 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });

        it('matches when unmount equals fromTs (boundary)', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 1000, unmount_timestamp: 1500 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1500, 1600, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });

        it('does not match when unit_id differs', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: 'other', mount_timestamp: 500, unmount_timestamp: 3000 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toBeNull();
        });

        it('does not match when history ends before range', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 100, unmount_timestamp: 900 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toBeNull();
        });

        it('does not match when history starts after range', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: 3000, unmount_timestamp: 4000 },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, 1000, 2000, UNIT);
            expect(res).toBeNull();
        });

        it('matches with date string timestamps and null unmount', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: '2024-01-01T00:00:00Z', unmount_timestamp: null },
                ],
            }];
            const from = new Date('2024-01-02T00:00:00Z');
            const to = new Date('2024-01-03T00:00:00Z');
            const res = findVesselByUnitHistory(vessels as any, from, to, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });

        it('matches when unmount string date is after range', () => {
            const vessels = [{
                _id: 'v1',
                units_history: [
                    { unit_id: UNIT, mount_timestamp: '2024-01-01T00:00:00Z', unmount_timestamp: '2024-02-01T00:00:00Z' },
                ],
            }];
            const res = findVesselByUnitHistory(vessels as any, '2024-01-15T00:00:00Z' as any, '2024-01-20T00:00:00Z' as any, UNIT);
            expect(res).toEqual({ vessel: vessels[0], unit_id: UNIT });
        });
    });

});
