import { jest } from '@jest/globals';

const mockJest = jest.fn as any;

const mockApiEndpoint = {
    find: mockJest().mockResolvedValue([]),
    findOne: mockJest().mockResolvedValue(null),
    findById: mockJest().mockResolvedValue(null),
    findOneAndUpdate: mockJest().mockResolvedValue(null),
    findOneAndDelete: mockJest().mockResolvedValue(null),
    create: mockJest().mockResolvedValue({}),
    updateOne: mockJest().mockResolvedValue({ modifiedCount: 1 }),
    updateMany: mockJest().mockResolvedValue({ modifiedCount: 1 }),
    deleteOne: mockJest().mockResolvedValue({ deletedCount: 1 }),
    deleteMany: mockJest().mockResolvedValue({ deletedCount: 1 }),
    aggregate: mockJest().mockResolvedValue([]),
    countDocuments: mockJest().mockResolvedValue(0),
    distinct: mockJest().mockResolvedValue([]),
    save: mockJest().mockResolvedValue({})
};

export default mockApiEndpoint;
