import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext, expectUnauthorizedResponse, expectServerErrorResponse, expectNextCalled, expectNextNotCalled, silenceConsole } from '../mocks/middlewares/auth.mock';

const mockGetUser = jest.fn() as any;
const mockApiKeyModel = { findOne: jest.fn() as any };

class MockJsonWebTokenError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'JsonWebTokenError';
    }
}

class MockTokenExpiredError extends MockJsonWebTokenError {
    expiredAt: Date;
    constructor(message: string, expiredAt: Date) {
        super(message);
        this.name = 'TokenExpiredError';
        this.expiredAt = expiredAt;
    }
}

jest.mock('jsonwebtoken', () => ({
    verify: jest.fn(),
    JsonWebTokenError: MockJsonWebTokenError,
    TokenExpiredError: MockTokenExpiredError
}));

jest.mock('../../queries/User', () => ({ __esModule: true, getUser: mockGetUser }));
jest.mock('../../models/ApiKey', () => ({ __esModule: true, default: mockApiKeyModel }));

describe('Auth Middleware', () => {
    let req: any;
    let res: any;
    let next: jest.MockedFunction<any>;
    let restoreConsole: any;
    let isAuthenticated: any;

    beforeEach(async () => {
        jest.clearAllMocks();
        
        req = createMockRequest({
            method: 'GET',
            originalUrl: '/test',
            _endpoint_id: 'test-endpoint'
        });

        res = createMockResponse();
        next = createMockNext();
        
        process.env.JWT_SECRET = 'test-jwt-secret';
        restoreConsole = silenceConsole();

        const jwt = require('jsonwebtoken');
        
        jwt.verify.mockImplementation((token: string, _secret: string) => {
            if (token === 'valid-user-token') {
                return { user_id: 'user123' };
            }
            if (token === 'valid-api-token') {
                return { api_key_id: 'apikey123' };
            }
            if (token === 'invalid-token') {
                throw new MockJsonWebTokenError('Invalid token');
            }
            if (token === 'expired-token') {
                throw new MockTokenExpiredError('Token expired', new Date());
            }
            if (token === 'unexpected-token') {
                return { unexpectedKey: 'value' };
            }
            if (token === 'generic-error-token') {
                throw new Error('Generic error');
            }
            if (token === 'non-error-token') {
                throw 'String error';
            }
            throw new Error('Generic error');
        });
        
        delete require.cache[require.resolve('../../middlewares/auth')];
        const authModule = await import('../../middlewares/auth');
        isAuthenticated = authModule.default;
    });

    afterEach(() => {
        jest.clearAllMocks();
        delete process.env.JWT_SECRET;
        if (restoreConsole) restoreConsole();
    });

    it('should return 401 if authorization header is missing', async () => {
        req.header.mockReturnValue(undefined);
        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Unauthorized');
        expectNextNotCalled(next);
    });

    it('should return 401 if authorization header does not start with Bearer', async () => {
        req.header.mockReturnValue('Basic invalid-token');
        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token type must be Bearer');
        expectNextNotCalled(next);
    });

    it('should return 401 if Bearer token is empty', async () => {
        req.header.mockReturnValue('Bearer ');
        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token is invalid');
        expectNextNotCalled(next);
    });

    it('should authenticate user successfully with valid JWT token', async () => {
        req.header.mockReturnValue('Bearer valid-user-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: false,
            jwt_tokens: ['valid-user-token']
        };

        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(mockGetUser).toHaveBeenCalledWith({
            user_id: 'user123',
            includeUnprojected: true
        });
        expect(req.user).toEqual(mockUser);
        expectNextCalled(next);
    });

    it('should return 401 if user account is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-user-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: true,
            jwt_tokens: ['valid-user-token']
        };

        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Your account has been deleted.');
        expectNextNotCalled(next);
    });

    it('should return 401 if token is not available in user tokens', async () => {
        req.header.mockReturnValue('Bearer valid-user-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: false,
            jwt_tokens: ['other-token']
        };

        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token is not available.');
        expectNextNotCalled(next);
    });

    it('should return 500 for unexpected JWT response', async () => {
        req.header.mockReturnValue('Bearer unexpected-token');
        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: JWT token returned unexpected data');
        expectNextNotCalled(next);
    });

    it('should return 401 if JWT token is invalid', async () => {
        req.header.mockReturnValue('Bearer invalid-token');
        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token is invalid');
        expectNextNotCalled(next);
    });

    it('should return 401 if JWT token is expired', async () => {
        req.header.mockReturnValue('Bearer expired-token');
        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token is invalid');
        expectNextNotCalled(next);
    });

    it('should handle generic error in JWT verification', async () => {
        req.header.mockReturnValue('Bearer generic-error-token');
        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: Generic error');
        expectNextNotCalled(next);
    });

    it('should handle non-Error object thrown in JWT verification', async () => {
        req.header.mockReturnValue('Bearer non-error-token');
        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: String error');
        expectNextNotCalled(next);
    });

    it('should handle user not found', async () => {
        req.header.mockReturnValue('Bearer valid-user-token');
        mockGetUser.mockResolvedValue(null);

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: Cannot read properties of null (reading \'is_deleted\')');
        expectNextNotCalled(next);
    });

    it('should handle getUser throwing an error', async () => {
        req.header.mockReturnValue('Bearer valid-user-token');
        mockGetUser.mockRejectedValue(new Error('Database error'));

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: Database error');
        expectNextNotCalled(next);
    });

    it('should handle missing JWT secret', async () => {
        delete process.env.JWT_SECRET;
        req.header.mockReturnValue('Bearer valid-user-token');
        
        const jwt = require('jsonwebtoken');
        jwt.verify.mockImplementation(() => {
            throw new MockJsonWebTokenError('Secret key required');
        });

        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'Token is invalid');
        expectNextNotCalled(next);
    });

    it('should authenticate API key successfully', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: {} as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockResolvedValue({} as never),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(mockApiKeyModel.findOne).toHaveBeenCalledWith({ _id: 'apikey123' });
        expect(mockApiKey.requests).toBe(6);
        expect(mockApiKey.requests_endpoints['test-endpoint']).toBe(1);
        expect(mockApiKey.last_used).toBeInstanceOf(Date);
        expect(mockApiKey.markModified).toHaveBeenCalledWith('requests_endpoints');
        expect(mockApiKey.save).toHaveBeenCalled();
        expect(req.api_key).toEqual(mockApiKey.toObject());
        expectNextCalled(next);
    });

    it('should return 401 if API key is not found', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        mockApiKeyModel.findOne.mockResolvedValue(null);

        await isAuthenticated(req, res, next);
        expectUnauthorizedResponse(res, 'API key is invalid');
        expectNextNotCalled(next);
    });

    it('should return 400 if API key is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: true,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint']
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your API key has been removed. Please contact an administrator'
        });
        expectNextNotCalled(next);
    });

    it('should return 400 if API key is revoked', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: true,
            allowed_endpoints: ['test-endpoint']
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your access has been revoked. Please contact an administrator'
        });
        expectNextNotCalled(next);
    });

    it('should return 403 if API key does not allow access to endpoint', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['other-endpoint']
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.json).toHaveBeenCalledWith({ message: 'You cannot access this resource' });
        expectNextNotCalled(next);
    });

    it('should handle API key with existing request count for endpoint', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: { 'test-endpoint': 3 } as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockResolvedValue({} as never),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(mockApiKey.requests).toBe(6);
        expect(mockApiKey.requests_endpoints['test-endpoint']).toBe(4);
        expectNextCalled(next);
    });

    it('should handle API key with null requests_endpoints (500 error)', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: null as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockResolvedValue({} as never),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, "Unexpected error occured: Cannot read properties of null (reading 'test-endpoint')");
        expectNextNotCalled(next);
    });

    it('should handle API key with undefined requests_endpoints (500 error)', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: undefined as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockResolvedValue({} as never),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, "Unexpected error occured: Cannot read properties of undefined (reading 'test-endpoint')");
        expectNextNotCalled(next);
    });

    it('should handle API key save error', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: {} as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockRejectedValue(new Error('Save failed') as never),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: Save failed');
        expectNextNotCalled(next);
    });

    it('should handle API key findOne throwing an error', async () => {
        req.header.mockReturnValue('Bearer valid-api-token');
        mockApiKeyModel.findOne.mockRejectedValue(new Error('Database error'));

        await isAuthenticated(req, res, next);
        expectServerErrorResponse(res, 'Unexpected error occured: Database error');
        expectNextNotCalled(next);
    });
});