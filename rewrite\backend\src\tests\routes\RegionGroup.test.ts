import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import regionGroupService from '../../services/RegionGroup.service';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/RegionGroup.service', () => require('../mocks/services/regionGroup.mock'));

describe('RegionGroup API', () => {
    describe('GET /api/regionGroups', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/regionGroups');
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).get('/api/regionGroups').set('Authorization', nonAuthToken);
                    expect([401, 403, 200]).toContain(res.status);
                });

                it('should return 200 and list region groups for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.find as jest.Mock).mockResolvedValueOnce([{ _id: '1', name: 'Test Group', timezone: '+08:00', created_by: { _id: '1', name: 'Test User' }, vessels: [] }] as never);
                    const res = await request(app).get('/api/regionGroups').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                    expect(res.body[0]).toHaveProperty('_id');
                    expect(res.body[0]).toHaveProperty('name');
                    expect(res.body[0]).toHaveProperty('timezone');
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (regionGroupService.find as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).get('/api/regionGroups').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/regionGroups', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/regionGroups').send({ name: 'Test Group', timezone: '+08:00' });
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups').set('Authorization', nonAuthToken).send({ name: 'Test Group', timezone: '+08:00' });
                    expect([401, 403]).toContain(res.status);
                });

                it('should return 400 if name is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups').set('Authorization', authToken).send({ timezone: '+08:00' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if timezone is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups').set('Authorization', authToken).send({ name: 'Test Group' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if timezone is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups').set('Authorization', authToken).send({ name: 'Test Group', timezone: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and create region group for authorized user; 500 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.create as jest.Mock).mockResolvedValueOnce({ _id: '1', name: 'Test Group', timezone: '+08:00', created_by: { _id: '1', name: 'Test User' }, vessels: [] } as never);
                    const res = await request(app).post('/api/regionGroups').set('Authorization', authToken).send({ name: 'Test Group', timezone: '+08:00' });
                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(res.body).toHaveProperty('message');
                        expect(res.body).toHaveProperty('regionGroup');
                    } else {
                        expect(res.status).toBe(500);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (regionGroupService.create as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).post('/api/regionGroups').set('Authorization', authToken).send({ name: 'Test Group', timezone: '+08:00' });
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/regionGroups/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').send({ name: 'Updated Group' });
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', nonAuthToken).send({ name: 'Updated Group' });
                    expect([401, 403]).toContain(res.status);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups/invalid').set('Authorization', authToken).send({ name: 'Updated Group' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if name is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ name: '' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if timezone is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ timezone: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and update region group for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.update as jest.Mock).mockResolvedValueOnce({ _id: '507f1f77bcf86cd799439011', name: 'Updated Group', timezone: '+08:00', created_by: { _id: '1', name: 'Test User' }, vessels: [] } as never);
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ name: 'Updated Group' });
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('message');
                    expect(res.body).toHaveProperty('regionGroup');
                });

                it('should return 400 if region group does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.update as jest.Mock).mockResolvedValueOnce(null as never);
                    const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ name: 'Updated Group' });
                    expect(res.status).toBe(400);
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (regionGroupService.update as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).post('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ name: 'Updated Group' });
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/regionGroups/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/regionGroups/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).delete('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', nonAuthToken);
                    expect([401, 403]).toContain(res.status);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/regionGroups/invalid').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 200 and delete region group for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.delete as jest.Mock).mockResolvedValueOnce(true as never);
                    const res = await request(app).delete('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('message');
                });

                it('should return 400 if region group does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (regionGroupService.delete as jest.Mock).mockResolvedValueOnce(false as never);
                    const res = await request(app).delete('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (regionGroupService.delete as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).delete('/api/regionGroups/507f1f77bcf86cd799439011').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
