import React from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Box,
    Alert,
    alpha,
} from "@mui/material";
import { Security as SecurityIcon } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import theme from "../theme";

const TwoFactorRequiredModal = ({ open, onClose }) => {
    const navigate = useNavigate();

    const handleGoToSettings = () => {
        onClose();
        navigate("/dashboard/settings", { state: { highlight2FA: true } });
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    color: "#FFFFFF",
                    borderRadius: "12px",
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                },
            }}
        >
            <DialogTitle sx={{ color: "#FFFFFF", fontSize: "24px", fontWeight: 600, textAlign: "center", pb: 1 }}>
                <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                    <SecurityIcon sx={{ color: theme.palette.custom.mainBlue, fontSize: "28px" }} />
                    Two-Factor Authentication Required
                </Box>
            </DialogTitle>

            <DialogContent sx={{ px: 3, pb: 1, "& .MuiPaper-root": { mb: 0 } }}>
                <Alert
                    severity="warning"
                    sx={{
                        mb: 3,
                        backgroundColor: alpha(theme.palette.warning.main, 0.1),
                        border: `1px solid ${theme.palette.warning.main}`,
                        "& .MuiAlert-icon": {
                            color: theme.palette.warning.main,
                        },
                        "& .MuiAlert-message": {
                            color: "#FFFFFF",
                        }
                    }}
                >
                    <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
                        Two-factor authentication is required for your account.
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                        Please enable 2FA in your settings to keep your account secure.
                    </Typography>
                </Alert>
            </DialogContent>

            <DialogActions sx={{ px: 3, pb: 3, gap: 1 }}>
                <Button
                    onClick={onClose}
                    variant="outlined"
                    sx={{
                        color: "#FFFFFF",
                        borderColor: alpha("#FFFFFF", 0.3),
                        fontSize: "14px",
                        fontWeight: 500,
                        padding: "8px 24px",
                        borderRadius: "8px",
                        textTransform: "none",
                        minWidth: "100px",
                        "&:hover": {
                            borderColor: "#FFFFFF",
                            backgroundColor: alpha("#FFFFFF", 0.08),
                        }
                    }}
                >
                    Close
                </Button>
                <Button
                    onClick={handleGoToSettings}
                    variant="contained"
                    sx={{
                        fontSize: "14px",
                        fontWeight: 600,
                        padding: "8px 24px",
                        borderRadius: "8px",
                        textTransform: "none",
                        minWidth: "140px",
                        backgroundColor: theme.palette.custom.mainBlue,
                        color: "#FFFFFF",
                        boxShadow: `0 4px 12px ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                        "&:hover": {
                            backgroundColor: "#2c5cc5",
                            boxShadow: `0 6px 16px ${alpha(theme.palette.custom.mainBlue, 0.4)}`,
                            transform: "translateY(-1px)",
                        },
                        "&:active": {
                            transform: "translateY(0)",
                        }
                    }}
                >
                    Enable 2FA
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default TwoFactorRequiredModal;
