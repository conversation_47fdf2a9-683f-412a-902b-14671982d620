import VesselOnlineLookup from "../models/VesselOnlineLookup";
import { IVesselOnlineLookup } from "../interfaces/VesselOnlineLookup";
import mongoose from "mongoose";

class VesselOnlineLookupService {
    async findByVesselIds(vesselIds: string[]): Promise<IVesselOnlineLookup[]> {
        try {
            const objectIds = vesselIds.map((id) => new mongoose.Types.ObjectId(id));
            return await VesselOnlineLookup.find({ vessel_id: { $in: objectIds } });
        } catch (error) {
            console.error("Error fetching vessel online lookup data:", error);
            return [];
        }
    }
}

const vesselOnlineLookupService = new VesselOnlineLookupService();
export default vesselOnlineLookupService;
