import mongoose from 'mongoose';
import { IVessel } from '../../interfaces/Vessel';
import { ILocationOptimized } from '../../interfaces/VesselLocation';

export const vesselsList: IVessel[] = [
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
        unit_id: 'prototype-37',
        name: 'Vessel Alpha',
        thumbnail_s3_key: 'thumbnail-alpha.jpg',
        is_active: true,
        region_group_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439012'),
        home_port_location: { type: 'Point', coordinates: [10.0, 20.0] },
        created_by: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
        creation_timestamp: new Date(),
        thumbnail_compressed_s3_key: 'thumbnail-alpha.jpg',
        units_history: [],
    },
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439013'),
        unit_id: 'prototype-38',
        name: 'Vess<PERSON> Beta',
        thumbnail_s3_key: 'thumbnail-beta.jpg',
        is_active: true,
        region_group_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439014'),
        home_port_location: { type: 'Point', coordinates: [30.0, 40.0] },
        created_by: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
        creation_timestamp: new Date(),
        thumbnail_compressed_s3_key: 'thumbnail-beta.jpg',
        units_history: [],
    },
];

export const locationsList: ILocationOptimized[] = [
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439021'),
        location: { type: 'Point', coordinates: [117.2075473, 8.333152] },
        timestamp: new Date('2024-09-15T22:29:20.555Z'),
        groundSpeed: 0.023,
        isStationary: false,
        metadata: {
            onboardVesselId: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
            unitId: 'prototype-37',
        },
        headingMotion: 0,
        details: { details: 'details' },
        accuracyHeading: 0,
    },
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439022'),
        location: { type: 'Point', coordinates: [117.2075474, 8.333153] },
        timestamp: new Date('2024-09-15T22:30:20.555Z'),
        groundSpeed: 0.025,
        isStationary: false,
        metadata: {
            onboardVesselId: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
            unitId: 'prototype-37',
        },
        headingMotion: 0,
        details: { details: 'details' },
        accuracyHeading: 0,
    },
];

export const lastKnownLocation = {
    _id: '507f1f77bcf86cd799439021',
    longitude: 117.2075473,
    latitude: 8.333152,
    timestamp: '2024-09-15T22:29:20.555Z',
    groundSpeed: 0.023,
    isStationary: false,
};

export const closestLocation = {
    _id: '507f1f77bcf86cd799439021',
    location: { type: 'Point', coordinates: [117.2075473, 8.333152] },
    timestamp: '2024-09-15T22:29:20.555Z',
    groundSpeed: 0.023,
    isStationary: false,
    metadata: {
        onboardVesselId: '507f1f77bcf86cd799439011',
        unitId: 'prototype-37',
    },
};
