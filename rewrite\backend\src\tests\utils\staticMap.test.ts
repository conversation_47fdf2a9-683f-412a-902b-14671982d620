import { jest, describe, it, expect, beforeEach } from '@jest/globals';
import { generateNumberedIconBuffer, getClustersAndPoints, getStaticMap, getStaticMapWithClustersUrl, getCenterFromPointsVector } from '../../utils/staticMap';

jest.mock('geodesy/latlon-nvector-ellipsoidal.js', () => {
    let options = { throwCtor: false, throwToNvector: false, throwToLatLon: false };
    class LatLon_NvectorEllipsoidalMock {
        lat: number; lon: number;
        constructor(lat: number, lon: number) {
            if (options.throwCtor || lat === -999 || lon === -999) throw new Error('ctor error');
            this.lat = lat; this.lon = lon;
        }
        toNvector() {
            if (options.throwToNvector) throw new Error('toNvector error');
            return new NvectorMock(1, 1, 1);
        }
    }
    class NvectorMock {
        x: number; y: number; z: number;
        constructor(x: number, y: number, z: number) { this.x = x; this.y = y; this.z = z; }
        plus(v: any) { return new NvectorMock(this.x + (v?.x ?? 0), this.y + (v?.y ?? 0), this.z + (v?.z ?? 0)); }
        times() { return this; }
        toLatLon() { if (options.throwToLatLon) throw new Error('toLatLon error'); return { lat: 1, lon: 1 }; }
    }
    function __setGeodesyMockOptions(next: Partial<typeof options>) { options = { ...options, ...next }; }
    function __resetGeodesyMockOptions() { options = { throwCtor: false, throwToNvector: false, throwToLatLon: false }; }
    return { __esModule: true, default: LatLon_NvectorEllipsoidalMock, Nvector: NvectorMock, __setGeodesyMockOptions, __resetGeodesyMockOptions };
});

jest.mock('sharp', () => ({
    __esModule: true,
    default: jest.fn(() => ({
        png: () => ({
            toBuffer: async () => Buffer.from('ok')
        })
    }))
}));

jest.mock('@mapbox/geo-viewport', () => ({
    __esModule: true,
    default: { bounds: jest.fn(() => [0, 0, 1, 1]) }
}));

jest.mock('supercluster', () => ({
    __esModule: true,
    default: class SuperclusterMock {
        load() {}
        getClusters() {
            return [
                { geometry: { coordinates: [10, 20] }, properties: { cluster: true, point_count: 5 } },
                { geometry: { coordinates: [11, 21] }, properties: {} }
            ];
        }
    }
}));

describe('staticMap utils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (global as any).fetch = jest.fn();
        process.env.API_URL = 'http://api.example.com';
        process.env.GOOGLE_API_KEY = 'key';
    });

    it('generateNumberedIconBuffer returns a PNG buffer for numeric counts', async () => {
        const buf = await generateNumberedIconBuffer(3, 'red', 'white');
        expect(Buffer.isBuffer(buf)).toBe(true);
    });

    it('getClustersAndPoints returns clusters and points when center provided', async () => {
        const points = [{ lat: 1, lng: 2 }];
        const res = await getClustersAndPoints(points as any, { centerLat: 1, centerLng: 1, zoom: 3, width: 800, height: 400 });
        expect(res).toBeDefined();
        expect(Array.isArray(res)).toBe(true);
        expect((res as any).length).toBeGreaterThan(0);
    });

    it('getStaticMap returns mime and source when fetch ok', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: true, point_count: 2 } }
        ];
        const res = await getStaticMap(clusters, 'png');
        expect((res as any).mimeType).toBe('image/png');
        expect((res as any).source).toBeDefined();
    });

    it('getStaticMap builds URL with center and markers', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: false } },
            { geometry: { coordinates: [11, 21] }, properties: { cluster: true, point_count: 3 } }
        ];
        process.env.API_URL = 'http://api.local';
        await getStaticMap(clusters, 'png');
        const url = (global as any).fetch.mock.calls[0][0] as string;
        expect(url).toContain('auto=');
        expect(url).toContain('markers=');
        expect(url).toContain('size=800x400');
    });

    it('getStaticMap returns empty object on error response', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: false, status: 403, text: async () => 'forbidden' } as never);
        const clusters: any = [];
        const res = await getStaticMap(clusters, 'png');
        expect(res).toEqual({});
    });

    it('getStaticMap handles fetch error', async () => {
        (global as any).fetch = jest.fn().mockRejectedValue(new Error('Network error') as never);
        const clusters: any = [];
        const res = await getStaticMap(clusters, 'png');
        expect(res).toEqual({});
    });

    it('getStaticMap handles empty clusters array', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const res = await getStaticMap([], 'png');
        expect(res).toEqual({ mimeType: 'image/png', source: { ok: true } });
    });

    it('getStaticMap handles different image formats', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: true, point_count: 2 } }
        ];
        await getStaticMap(clusters, 'jpg');
        await getStaticMap(clusters, 'webp');
        expect((global as any).fetch).toHaveBeenCalledTimes(2);
    });

    it('getClustersAndPoints handles empty points array', async () => {
        const result = await getClustersAndPoints([], undefined);
        expect(result).toBeUndefined();
    });

    it('getClustersAndPoints handles points without center', async () => {
        const points = [{ lat: 1, lng: 2 }];
        const result = await getClustersAndPoints(points as any, { zoom: 3, width: 800, height: 400 });
        expect(Array.isArray(result)).toBe(true);
    });

    describe('getCenterFromPointsVector via getClustersAndPoints', () => {
        let geodesyMock: any;
        beforeEach(() => {
            geodesyMock = jest.requireMock('geodesy/latlon-nvector-ellipsoidal.js');
            geodesyMock.__resetGeodesyMockOptions();
        });

        it('returns null for empty input', async () => {
            const res = await getClustersAndPoints([], { zoom: 3, width: 800, height: 400 } as any);
            expect(res).toBeUndefined();
        });

        it('returns null for single invalid point', async () => {
            const res = await getClustersAndPoints([{ lat: NaN, lng: 10 }] as any, { zoom: 3, width: 800, height: 400 } as any);
            expect(res).toBeDefined();
        });

        it('returns center for single valid point', async () => {
            const res = await getClustersAndPoints([{ lat: 10, lng: 20 }] as any, { width: 800, height: 400 } as any);
            expect(Array.isArray(res)).toBe(true);
        });

        it('skips invalid points and computes center from valid ones', async () => {
            const res = await getClustersAndPoints([{ lat: NaN, lng: 1 }, { lat: 10, lng: 20 }] as any, { width: 800, height: 400 } as any);
            expect(Array.isArray(res)).toBe(true);
        });

        it('handles constructor error for a point', async () => {
            const res = await getClustersAndPoints([{ lat: -999, lng: 5 }, { lat: 10, lng: 20 }] as any, { width: 800, height: 400 } as any);
            expect(Array.isArray(res)).toBe(true);
        });

        it('returns null when no valid points after filtering', async () => {
            const res = await getClustersAndPoints([{ lat: NaN, lng: NaN }] as any, { width: 800, height: 400 } as any);
            expect(res).toBeDefined();
        });

        it('handles constructor error for single-point input (returns null center)', async () => {
            const res = await getClustersAndPoints([{ lat: -999, lng: 5 }] as any, { width: 800, height: 400 } as any);
            expect(res).toBeDefined();
        });

        it('skips points when toNvector throws and falls back gracefully', async () => {
            geodesyMock.__setGeodesyMockOptions({ throwToNvector: true });
            const res = await getClustersAndPoints([{ lat: 10, lng: 10 }, { lat: 15, lng: 15 }] as any, { width: 800, height: 400 } as any);
            expect(res).toBeDefined();
            geodesyMock.__resetGeodesyMockOptions();
        });

        it('handles error converting average vector back to LatLon', async () => {
            geodesyMock.__setGeodesyMockOptions({ throwToLatLon: true });
            const res = await getClustersAndPoints([{ lat: 10, lng: 10 }, { lat: 15, lng: 15 }] as any, { width: 800, height: 400 } as any);
            expect(res).toBeDefined();
            geodesyMock.__resetGeodesyMockOptions();
        });
    });

    describe('getCenterFromPointsVector direct', () => {
        let geodesyMock: any;
        beforeEach(() => {
            geodesyMock = jest.requireMock('geodesy/latlon-nvector-ellipsoidal.js');
            geodesyMock.__resetGeodesyMockOptions();
        });

        it('returns null for empty array', async () => {
            const res = await getCenterFromPointsVector([] as any);
            expect(res).toBeNull();
        });

        it('returns null for single invalid point', async () => {
            const res = await getCenterFromPointsVector([{ lat: NaN, lng: 1 }] as any);
            expect(res).toBeNull();
        });

        it('returns lat/lon for single valid point', async () => {
            const res = await getCenterFromPointsVector([{ lat: 10, lng: 20 }] as any);
            expect(res).toEqual({ lat: 10, lon: 20 });
        });

        it('returns null when constructor throws for single point', async () => {
            const res = await getCenterFromPointsVector([{ latitude: -999, longitude: 5 }] as any);
            expect(res).toBeNull();
        });

        it('returns null when all points invalid (validPointsCount === 0)', async () => {
            const res = await getCenterFromPointsVector([{ lat: NaN, lng: NaN }, { latitude: NaN, longitude: NaN }] as any);
            expect(res).toBeNull();
        });

        it('handles toLatLon error after averaging vectors', async () => {
            geodesyMock.__setGeodesyMockOptions({ throwToLatLon: true });
            const res = await getCenterFromPointsVector([{ lat: 10, lng: 20 }, { lat: 11, lng: 21 }] as any);
            expect(res).toBeNull();
            geodesyMock.__resetGeodesyMockOptions();
        });

        it('computes center for multiple valid points', async () => {
            const res = await getCenterFromPointsVector([{ lat: 10, lng: 20 }, { lat: 11, lng: 21 }] as any);
            expect(res).not.toBeNull();
        });

        it('accepts latitude/longitude keys in points', async () => {
            const res = await getCenterFromPointsVector([{ latitude: 10, longitude: 20 }, { latitude: 11, longitude: 21 }] as any);
            expect(res).not.toBeNull();
        });
    });

    it('generateNumberedIconBuffer handles different colors', async () => {
        const buf1 = await generateNumberedIconBuffer('null');
        const buf2 = await generateNumberedIconBuffer(10, 'green', 'red');
        expect(Buffer.isBuffer(buf1)).toBe(true);
        expect(Buffer.isBuffer(buf2)).toBe(true);
    });

    it('generateNumberedIconBuffer handles large numbers', async () => {
        const buf = await generateNumberedIconBuffer(999, 'purple', 'orange');
        expect(Buffer.isBuffer(buf)).toBe(true);
    });

    it('generateNumberedIconBuffer covers thousand and million label branches', async () => {
        const bufK = await generateNumberedIconBuffer(1500, 'black', 'white');
        const bufM = await generateNumberedIconBuffer(2_000_000, 'black', 'white');
        expect(Buffer.isBuffer(bufK)).toBe(true);
        expect(Buffer.isBuffer(bufM)).toBe(true);
    });

    it('generateNumberedIconBuffer surfaces sharp error path', async () => {
        const sharpMod = jest.requireMock('sharp') as any;
        const original = sharpMod.default;
        sharpMod.default = jest.fn(() => ({ png: () => ({ toBuffer: async () => { throw new Error('sharp fail'); } }) }));
        await expect(generateNumberedIconBuffer(3, 'x', 'y')).rejects.toThrow('sharp fail');
        sharpMod.default = original;
    });

    it('getStaticMap handles clusters with different properties', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: false } },
            { geometry: { coordinates: [11, 21] }, properties: { cluster: true, point_count: 3 } }
        ];
        const res = await getStaticMap(clusters, 'png');
        expect((res as any).mimeType).toBe('image/png');
    });

    it('getStaticMap handles URL build with auto center when not provided', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: false } }
        ];
        await getStaticMap(clusters, 'png');
        const url = (global as any).fetch.mock.calls[0][0] as string;
        expect(url).toContain('auto=');
    });

    it('getStaticMapWithClustersUrl includes center when provided', () => {
        process.env.API_URL = 'http://api.local';
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: false } },
        ];
        const url = getStaticMapWithClustersUrl(clusters, { centerLat: 12.34, centerLng: 56.78, width: 640, height: 360, zoom: 5, mapType: 'hybrid', format: 'png', scale: 1 } as any);
        expect(url).toContain('center=12.34%2C56.78');
        expect(url).toContain('markers=');
        expect(url).toContain('size=640x360');
    });

    it('getStaticMapWithClustersUrl with no clusters uses auto when center missing', () => {
        const url = getStaticMapWithClustersUrl([], undefined);
        expect(url).toContain('auto=');
        expect(url).not.toContain('markers=');
    });

    it('getStaticMap handles clusters with no properties', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] } }
        ];
        const res = await getStaticMap(clusters);
        expect(res).toBeDefined();
    });

    it('getStaticMap handles different zoom levels', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: true, point_count: 2 } }
        ];
        await getStaticMap(clusters, 'png');
        expect((global as any).fetch).toHaveBeenCalled();
    });

    it('getStaticMap handles clusters with missing coordinates', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { properties: { cluster: true, point_count: 2 } }
        ];
        const res = await getStaticMap(clusters, 'png');
        expect(res).toBeDefined();
    });

    it('getStaticMap handles error response with status text', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ 
            ok: false, 
            status: 500, 
            text: async () => 'Internal server error' 
        } as never);
        const clusters: any = [];
        const res = await getStaticMap(clusters, 'png');
        expect(res).toEqual({});
    });

    it('getStaticMap handles fetch rejection with specific error', async () => {
        (global as any).fetch = jest.fn().mockRejectedValue(new Error('Network timeout') as never);
        const clusters: any = [];
        const res = await getStaticMap(clusters, 'png');
        expect(res).toEqual({});
    });

    it('getStaticMap handles clusters with nested geometry', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { 
                geometry: { 
                    coordinates: [[10, 20], [11, 21]], 
                    type: 'LineString' 
                }, 
                properties: { cluster: true, point_count: 2 } 
            }
        ];
        const res = await getStaticMap(clusters, 'png');
        expect((res as any).mimeType).toBe('image/png');
    });

    it('getStaticMap handles clusters with different geometry types', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { 
                geometry: { 
                    coordinates: [10, 20], 
                    type: 'Point' 
                }, 
                properties: { cluster: false } 
            },
            { 
                geometry: { 
                    coordinates: [[10, 20], [11, 21], [12, 22]], 
                    type: 'Polygon' 
                }, 
                properties: { cluster: true, point_count: 3 } 
            }
        ];
        const res = await getStaticMap(clusters, 'png');
        expect((res as any).mimeType).toBe('image/png');
    });

    it('generateNumberedIconBuffer parses string count and medium font branch', async () => {
        const buf = await generateNumberedIconBuffer('12' as any, 'red', 'white');
        expect(Buffer.isBuffer(buf)).toBe(true);
    });

    it('generateNumberedIconBuffer hits >=100 font size branch', async () => {
        const buf = await generateNumberedIconBuffer(150, 'red', 'white');
        expect(Buffer.isBuffer(buf)).toBe(true);
    });

    it('generateNumberedIconBuffer falls back to default colors when sanitized empty', async () => {
        const buf = await generateNumberedIconBuffer(7, '' as any, '' as any);
        expect(Buffer.isBuffer(buf)).toBe(true);
    });

    it('getStaticMap uses default png when format is falsy', async () => {
        (global as any).fetch = jest.fn().mockResolvedValue({ ok: true } as never);
        const clusters: any = [
            { geometry: { coordinates: [10, 20] }, properties: { cluster: false } }
        ];
        await getStaticMap(clusters, '' as any);
        const url = (global as any).fetch.mock.calls[0][0] as string;
        expect(url).toContain('format=png');
    });
});


