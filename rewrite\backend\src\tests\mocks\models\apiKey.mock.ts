import { jest, expect } from '@jest/globals';

const mockJest = jest as any;
export const createMockApiKey = (overrides: any = {}) => ({
    _id: '507f1f77bcf86cd799439013',
    name: 'Test API Key',
    key: 'test-api-key',
    is_deleted: false,
    is_revoked: false,
    allowed_endpoints: ['test-endpoint'],
    requests: 0,
    requests_endpoints: {},
    last_used: new Date(),
    organization_id: '507f1f77bcf86cd799439012',
    created_by: '507f1f77bcf86cd799439011',
    creation_timestamp: new Date().toISOString(),
    save: mockJest.fn().mockResolvedValue({}),
    markModified: jest.fn(),
    toObject: mockJest.fn().mockReturnValue({ _id: '507f1f77bcf86cd799439013', name: 'Test API Key' }),
    ...overrides
});

export const createMockDeletedApiKey = (overrides: any = {}) => 
    createMockApiKey({ is_deleted: true, ...overrides });

export const createMockRevokedApiKey = (overrides: any = {}) => 
    createMockApiKey({ is_revoked: true, ...overrides });

export const createMockRestrictedApiKey = (allowedEndpoints: string[], overrides: any = {}) => 
    createMockApiKey({ allowed_endpoints: allowedEndpoints, ...overrides });

export const createApiKeyModelMock = () => ({
    find: mockJest.fn().mockResolvedValue([]),
    findOne: mockJest.fn().mockResolvedValue(null),
    findById: mockJest.fn().mockResolvedValue(null),
    findOneAndUpdate: mockJest.fn().mockResolvedValue(null),
    findOneAndDelete: mockJest.fn().mockResolvedValue(null),
    create: mockJest.fn().mockResolvedValue({}),
    updateOne: mockJest.fn().mockResolvedValue({ modifiedCount: 1 }),
    updateMany: mockJest.fn().mockResolvedValue({ modifiedCount: 1 }),
    deleteOne: mockJest.fn().mockResolvedValue({ deletedCount: 1 }),
    deleteMany: mockJest.fn().mockResolvedValue({ deletedCount: 1 }),
    aggregate: mockJest.fn().mockResolvedValue([]),
    countDocuments: mockJest.fn().mockResolvedValue(0),
    distinct: mockJest.fn().mockResolvedValue([]),
    save: mockJest.fn().mockResolvedValue({}),
    markModified: jest.fn(),
    toObject: mockJest.fn().mockReturnValue({}),
    toJSON: mockJest.fn().mockReturnValue({}),
    schema: {}
});

export const createMockApiKeyDocument = (data: any = {}) => {
    const apiKeyData = createMockApiKey(data);
    
    return {
        ...apiKeyData,
        save: mockJest.fn().mockResolvedValue(apiKeyData),
        remove: mockJest.fn().mockResolvedValue(apiKeyData),
        deleteOne: mockJest.fn().mockResolvedValue({ deletedCount: 1 }),
        markModified: jest.fn(),
        toObject: mockJest.fn().mockReturnValue(apiKeyData),
        toJSON: mockJest.fn().mockReturnValue(apiKeyData),
        isModified: mockJest.fn().mockReturnValue(false),
        isNew: false
    };
};

export const createApiKeySchemaMock = () => ({
    pre: jest.fn(),
    post: jest.fn(),
    index: jest.fn(),
    methods: {},
    statics: {},
    paths: {
        name: { type: String, required: true },
        key: { type: String, required: true, unique: true },
        is_deleted: { type: Boolean, default: false },
        is_revoked: { type: Boolean, default: false },
        allowed_endpoints: [{ type: String }],
        requests: { type: Number, default: 0 },
        requests_endpoints: { type: Object, default: {} },
        last_used: { type: Date },
        organization_id: { type: String, required: true },
        created_by: { type: String, required: true },
        creation_timestamp: { 
            type: Date, 
            required: true, 
            default: () => new Date().toISOString() 
        }
    }
});

export const setupApiKeyTest = () => {
    const mockModel = createApiKeyModelMock();
    const mockApiKey = createMockApiKey();
    const mockDocument = createMockApiKeyDocument();
    
    return {
        mockModel,
        mockApiKey,
        mockDocument
    };
};

export const expectApiKeyCreated = (mockModel: any, apiKeyData: any) => {
    expect(mockModel.create).toHaveBeenCalledWith(apiKeyData);
};

export const expectApiKeyFound = (mockModel: any, query: any) => {
    expect(mockModel.findOne).toHaveBeenCalledWith(query);
};

export const expectApiKeyUpdated = (mockModel: any, query: any, update: any) => {
    expect(mockModel.updateOne).toHaveBeenCalledWith(query, update);
};

export const expectApiKeyDeleted = (mockModel: any, query: any) => {
    expect(mockModel.deleteOne).toHaveBeenCalledWith(query);
};

export const expectApiKeyUsageTracked = (mockApiKey: any, endpoint: string) => {
    expect(mockApiKey.requests).toBeGreaterThan(0);
    expect(mockApiKey.requests_endpoints[endpoint]).toBeGreaterThan(0);
    expect(mockApiKey.last_used).toBeInstanceOf(Date);
    expect(mockApiKey.markModified).toHaveBeenCalledWith('requests_endpoints');
    expect(mockApiKey.save).toHaveBeenCalled();
};

export const createValidApiKeyData = (overrides: any = {}) => ({
    name: 'Test API Key',
    key: 'test-api-key-12345',
    allowed_endpoints: ['users', 'vessels', 'artifacts'],
    organization_id: '507f1f77bcf86cd799439012',
    created_by: '507f1f77bcf86cd799439011',
    ...overrides
});

export const createInvalidApiKeyData = (field: string) => {
    const validData = createValidApiKeyData();
    
    switch (field) {
        case 'name':
            return { ...validData, name: '' };
        case 'key':
            return { ...validData, key: '' };
        case 'allowed_endpoints':
            return { ...validData, allowed_endpoints: [] };
        case 'organization_id':
            return { ...validData, organization_id: '' };
        case 'created_by':
            return { ...validData, created_by: '' };
        default:
            return validData;
    }
};

export const cleanupApiKeyTest = () => {
    jest.clearAllMocks();
};

export default createApiKeyModelMock();