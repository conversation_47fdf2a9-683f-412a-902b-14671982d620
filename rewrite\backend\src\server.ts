import "dotenv/config";
// import "aws-sdk/lib/maintenance_mode_message".suppress = true;
import "./modules/processLogs";
import express from "express";
import mongoose from "mongoose";
import cors from "cors";
import path from "path";
import socketIo from "socket.io";
import http from "http";
import ioEmitter from "./modules/ioEmitter";
import jwt, { JwtPayload } from "jsonwebtoken";
import SessionLog from "./models/SessionLog";
import { swaggerUi, swaggerDocs, swaggerConfig } from "./modules/swagger";
import cookieParser from "cookie-parser";
import { v4 as uuidv4 } from "uuid";
import indexRouter from "./routes/index";
import indexRouterV2 from "./routes/v2/index.v2";
import { postLogToSlack } from "./modules/notifyLog";
import { createAdapter } from "@socket.io/redis-adapter";
import { createClient } from "redis";

if (!process.env.JWT_SECRET) {
    throw new Error("JWT_SECRET is not defined");
}

const app = express();
const server = http.createServer(app);

const io = new socketIo.Server(server, {
    cors: {
        origin: "*",
        credentials: true,
    },
});

app.use(
    cors({
        exposedHeaders: ["RateLimit-Reset", "Content-Disposition", "Content-Type"],
        origin: true,
        credentials: true,
    }),
);

app.use(cookieParser());
app.use(express.json({ limit: "20mb" }));

app.use("/api", (req, res, next) => {
    if (!req.headers.authorization) {
        console.log(
            `[${req.method}: ${req.originalUrl}] user: public, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `,
        );
    }
    if (!req.cookies.deviceId) {
        const deviceId = uuidv4();
        res.cookie("deviceId", deviceId, {
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
        });
    }
    next();
});
app.use("/api/docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs, swaggerConfig));
app.use("/api", indexRouter); // this is a v1 version route
app.use("/api/v2", indexRouterV2); // this is a v2 version route

app.use("/api/*", (_req, res) => res.status(404).send(`< h3 > Sorry, that route does not exist.</h3 > `));
app.use("/api", (_req, res) => res.send(`< h3 > Welcome to Quartermaster API</h3 > `));

const frontendDistPath = path.resolve(__dirname, "../../../frontend/dist");
app.use(express.static(frontendDistPath));

app.get("*", (_req, res) => {
    res.sendFile(path.join(frontendDistPath, "index.html"));
});

const PORT = Number(process.env.PORT);

// wrap startup so Redis connects before we accept socket connections
async function start() {
    // ----- Redis adapter setup (single-node / cluster-mode disabled) -----
    const redisUrl = process.env.SHARED_REDIS_URL;
    if (!redisUrl) {
        throw new Error("SHARED_REDIS_URL is not defined");
    }

    const pubClient = createClient({ url: redisUrl });
    const subClient = pubClient.duplicate();

    pubClient.on("error", (err) => console.error("Redis pub error", err));
    subClient.on("error", (err) => console.error("Redis sub error", err));

    await pubClient.connect();
    await subClient.connect();

    const env = process.env.NODE_ENV;
    if (!env) {
        throw new Error("NODE_ENV is not defined");
    }
    io.adapter(createAdapter(pubClient, subClient, { key: `socket.io:${env}` }));
    // --------------------------------------------------------------------

    io.use((socket, next) => {
        try {
            const { jwt_token } = socket.handshake.auth;
            if (!jwt_token) return next(new Error("Authentication error: No token provided"));

            const { user_id } = jwt.verify(jwt_token, process.env.JWT_SECRET as string) as JwtPayload;
            if (!user_id) return next(new Error("Authentication error: Invalid token"));

            // attach to auth for downstream usage
            socket.handshake.auth.user_id = user_id;

            next();
        } catch (err: any) {
            next(new Error(err.message));
        }
    });

    io.on("connection", async (socket) => {
        console.log(`User connected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);

        socket.on("disconnect", async () => {
            console.log(`User disconnected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
            await SessionLog.updateOne({ socket_id: socket.id }, { $set: { disconnect_timestamp: new Date().toISOString() } });
        });

        SessionLog.create({
            socket_id: socket.id,
            device: socket.handshake.auth.device,
            browser: socket.handshake.auth.browser,
            user_id: new mongoose.Types.ObjectId(socket.handshake.auth.user_id),
        });
    });

    ioEmitter.on("notifyAll", (event) => {
        // broadcasts work across instances
        io.emit(event.name, event.data);
    });

    if (process.env.NODE_ENV !== "test") {
        const rServer = server.listen(PORT, () => {
            console.log(`Server running at http://localhost:${PORT}`);
        });
        rServer.setTimeout(600000);
    }

    // Graceful shutdown: close Redis + HTTP
    const shutdown = async (signal: string) => {
        console.log(`\n${signal} received. Closing server...`);
        try {
            await Promise.allSettled([pubClient.quit(), subClient.quit()]);
        } catch {}
        server.close(() => process.exit(0));
        // Force-exit after a timeout
        setTimeout(() => process.exit(1), 10_000).unref();
    };
    process.on("SIGINT", () => shutdown("SIGINT"));
    process.on("SIGTERM", () => shutdown("SIGTERM"));
}

if (process.env.NODE_ENV !== "test") {
    start().catch((err) => {
        console.error("Failed to start server:", err);
        process.exit(1);
    });
}

process.on("uncaughtException", (err) => {
    console.error("(FATAL ERROR) Uncaught Exception:", err);
    postLogToSlack({
        severity: "fatal",
        message: "Uncaught Exception in the backend process",
        stack: err.stack,
    });
});

export default app;
export { server, io, start };
