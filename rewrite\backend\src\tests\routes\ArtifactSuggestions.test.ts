import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import ArtifactSuggestion from '../../models/ArtifactSuggestion';
import ArtifactSynonym from '../../models/ArtifactSynonym';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('natural', () => require('../mocks/modules/natural.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/ArtifactSuggestion', () => require('../mocks/models/artifactSuggestion.mock'));
jest.mock('../../models/ArtifactSynonym', () => require('../mocks/models/artifactSynonym.mock'));
jest.mock('../../modules/spellingCorrector', () => require('../mocks/modules/spellingCorrector.mock'));

describe('Artifact Suggestions API', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return 400 if query is missing or invalid', async () => {
        let res = await request(app).post('/api/suggestions').send({});
        expect(res.status).toBe(400);

        res = await request(app).post('/api/suggestions').send({ query: 123 });
        expect(res.status).toBe(400);
    });

    it('should return 200 with ranked suggestions and update impressions', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([
            { word: 'ship', synonyms: ['boat'] },
        ]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([
            { search: 'ship', click: 5 },
            { search: 'boat', click: 1 },
        ]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 1, modifiedCount: 1 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'Ship' });
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('suggestions');
        expect(Array.isArray(res.body.suggestions)).toBe(true);
        expect(((ArtifactSuggestion as any).updateMany as any).mock.calls.length).toBeGreaterThanOrEqual(0);
    });

    it('should return 500 on internal error', async () => {
        (ArtifactSynonym as any).find.mockRejectedValueOnce(new Error('DB error'));
        (ArtifactSuggestion as any).find.mockRejectedValueOnce(new Error('DB error'));

        const res = await request(app).post('/api/suggestions').send({ query: 'Ship' });
        expect(res.status).toBe(500);
    });

    it('should return only normalized query when DB has no suggestions', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 1, modifiedCount: 1 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'Ship' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
        expect(res.body.suggestions.length).toBeGreaterThanOrEqual(1);
        expect(res.body.suggestions[0]).toBe('ship');
    });

    it('should rank suggestions lexicographically on tie', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([
            { search: 'beta', click: 3 },
            { search: 'alpha', click: 3 },
        ]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 1, modifiedCount: 1 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'Ship Ship' });
        expect(res.status).toBe(200);
        const idxAlpha = res.body.suggestions.indexOf('alpha');
        const idxBeta = res.body.suggestions.indexOf('beta');
        expect(idxAlpha).toBeGreaterThan(-1);
        expect(idxBeta).toBeGreaterThan(-1);
        expect(idxAlpha).toBeLessThan(idxBeta);
    });

    it('should handle empty query string', async () => {
        const res = await request(app).post('/api/suggestions').send({ query: '' });
        expect(res.status).toBe(400);
    });


    it('should limit suggestions to 10 items', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce(
            Array.from({ length: 30 }).map((_, i) => ({ search: `word${i}`, click: i }))
        );
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 1, modifiedCount: 1 } as never);
        });
        const res = await request(app).post('/api/suggestions').send({ query: 'word' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions.length).toBeLessThanOrEqual(10);
    });

    it('should handle synonym cache refresh', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([
            { word: 'ship', synonyms: ['boat', 'vessel'] },
        ]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'boat' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions).toContain('ship');
    });

    it('should handle WordNet synonyms when no DB synonyms found', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'car' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle spelling correction', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'shipp' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle multi-word queries', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([
            { search: 'big ship', click: 5 },
            { search: 'large vessel', click: 3 },
        ]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 2, modifiedCount: 2 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'big ship' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions).toContain('big ship');
    });

    it('should handle updateMany error gracefully', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([
            { search: 'test', click: 1 },
        ]);
        (ArtifactSuggestion as any).updateMany.mockRejectedValueOnce(new Error('Update failed'));

        const res = await request(app).post('/api/suggestions').send({ query: 'test' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions).toContain('test');
    });

    it('should handle non-string query type', async () => {
        const res = await request(app).post('/api/suggestions').send({ query: null });
        expect(res.status).toBe(400);
    });

    it('should handle query with special characters', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'test@#$%' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle very long query', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const longQuery = 'a'.repeat(1000);
        const res = await request(app).post('/api/suggestions').send({ query: longQuery });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle spelling correction when word is different', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'shipp' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle WordNet synonyms with actual synonyms', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'automobile' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle synonym cache with array synonyms', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([
            { word: 'ship', synonyms: ['boat', 'vessel', 'craft'] },
        ]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'boat' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions).toContain('ship');
    });

    it('should handle WordNet synonyms with different word case', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'Car' });
        expect(res.status).toBe(200);
        expect(Array.isArray(res.body.suggestions)).toBe(true);
    });

    it('should handle synonym cache with non-array synonyms', async () => {
        (ArtifactSynonym as any).find.mockResolvedValueOnce([
            { word: 'ship', synonyms: 'boat' },
        ]);
        (ArtifactSuggestion as any).find.mockResolvedValueOnce([]);
        (ArtifactSuggestion as any).updateMany.mockImplementationOnce((_query: any, _update: any) => {
            return Promise.resolve({ matchedCount: 0, modifiedCount: 0 } as never);
        });

        const res = await request(app).post('/api/suggestions').send({ query: 'boat' });
        expect(res.status).toBe(200);
        expect(res.body.suggestions).toContain('ship');
    });

    it('starts monitoring interval when not in test env', async () => {
        jest.resetModules();
        await jest.isolateModulesAsync(async () => {
            const prevEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'dev';
            const setIntervalSpy = jest.spyOn(global, 'setInterval').mockImplementation((callback: () => void, _interval?: number) => {
                callback();
                return {} as NodeJS.Timeout;
            });
            require('../../routes/ArtifactSuggestions.route');
            expect(setIntervalSpy).toHaveBeenCalled();
            setIntervalSpy.mockRestore();
            process.env.NODE_ENV = prevEnv;
        });
    });
});