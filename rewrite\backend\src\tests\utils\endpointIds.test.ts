import { describe, it, expect } from '@jest/globals';
import { endpointIds } from '../../utils/endpointIds';

describe('endpointIds', () => {
    it('should export endpointIds object with correct structure', () => {
        expect(endpointIds).toBeDefined();
        expect(typeof endpointIds).toBe('object');
    });

    it('should contain user-related endpoint IDs', () => {
        expect(endpointIds.FETCH_TOKEN).toBe(101);
        expect(endpointIds.FETCH_USER).toBe(102);
        expect(endpointIds.CREATE_USER).toBe(104);
        expect(endpointIds.DELETE_USER).toBe(106);
    });

    it('should contain stream-related endpoint IDs', () => {
        expect(endpointIds.FETCH_STREAMS_LIST).toBe(201);
        expect(endpointIds.FETCH_STREAM_URL).toBe(202);
        expect(endpointIds.GET_CLIP).toBe(203);
    });

    it('should contain artifact-related endpoint IDs', () => {
        expect(endpointIds.FETCH_ARTIFACTS).toBe(501);
        expect(endpointIds.DOWNLOAD_ARTIFACT).toBe(505);
        expect(endpointIds.FLAG_ARTIFACT).toBe(516);
    });

    it('should contain role and permission endpoint IDs', () => {
        expect(endpointIds.FETCH_ROLES).toBe(601);
        expect(endpointIds.CREATE_ROLE).toBe(602);
        expect(endpointIds.FETCH_PERMISSIONS).toBe(701);
    });

    it('should contain notification endpoint IDs', () => {
        expect(endpointIds.FETCH_NOTIFICATION_ALERTS).toBe(1501);
        expect(endpointIds.CREATE_NOTIFICATION_ALERTS).toBe(1502);
        expect(endpointIds.FETCH_NOTIFICATION_SUMMARIES).toBe(1701);
    });

    it('should contain vessel management endpoint IDs', () => {
        expect(endpointIds.FETCH_PAGINATED_VESSEL_MANAGEMENT).toBe(2301);
        expect(endpointIds.CREATE_VESSEL_MANAGEMENT).toBe(2303);
        expect(endpointIds.UPDATE_VESSEL_MANAGEMENT).toBe(2304);
    });

    it('should contain all expected endpoint ID ranges', () => {
        const values = Object.values(endpointIds);
        expect(values).toContain(101);
        expect(values).toContain(2601);
        expect(values.length).toBeGreaterThan(100);
    });
});
