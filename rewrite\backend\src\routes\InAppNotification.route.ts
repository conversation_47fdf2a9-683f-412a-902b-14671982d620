import rateLimit from "express-rate-limit";
import express, { Request, Response } from "express";
import InAppNotification from "../models/InAppNotification";
import isAuthenticated from "../middlewares/auth";
import { validateError } from "../utils/functions";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import { validateData } from "../middlewares/validator";
import { query, param, body } from "express-validator";
import db from "../modules/db";
import { processBatchItem, s3Config } from "../modules/awsS3";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        query("is_read")
            .optional()
            .isIn(["0", "1"])
            .withMessage((value, { path }) => `Invalid value ${value} provided for ${path}`)
            .toBoolean(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const page: number = parseInt(req.query.page as string) || 1;
            const pageSize: number = Math.min(parseInt(req.query.page_size as string) || 50, 50);
            const skip: number = (page - 1) * pageSize;

            const totalRecords: number = await InAppNotification.countDocuments({ receiver: req.user._id });
            const inAppNotifications = await InAppNotification.find({ receiver: req.user._id }).sort({ created_at: -1 }).limit(pageSize).skip(skip);

            const artifactIds = inAppNotifications.map((notification) => notification.artifact_id);
            const artifacts = await db.qmai
                .collection("analysis_results")
                .find({ _id: { $in: artifactIds } })
                .toArray();

            const notificationsWithArtifacts = inAppNotifications
                .filter((notification) => notification.artifact_id && artifacts.some((artifact) => artifact._id.equals(notification.artifact_id!)))
                .map((notification) => {
                    const artifact = artifacts.find((artifact) => notification.artifact_id && artifact._id.equals(notification.artifact_id!));
                    const artifactWithUrls: any = { ...artifact };
                    if (artifact?.image_path) {
                        artifactWithUrls.image_url = processBatchItem({
                            bucketName: artifact.bucket_name,
                            key: artifact.image_path,
                            region: artifact.aws_region,
                        }).signedUrl;
                    }
                    if (artifact?.thumbnail_image_path) {
                        artifactWithUrls.thumbnail_url = processBatchItem({
                            bucketName: s3Config.buckets.compressedItems.name as string,
                            key: artifact.thumbnail_image_path,
                            region: artifact.aws_region,
                        }).signedUrl;
                    }
                    return { ...notification.toObject(), artifact_details: artifactWithUrls };
                });

            const totalPages: number = Math.ceil(totalRecords / pageSize);
            const nextPage: number | null = page < totalPages ? page + 1 : null;
            const previousPage: number | null = page > 1 ? page - 1 : null;

            res.json({
                data: notificationsWithArtifacts,
                pagination: {
                    totalRecords,
                    totalPages,
                    currentPage: page,
                    nextPage,
                    previousPage,
                },
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/markRead/:id",
    assignEndpointId.bind(this, endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const inAppNotification = await InAppNotification.findByIdAndUpdate(req.params.id, { is_read: true }, { new: true });
            res.json({
                data: inAppNotification,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/bulkMarkRead",
    assignEndpointId.bind(this, endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        body("ids")
            .isArray()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req: Request, res: Response) => {
        const { ids }: { ids: string[] } = req.body;
        try {
            const inAppNotifications = await InAppNotification.updateMany({ _id: { $in: ids } }, { is_read: true });
            res.json({
                data: inAppNotifications,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
