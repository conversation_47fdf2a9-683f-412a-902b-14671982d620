import { useState, useEffect } from "react";
import { Modal, TextField, Button, Grid, FormControlLabel, Switch, MenuItem, Typography, CircularProgress } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import S3ImageUpload from "../../../components/S3ImageUpload";
import { useToaster } from "../../../hooks/ToasterHook";
import theme from "../../../theme";
import { useApp } from "../../../hooks/AppHook";
import axiosInstance from "../../../axios";

const EditVesselModal = ({
    open,
    onClose,
    vessel,
    onSubmit,
    units = [],
    unitsLoading = false,
    assignedUnitIds = [],
    assignedUnitIdsLoading = false,
    regions = [],
}) => {
    const toaster = useToaster();
    const { deviceHeight } = useApp();
    const [formData, setFormData] = useState({
        name: "",
        thumbnail_file: null,
        unit_id: "",
        is_active: true,
        region_group_id: "",
        home_port_location: "",
        country_iso_code: "",
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [fetchingIsoCode, setFetchingIsoCode] = useState(false);

    useEffect(() => {
        if (vessel) {
            let coordinatesStr = "";
            if (vessel.home_port_location && vessel.home_port_location.coordinates && vessel.home_port_location.coordinates.length === 2) {
                const [lng, lat] = vessel.home_port_location.coordinates;
                coordinatesStr = `${lat}, ${lng}`;
            }

            setFormData({
                name: vessel.name || "",
                thumbnail_file: vessel.thumbnail_s3_key || null,
                unit_id: vessel.unit_id || "",
                is_active: vessel.is_active !== undefined ? vessel.is_active : true,
                region_group_id: vessel.region_group_id || "",
                home_port_location: coordinatesStr,
                country_iso_code: vessel.country_iso_code || "",
            });
        }
    }, [vessel]);

    const handleChange = (field) => (event) => {
        const value = field === "is_active" ? event.target.checked : event.target.value;
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prev) => ({
                ...prev,
                [field]: "",
            }));
        }
    };

    // Auto-fetch country ISO code when home port coordinates are provided
    useEffect(() => {
        const fetchCountryIsoCode = async () => {
            if (!formData.home_port_location || formData.home_port_location.trim() === "") {
                // Clear ISO code if coordinates are cleared
                if (formData.country_iso_code) {
                    setFormData((prev) => ({ ...prev, country_iso_code: "" }));
                }
                return;
            }

            const coords = formData.home_port_location.split(",").map((coord) => coord.trim());
            if (coords.length !== 2) {
                return;
            }

            const lat = parseFloat(coords[0]);
            const lng = parseFloat(coords[1]);

            if (isNaN(lat) || isNaN(lng)) {
                return;
            }

            // Only fetch if coordinates changed and we don't already have an ISO code (to allow manual override)
            // Check if coordinates match the original vessel coordinates
            const originalCoords = vessel?.home_port_location?.coordinates
                ? `${vessel.home_port_location.coordinates[1]}, ${vessel.home_port_location.coordinates[0]}`
                : "";
            const currentCoords = formData.home_port_location.trim();

            // If coordinates haven't changed from original, don't auto-fetch (preserve existing ISO code)
            if (originalCoords === currentCoords && vessel?.country_iso_code) {
                return;
            }

            // Only fetch if we don't already have an ISO code (to allow manual override)
            if (formData.country_iso_code && formData.country_iso_code.trim() !== "") {
                return;
            }

            setFetchingIsoCode(true);
            try {
                const response = await axiosInstance.get("/geolocations/countryIsoCode", {
                    params: { lat, lng },
                });
                if (response.data && response.data.country_iso_code) {
                    setFormData((prev) => ({
                        ...prev,
                        country_iso_code: response.data.country_iso_code,
                    }));
                }
            } catch (error) {
                console.error("Failed to fetch country ISO code:", error);
                // Don't show error to user, just silently fail
            } finally {
                setFetchingIsoCode(false);
            }
        };

        // Debounce the API call
        const timeoutId = setTimeout(() => {
            fetchCountryIsoCode();
        }, 1000);

        return () => clearTimeout(timeoutId);
    }, [formData.home_port_location, vessel]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = "Vessel name is required";
        }

        if (!formData.region_group_id) {
            newErrors.region_group_id = "Region group is required";
        }
        if (formData.home_port_location && formData.home_port_location.trim() !== "") {
            const coords = formData.home_port_location.split(",").map((coord) => coord.trim());
            if (coords.length !== 2 || isNaN(parseFloat(coords[0])) || isNaN(parseFloat(coords[1]))) {
                newErrors.home_port_location = "Home port location must be valid coordinates (latitude, longitude)";
            }
        }
        if (formData.country_iso_code && formData.country_iso_code.trim() !== "") {
            if (formData.country_iso_code.trim().length !== 3) {
                newErrors.country_iso_code = "Country ISO code must be 3 letters";
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const submitData = new FormData();
            let hasChanges = false;

            if (formData.name !== vessel.name) {
                submitData.append("name", formData.name);
                hasChanges = true;
            }
            if (formData.unit_id !== vessel.unit_id) {
                submitData.append("unit_id", formData.unit_id);
                hasChanges = true;
            }
            if (formData.is_active !== vessel.is_active) {
                submitData.append("is_active", formData.is_active);
                hasChanges = true;
            }
            if (formData.thumbnail_file instanceof File) {
                submitData.append("thumbnail_file", formData.thumbnail_file);
                hasChanges = true;
            } else if (formData.thumbnail_file === "" && vessel.thumbnail_s3_key) {
                submitData.append("remove_thumbnail", true);
                hasChanges = true;
            } else if (formData.region_group_id !== vessel.region_group_id) {
                submitData.append("region_group_id", formData.region_group_id);
                hasChanges = true;
            }

            // Check for home port location changes - allow null for existing records
            const currentCoords = vessel.home_port_location
                ? [vessel.home_port_location.coordinates[1], vessel.home_port_location.coordinates[0]]
                : null;
            let newCoords = null;
            if (formData.home_port_location && formData.home_port_location.trim()) {
                const coords = formData.home_port_location.split(",").map((coord) => parseFloat(coord.trim()));
                if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
                    newCoords = coords;
                }
            }

            if (JSON.stringify(currentCoords) !== JSON.stringify(newCoords)) {
                if (newCoords) {
                    submitData.append("home_port_location", JSON.stringify([newCoords[1], newCoords[0]])); // Send as [longitude, latitude]
                } else {
                    submitData.append("home_port_location", JSON.stringify(null));
                }
                hasChanges = true;
            }

            const currentIsoCode = vessel.country_iso_code || "";
            const newIsoCode = formData.country_iso_code && formData.country_iso_code.trim() !== ""
                ? formData.country_iso_code.trim().toUpperCase()
                : "";

            if (currentIsoCode !== newIsoCode) {
                if (newIsoCode) {
                    submitData.append("country_iso_code", newIsoCode);
                } else {
                    submitData.append("country_iso_code", "");
                }
                hasChanges = true;
            }

            if (!hasChanges) {
                handleClose();
                return;
            }

            const result = await onSubmit(submitData);

            if (result.success) {
                handleClose();
            } else {
                console.error(result.error);
            }
        } catch {
            toaster("An unexpected error occurred", { variant: "error" });
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setErrors({});
        onClose();
    };

    if (!vessel) return null;

    return (
        <Modal open={open} onClose={handleClose}>
            <ModalContainer title={"Edit Vessel"} onClose={handleClose}>
                <Grid
                    container
                    direction="column"
                    sx={{ gap: 2, maxHeight: deviceHeight < 700 ? "60vh" : "80vh", overflow: "auto", flexWrap: "nowrap", flexDirection: "column" }}
                >
                    <Grid>
                        <TextField
                            value={formData.name}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("name")}
                            label="Vessel Name"
                            variant="filled"
                            required
                            error={!!errors.name}
                        />
                    </Grid>

                    <Grid>
                        <TextField
                            select
                            value={formData.unit_id}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("unit_id")}
                            label="Unit ID (Optional)"
                            variant="filled"
                            disabled={unitsLoading || assignedUnitIdsLoading}
                            error={!!errors.unit_id}
                            helperText={unitsLoading || assignedUnitIdsLoading ? "Loading units..." : errors.unit_id}
                        >
                            <MenuItem value={""}>
                                <em>No Unit Assigned</em>
                            </MenuItem>
                            {units.length === 0 && !unitsLoading ? (
                                <MenuItem disabled>No units available</MenuItem>
                            ) : (
                                units.map((unit) => {
                                    const isCurrentVesselUnit = vessel && vessel.unit_id === unit.unit_id;
                                    const isAssignedToOtherVessel = assignedUnitIds.includes(unit.unit_id) && !isCurrentVesselUnit;

                                    return (
                                        <MenuItem key={unit.unit_id} value={unit.unit_id} disabled={isAssignedToOtherVessel}>
                                            {unit.unit_id}
                                        </MenuItem>
                                    );
                                })
                            )}
                        </TextField>
                    </Grid>

                    <Grid>
                        <TextField
                            select
                            value={formData.region_group_id}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleChange("region_group_id")}
                            label="Select Region Group"
                            variant="filled"
                            error={!!errors.region_group_id}
                        >
                            {regions.length === 0 ? (
                                <MenuItem disabled>No regions available</MenuItem>
                            ) : (
                                regions.map((region) => (
                                    <MenuItem key={region._id} value={region._id}>
                                        {region.name} ({region.timezone})
                                    </MenuItem>
                                ))
                            )}
                        </TextField>
                    </Grid>

                    <Grid>
                        <TextField
                            type="text"
                            value={formData.home_port_location || ""}
                            onChange={handleChange("home_port_location")}
                            label="Home Port Location (latitude, longitude)"
                            variant="filled"
                            placeholder="e.g., 14.5995, 120.9842"
                            helperText="Enter coordinates as latitude, longitude (comma separated)"
                            error={!!errors.home_port_location}
                            fullWidth
                            sx={{
                                ".MuiFormHelperText-root": {
                                    color: "grey",
                                    opacity: 0.5,
                                },
                            }}
                        />
                    </Grid>

                    <Grid>
                        <TextField
                            type="text"
                            value={formData.country_iso_code || ""}
                            onChange={handleChange("country_iso_code")}
                            label="Country ISO Code (ISO 3166-1 alpha-3)"
                            variant="filled"
                            placeholder="e.g., USA, GBR, PHL"
                            helperText={
                                fetchingIsoCode
                                    ? "Fetching country code..."
                                    : formData.home_port_location
                                        ? "Auto-filled from coordinates. You can override if incorrect."
                                        : "Enter 3-letter country code (e.g., USA, GBR)"
                            }
                            error={!!errors.country_iso_code}
                            fullWidth
                            InputProps={{
                                endAdornment: fetchingIsoCode ? <CircularProgress size={20} /> : null,
                            }}
                            inputProps={{
                                style: { textTransform: "uppercase" },
                                maxLength: 3,
                            }}
                            onInput={(e) => {
                                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z]/g, "");
                            }}
                            sx={{
                                ".MuiFormHelperText-root": {
                                    color: "grey",
                                    opacity: 0.5,
                                },
                            }}
                        />
                    </Grid>

                    <Grid>
                        <S3ImageUpload
                            value={formData.thumbnail_file}
                            onChange={(fileData) => setFormData((prev) => ({ ...prev, thumbnail_file: fileData }))}
                            label="Vessel Thumbnail (optional)"
                            maxSizeBytes={5 * 1024 * 1024} // 5MB
                            acceptedTypes="image/jpeg,image/jpg"
                            error={!!errors.thumbnail_file}
                            helperText={errors.thumbnail_file || "Upload a JPG or JPEG image for the vessel thumbnail"}
                        />
                    </Grid>

                    <Grid marginBottom={5}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={formData.is_active}
                                    onChange={handleChange("is_active")}
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label={formData.is_active ? "Active Vessel" : "Inactive Vessel"}
                        />
                        <Typography color="warning" fontSize={12} maxWidth={500}>
                            Warning: Inactivating this vessel will revoke its access from Invite Tokens, API Keys, Users and Notifications
                        </Typography>
                    </Grid>
                </Grid>

                <Grid sx={{ justifyContent: "center", display: "flex", gap: 1 }}>
                    <Button
                        variant="contained"
                        onClick={handleSubmit}
                        disabled={loading || !formData.name || !!Object.keys(errors).find((key) => errors[key])}
                    >
                        {loading ? "Updating..." : "Update Vessel"}
                    </Button>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EditVesselModal;
