import { jest } from '@jest/globals';

const canAccessVessel = jest.fn().mockReturnValue(true);
const validateError = jest.fn().mockImplementation((err: any, res: any) => {
    res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
});
const splitByMonthsUTC = jest.fn().mockReturnValue([
    { start: new Date('2024-09-01'), end: new Date('2024-09-30') }
]);
const getLocationsCollections = jest.fn().mockReturnValue([]);
const getContentTypeFromFileExtension = jest.fn().mockReturnValue('image/jpeg');
const fileNameTimestamp = jest.fn().mockReturnValue('2024-01-01T00:00:00Z');
const generateZip = jest.fn().mockReturnValue({
    generateNodeStream: jest.fn().mockReturnValue({
        pipe: jest.fn().mockReturnValue({
            on: jest.fn().mockImplementation((event, callback: any) => {
                if (event === 'finish') callback();
                return { on: jest.fn() };
            }),
        }),
        on: jest.fn(),
    }),
});
const removeSpecialCharsFromFilename = jest.fn().mockImplementation((name) => name);
const userHasPermissions = jest.fn().mockReturnValue(false);
const groupByImage = jest.fn().mockReturnValue([]);
const groupArtifactsByDuplicateIndex = jest.fn().mockReturnValue([]);
const generateTimeSeries = jest.fn().mockReturnValue({
    timestamp: new Date().toISOString(),
    count: 3,
});
const getSimplifiedCoords = (jest.fn() as any).mockImplementation((locations: any[]) => locations);
const generateInvitationLink = jest.fn().mockResolvedValue('http://example.com/invite-link' as never);
const escapeRegExp = jest.fn().mockImplementation((str: any) => {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
});
const isIntStrict = jest.fn().mockImplementation((value: any) => {
    return Number.isInteger(value);
});
const cleanSuggestion = (jest.fn() as any).mockImplementation((text: string) => text);
const consoleLogObjectSize = (jest.fn() as any).mockImplementation((obj: any, label: string) => {
    console.log(label, obj);
});
const generateUnsubscribeToken = jest.fn().mockReturnValue('mock-token');
const buildStaticMarkerSignature = jest.fn().mockReturnValue('marker-signature');
const getStaticMapOld = jest.fn();

export {
    canAccessVessel,
    validateError,
    splitByMonthsUTC,
    getLocationsCollections,
    getContentTypeFromFileExtension,
    fileNameTimestamp,
    generateZip,
    removeSpecialCharsFromFilename,
    userHasPermissions,
    groupByImage,
    groupArtifactsByDuplicateIndex,
    generateTimeSeries,
    generateInvitationLink,
    escapeRegExp,
    isIntStrict,
    cleanSuggestion,
    consoleLogObjectSize,
    generateUnsubscribeToken
    ,buildStaticMarkerSignature
    ,getStaticMapOld
    ,getSimplifiedCoords
};
