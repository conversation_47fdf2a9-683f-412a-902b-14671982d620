import { OTP_EMAIL_CONTENT } from "../utils/Email";
import { getSharedRedisClient } from "./sharedRedis";

const OTP_EXPIRATION_TIME = 10 * 60; // 10 minutes in seconds (for Redis TTL)

const generateOtp = () => ({
    otp: Math.floor(100000 + Math.random() * 900000),
});

const getOtpKey = (email: string) => `otp:${email}`;

const sendOtp = async (email: string, name: string, sendEmail: typeof import("./email").sendEmail) => {
    const { otp } = generateOtp();
    const client = await getSharedRedisClient();

    // Store OTP in Redis with TTL (expiration handled by Redis)
    const otpKey = getOtpKey(email);
    await client.setEx(otpKey, OTP_EXPIRATION_TIME, otp.toString());

    console.log(`Generated OTP ${otp} for ${email}, expires in ${OTP_EXPIRATION_TIME} seconds`);

    const date = new Date();
    const content = OTP_EMAIL_CONTENT(otp, name, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
    try {
        await sendEmail({
            to: email,
            subject: "Your OTP Code",
            html: content,
        });
        return { message: "OTP sent successfully" };
    } catch (error) {
        // If email fails, remove OTP from Redis
        await client.del(otpKey).catch(() => {});
        console.error(`Failed to send OTP to ${email}:`, error);
        throw new Error("Failed to send OTP");
    }
};

const verifyOtp = async (email: string, otp: number) => {
    const client = await getSharedRedisClient();

    const otpKey = getOtpKey(email);
    const storedOtp = await client.get(otpKey);

    if (!storedOtp) {
        return { valid: false, message: "Invalid OTP or email" };
    }

    if (parseInt(storedOtp, 10) !== otp) {
        return { valid: false, message: "Invalid OTP or email" };
    }

    // OTP is valid, delete it from Redis (one-time use)
    await client.del(otpKey);
    return { valid: true };
};

// Legacy exports for backward compatibility (deprecated, will be removed)
const cleanupExpiredOtps = () => {
    // No-op: Redis handles expiration automatically via TTL
};

const otpStore: never[] = []; // Empty array for backward compatibility

const startCleanupTimeout = () => {
    // No-op: Redis handles expiration automatically via TTL
};

const stopCleanupTimeout = () => {
    // No-op: Redis handles expiration automatically via TTL
};

const cleanupTimeout: null = null; // Always null for backward compatibility

export { sendOtp, verifyOtp, cleanupExpiredOtps, otpStore, startCleanupTimeout, stopCleanupTimeout, cleanupTimeout };
