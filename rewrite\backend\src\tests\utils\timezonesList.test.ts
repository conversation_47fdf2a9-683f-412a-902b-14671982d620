import { describe, it, expect } from '@jest/globals';
import { timezonesList, isValidTimezoneOffset, defaultDateTimeFormat } from '../../utils/timezonesList';

describe('timezonesList', () => {
    it('should export timezonesList array with correct structure', () => {
        expect(timezonesList).toBeDefined();
        expect(Array.isArray(timezonesList)).toBe(true);
        expect(timezonesList.length).toBeGreaterThan(0);
    });

    it('should contain timezone objects with required properties', () => {
        timezonesList.forEach(tz => {
            expect(tz).toHaveProperty('name');
            expect(tz).toHaveProperty('offset');
            expect(tz).toHaveProperty('representative');
            expect(typeof tz.name).toBe('string');
            expect(typeof tz.offset).toBe('string');
            expect(typeof tz.representative).toBe('string');
        });
    });

    it('should contain expected timezone offsets', () => {
        const offsets = timezonesList.map(tz => tz.offset);
        expect(offsets).toContain('+00:00');
        expect(offsets).toContain('-12:00');
        expect(offsets).toContain('+12:00');
        expect(offsets).toContain('+05:30');
    });

    it('should have valid timezone offset format', () => {
        timezonesList.forEach(tz => {
            expect(tz.offset).toMatch(/^[+-]\d{2}:\d{2}$/);
        });
    });

    it('should contain expected timezone names', () => {
        const names = timezonesList.map(tz => tz.name);
        expect(names).toContain('GMT+00:00');
        expect(names).toContain('GMT-12:00');
        expect(names).toContain('GMT+12:00');
    });

    it('should contain expected representative timezones', () => {
        const representatives = timezonesList.map(tz => tz.representative);
        expect(representatives).toContain('UTC');
        expect(representatives).toContain('America/New_York');
        expect(representatives).toContain('Asia/Tokyo');
    });
});

describe('isValidTimezoneOffset', () => {
    it('should return true for valid timezone offsets', () => {
        expect(isValidTimezoneOffset('+00:00')).toBe(true);
        expect(isValidTimezoneOffset('-12:00')).toBe(true);
        expect(isValidTimezoneOffset('+12:00')).toBe(true);
        expect(isValidTimezoneOffset('+05:30')).toBe(true);
    });

    it('should return false for invalid timezone offsets', () => {
        expect(isValidTimezoneOffset('+13:00')).toBe(false);
        expect(isValidTimezoneOffset('invalid')).toBe(false);
        expect(isValidTimezoneOffset('')).toBe(false);
        expect(isValidTimezoneOffset('+00:60')).toBe(false);
    });
});

describe('defaultDateTimeFormat', () => {
    it('should export defaultDateTimeFormat string', () => {
        expect(defaultDateTimeFormat).toBeDefined();
        expect(typeof defaultDateTimeFormat).toBe('string');
        expect(defaultDateTimeFormat).toBe('MM/DD/YYYY h:mm:ss A');
    });
});
