import { jest, beforeEach, describe, it, expect, afterEach } from '@jest/globals';


describe('setInterval conditional based on NODE_ENV', () => {
    beforeEach(() => {
        jest.spyOn(global, 'setInterval').mockImplementation((() => { }) as any);
        jest.resetModules()
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should call setInterval when NODE_ENV is not "test"', () => {
        process.env.NODE_ENV = 'dev';

        require('../../modules/processLogs');

        expect(setInterval).toHaveBeenCalledTimes(1);
    });

    it('should not call setInterval when NODE_ENV is "test"', () => {
        process.env.NODE_ENV = 'test';

        require('../../modules/processLogs');

        expect(setInterval).not.toHaveBeenCalled();
    });

    it('should successfully execute the function without throwing errors', () => {
        process.env.NODE_ENV = 'test';
        const { logCpuAndMemoryUsage } = require('../../modules/processLogs')
        console.log('logCpuAndMemoryUsage is', logCpuAndMemoryUsage)
        expect(() => logCpuAndMemoryUsage()).not.toThrow();
    });
});
