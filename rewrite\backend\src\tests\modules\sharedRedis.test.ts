import { jest, beforeEach, afterEach, describe, it, expect } from '@jest/globals';
import { getSharedRedisClient, closeSharedRedisClient } from '../../modules/sharedRedis';

describe('Shared Redis Module', () => {
    const originalEnv = process.env;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.resetModules();
        process.env = {
            ...originalEnv,
            SHARED_REDIS_URL: 'redis://localhost:6379',
        };
    });

    afterEach(async () => {
        // Clean up: close any open connections
        try {
            await closeSharedRedisClient();
        } catch (_) {
            // Ignore cleanup errors
        }
        process.env = originalEnv;
        jest.resetModules();
    });

    describe('getSharedRedisClient', () => {
        it('should throw error when SHARED_REDIS_URL is not defined', async () => {
            delete process.env.SHARED_REDIS_URL;
            jest.resetModules();

            const { getSharedRedisClient } = await import('../../modules/sharedRedis');
            await expect(getSharedRedisClient()).rejects.toThrow('SHARED_REDIS_URL is not defined');
        });

        it('should create and return a Redis client when SHARED_REDIS_URL is defined', async () => {
            const client = await getSharedRedisClient();

            expect(client).toBeDefined();
            expect(client.isOpen).toBe(true);
        });

        it('should return the same client instance on subsequent calls (singleton pattern)', async () => {
            const client1 = await getSharedRedisClient();
            const client2 = await getSharedRedisClient();

            expect(client1).toBe(client2);
        });

        it('should reconnect if client connection is lost', async () => {
            const client = await getSharedRedisClient();
            expect(client.isOpen).toBe(true);

            // Simulate connection loss by setting isOpen to false
            (client as any).isOpen = false;

            // Get client again - should reconnect
            const reconnectedClient = await getSharedRedisClient();
            expect(reconnectedClient.isOpen).toBe(true);
            expect(reconnectedClient).toBe(client);
        });

        it('should register error handler on Redis client and handle errors', async () => {
            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

            // Reset modules to get a fresh instance
            jest.resetModules();
            const { getSharedRedisClient } = await import('../../modules/sharedRedis');
            const client = await getSharedRedisClient();

            // Verify that the client has an error handler registered
            expect(client).toBeDefined();
            expect(client.isOpen).toBe(true);

            // Verify that on() was called to set up error handler
            expect((client as any).on).toHaveBeenCalledWith('error', expect.any(Function));

            // Trigger the error handler to ensure it's callable
            const errorHandler = (client as any).on.mock.calls.find((call: any[]) => call[0] === 'error')?.[1];
            if (errorHandler) {
                const testError = new Error('Test Redis error');
                errorHandler(testError);
                expect(consoleErrorSpy).toHaveBeenCalledWith('Redis client error:', testError);
            }

            consoleErrorSpy.mockRestore();
        });
    });

    describe('closeSharedRedisClient', () => {
        it('should close an open Redis client connection', async () => {
            const client = await getSharedRedisClient();
            expect(client.isOpen).toBe(true);

            await closeSharedRedisClient();

            // After closing, the client should be null (reset)
            // Get a new client to verify the old one was closed
            const newClient = await getSharedRedisClient();
            // The new client should be a different instance or the same but reconnected
            expect(newClient).toBeDefined();
        });

        it('should handle closing when no client exists', async () => {
            // Ensure no client exists
            await closeSharedRedisClient();

            // Should not throw
            await expect(closeSharedRedisClient()).resolves.not.toThrow();
        });

        it('should handle closing when client is already closed', async () => {
            const client = await getSharedRedisClient();
            (client as any).isOpen = false;

            // Should not throw
            await expect(closeSharedRedisClient()).resolves.not.toThrow();
        });

        it('should reset client to null after closing', async () => {
            await getSharedRedisClient();
            await closeSharedRedisClient();

            // Get a new client - should create a fresh instance
            const newClient = await getSharedRedisClient();
            expect(newClient).toBeDefined();
            expect(newClient.isOpen).toBe(true);
        });
    });
});

