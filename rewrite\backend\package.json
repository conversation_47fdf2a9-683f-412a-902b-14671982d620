{"name": "quartermaster-backend", "version": "2.16.4", "engines": {"node": "18.x"}, "description": "Backend server for authentication and video streaming", "main": "server.js", "scripts": {"dev": "ts-node-dev --respawn src/server.ts", "build": "tsc && npm run copy:assets", "build:ci": "tsc --outDir dist-$BUILD_ID && npm run copy:assets:ci", "copy:assets": "copyfiles -u 1 src/assets/* src/assets/**/* dist/", "copy:assets:ci": "copyfiles -u 1 src/assets/* src/assets/**/* dist-$BUILD_ID/", "start": "node dist/server.js", "test": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint .", "lint:fix": "eslint --fix .", "lint:check": "eslint . --max-warnings=0", "dev:latest": "git restore . && git checkout dev && git pull & npm i && cd ./frontend && npm i && npm run build && cd .. && npm run start", "format": "prettier --config .prettierrc --ignore-path .prettierignore --write \"**/*.{js,ts,json,css,md}\"", "format:check": "prettier --config .prettierrc --ignore-path .prettierignore --check \"**/*.{js,ts,json,css,md}\"", "coverage:check": "node scripts/checkCoverage.js"}, "dependencies": {"@aws-sdk/client-cloudfront": "^3.859.0", "@aws-sdk/client-kinesis-video": "^3.782.0", "@aws-sdk/client-kinesis-video-archived-media": "^3.782.0", "@aws-sdk/client-kinesis-video-media": "^3.758.0", "@aws-sdk/cloudfront-signer": "^3.813.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@mapbox/geo-viewport": "^0.5.0", "@socket.io/redis-adapter": "^8.3.0", "@types/multer": "^2.0.0", "aws-sdk": "^2.1677.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.0.3", "express": "^4.20.0", "express-rate-limit": "^7.4.0", "express-validator": "^7.1.0", "geodesy": "^2.4.0", "googleapis": "^140.0.1", "i18n-iso-countries": "^7.14.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "mongoose": "^6.13.3", "multer": "^2.0.0", "natural": "^8.1.0", "nodemailer": "^6.9.14", "nspell": "^2.1.5", "openai": "^4.83.0", "p-limit": "^3.1.0", "redis": "^4.6.13", "sharp": "^0.34.3", "simplify-js": "^1.2.4", "socket.io": "^4.7.5", "socket.io-client": "^4.8.1", "supercluster": "^8.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.2"}, "devDependencies": {"@eslint/js": "^9.18.0", "@jest/globals": "^30.1.2", "@types/bcryptjs": "^2.4.6", "@types/chai": "^5.0.0", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.21", "@types/geodesy": "^2.2.8", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.7", "@types/mapbox__geo-viewport": "^0.5.3", "@types/nodemailer": "^7.0.1", "@types/nspell": "^2.1.6", "@types/sinon": "^17.0.3", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "chai": "^4.5.0", "copyfiles": "^2.4.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "jest": "^29.7.0", "lint-staged": "^15.4.3", "mocha": "^10.7.3", "mockingoose": "^2.16.2", "nodemon": "^3.1.4", "prettier": "3.5.3", "supertest": "^7.0.0", "ts-jest": "^29.4.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2", "typescript-eslint": "^8.44.0"}, "author": "", "license": "ISC", "nodemonConfig": {"ignore": ["frontend/*"]}, "lint-staged": {"!(frontend)/**/*.{js,ts}": ["prettier --config .prettierrc --ignore-path .prettierignore --write", "eslint --config ./eslint.config.mjs --fix"], "frontend/**/*.{js,jsx,ts,tsx}": ["prettier --config ./frontend/.prettierrc --ignore-path ./frontend/.prettierignore --write", "eslint --config ./frontend/eslint.config.js --fix"]}}