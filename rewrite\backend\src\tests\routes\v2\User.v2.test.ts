import request from 'supertest';
import app from '../../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../../mocks/auth.mock';
import User from '../../../models/User';
import ApiKey from '../../../models/ApiKey';
import { getUser } from '../../../queries/User';

jest.mock('../../../modules/db', () => require('../../mocks/modules/db.mock'));
jest.mock('../../../models/User', () => require('../../mocks/models/user.mock'));
jest.mock('../../../models/ApiKey', () => require('../../mocks/models/apiKey.mock'));
jest.mock('../../../queries/User', () => ({ getUser: jest.fn() }));

describe('User V2 API', () => {
    describe('GET /api/v2/users', () => {
        const runTests = (authMethod: 'user' | 'api-key', generateToken: (id: string) => string, userOrApiKey: any) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/v2/users');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.nonAuthorized,
                        jwt_tokens: [nonAuthToken.replace('Bearer ', '')],
                        permissions: [],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.nonAuthorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    const res = await request(app).get('/api/v2/users').set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return 200 and list users with pagination and filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.authorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(25 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439011', name: 'John', email: '<EMAIL>' },
                    ] as never);

                    const res = await request(app)
                        .get('/api/v2/users?page=2&rowsPerPage=10&roles=1,2&vessels=507f1f77bcf86cd799439012&hasEmail=true&full_name_or_email=john&created_after=1700000000000&organizations=507f1f77bcf86cd799439013')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                });

                it('should ignore invalid sort fields safely and still return 200', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.authorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(1 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users?sort=__proto__&sortOrder=desc')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should apply allowed sort field and return 200', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(2 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users?sort=name&sortOrder=asc')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should filter by roles and vessels and return 200', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(1 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users?roles=1,2&vessels=507f1f77bcf86cd799439011,507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should apply hasEmail=true and search query', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(1 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users?hasEmail=true&full_name_or_email=jane')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should apply hasEmail=false and created_after', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(0 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([] as never);

                    const res = await request(app)
                        .get(`/api/v2/users?hasEmail=false&created_after=${Date.now() - 1000}`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should restrict organization when user is not internal', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439099', is_internal: false },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(1 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should use organizations query when internal user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(1 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);

                    const res = await request(app)
                        .get('/api/v2/users?organizations=507f1f77bcf86cd799439011,507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should handle invalid rowsPerPage and page gracefully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.countDocuments as jest.Mock).mockResolvedValue(0 as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([] as never);

                    const res = await request(app)
                        .get('/api/v2/users?page=-5&rowsPerPage=9999')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (getUser as jest.Mock).mockResolvedValue({
                        ...userOrApiKey.authorized,
                        jwt_tokens: [authToken.replace('Bearer ', '')],
                        organization: { _id: '507f1f77bcf86cd799439013', is_internal: true },
                    } as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.authorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    (User.countDocuments as jest.Mock).mockRejectedValue(new Error('db') as never);

                    const res = await request(app)
                        .get('/api/v2/users')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        const { generateUserToken, authorizedUser, nonAuthorizedUser } = require('../../data/Auth');
        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


