import { expect, jest } from '@jest/globals';

const mockJest = jest as any;

export const createMicroserviceSocketMock = () => ({
    connect: mockJest.fn(),
    disconnect: mockJest.fn(),
    emit: mockJest.fn(),
    on: mockJest.fn(),
    off: mockJest.fn(),
    once: mockJest.fn(),
    removeAllListeners: mockJest.fn(),
    connected: true,
    disconnected: false,
    id: 'mock-socket-id',
    io: {
        uri: 'http://localhost:3001',
        opts: {
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 5,
            timeout: 20000
        }
    }
});

export const setupMicroserviceSocketScenarios = () => {
    const mockSocket = createMicroserviceSocketMock();
    
    return {
        mockSocket,
        connectionSuccess: () => {
            mockSocket.connected = true;
            mockSocket.disconnected = false;
            mockSocket.connect();
        },
        connectionFailure: (errorMessage = 'Connection failed') => {
            const error = new Error(errorMessage);
            mockSocket.connected = false;
            mockSocket.disconnected = true;
            mockSocket.connect();
            return error;
        },
        disconnection: () => {
            mockSocket.connected = false;
            mockSocket.disconnected = true;
            mockSocket.disconnect();
        },
        reconnection: () => {
            mockSocket.connected = true;
            mockSocket.disconnected = false;
            mockSocket.emit('reconnect', 1);
        },
        reconnectionFailure: (attempts: number = 5) => {
            mockSocket.connected = false;
            mockSocket.disconnected = true;
            mockSocket.emit('reconnect_failed', attempts);
        },
        addListener: (event: string, listener: Function) => {
            mockSocket.on(event, listener);
        },
        removeListener: (event: string, listener: Function) => {
            mockSocket.off(event, listener);
        },
        simulateIncomingMessage: (socket: any, event: string, ...args: any[]) => {
            socket.emit(event, ...args);
            return { event, args };
        }
    };
};

export const createMicroserviceSocketTestHelpers = () => ({
    createTestEvent: (name: string, data: any = {}) => ({
        name,
        data,
        timestamp: Date.now()
    }),
    createTestMessage: (type: string, payload: any = {}) => ({
        type,
        payload,
        id: `msg-${Date.now()}`,
        timestamp: Date.now()
    }),
        simulateIncomingMessage: (socket: any, event: string, ...args: any[]) => {
            socket.emit(event, ...args);
            return { event, args };
        },
    waitForConnection: (socket: any, timeout: number = 1000) => {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Connection not established within ${timeout}ms`));
            }, timeout);
            
            if (socket.connected) {
                clearTimeout(timer);
                resolve(true);
            } else {
                socket.once('connect', () => {
                    clearTimeout(timer);
                    resolve(true);
                });
            }
        });
    },
    waitForDisconnection: (socket: any, timeout: number = 1000) => {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Disconnection not detected within ${timeout}ms`));
            }, timeout);
            
            if (socket.disconnected) {
                clearTimeout(timer);
                resolve(true);
            } else {
                socket.once('disconnect', () => {
                    clearTimeout(timer);
                    resolve(true);
                });
            }
        });
    }
});

export const setupMicroserviceSocketTest = () => {
    const mockSocket = createMicroserviceSocketMock();
    const scenarios = setupMicroserviceSocketScenarios();
    const helpers = createMicroserviceSocketTestHelpers();
    
    return {
        mockSocket,
        scenarios,
        helpers
    };
};

export const expectSocketConnected = (mockSocket: any) => {
    expect(mockSocket.connect).toHaveBeenCalled();
};

export const expectSocketDisconnected = (mockSocket: any) => {
    expect(mockSocket.disconnect).toHaveBeenCalled();
};

export const expectEventEmitted = (mockSocket: any, event: string, data?: any) => {
    expect(mockSocket.emit).toHaveBeenCalledWith(event, data);
};

export const expectEventNotEmitted = (mockSocket: any, event: string) => {
    expect(mockSocket.emit).not.toHaveBeenCalledWith(event, expect.anything());
};

export const expectListenerAdded = (mockSocket: any, event: string, listener: Function) => {
    expect(mockSocket.on).toHaveBeenCalledWith(event, listener);
};

export const expectListenerRemoved = (mockSocket: any, event: string, listener: Function) => {
    expect(mockSocket.off).toHaveBeenCalledWith(event, listener);
};

export const cleanupMicroserviceSocketTest = () => {
    jest.clearAllMocks();
};
