export const permissionsList = [
    {
        _id: "66d6fd7de480fbb5a5e1abc6",
        permission_id: 100,
        permission_name: "MANAGE_ROLES",
        permission_description: "User can add, remove, and update roles",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "66d70185e480fbb5a5e1abc9",
        permission_id: 200,
        permission_name: "MANAGE_USERS",
        permission_description: "User can update roles for the users",
        two_factor_required: true,
        assignable: true
    },
    {
        _id: "66d701b0e480fbb5a5e1abcb",
        permission_id: 300,
        permission_name: "ACCESS_ALL_VESSELS",
        permission_description: "User can switch region on the dashboard",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "66e0546871fd26bbd48668a2",
        permission_id: 400,
        permission_name: "VIEW_SESSION_LOGS",
        permission_description: "User view session logs on the dashboard",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "66f2ab205fc1d8f82af7c720",
        permission_id: 500,
        permission_name: "MANAGE_API_KEYS",
        permission_description: "User can manage API developer keys on the dashboard",
        two_factor_required: false,
        assignable: false
    },
    {
        _id: "6707dd36af2c1b345bd06b8f",
        permission_id: 600,
        permission_name: "VIEW_STATISTICS",
        permission_description: "User can view statistics on the dashboard",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "679240e27524efb8a6e0cf66",
        permission_id: 700,
        permission_name: "MANAGE_NOTIFICATIONS_ALERTS",
        permission_description: "User can manage notifications alerts",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "67d86ed1926673c75a3da10f",
        permission_id: 800,
        permission_name: "MANAGE_REGIONS_GROUPS",
        permission_description: "User can manage regions groups",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "67daf32c7a2d4a3308b54ad1",
        permission_id: 900,
        permission_name: "ADDITIONAL_EMAIL_ADDRESSES_PRIVILEGE",
        permission_description: "User can add any email in notification alerts",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "67e2d9b466dfd6d9dd8222cc",
        permission_id: 1000,
        permission_name: "MANAGE_ORGANIZATIONS",
        permission_description: "User can add, remove, and update organizations",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "67f6c3f18a24775155418da4",
        permission_id: 1100,
        permission_name: "TEST_NOTIFICATION_ALERTS",
        permission_description: "User can fetch latest notification alerts for testing",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "682f5d2fc198f6e4dd434e1e",
        permission_id: 1200,
        permission_name: "MANAGE_VESSELS",
        permission_description: "User can add and update vessels",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "686d5ce51b5fd43f6ad300e9",
        permission_id: 1300,
        permission_name: "MANAGE_ARTIFACTS",
        permission_description: "User can hide/unhide artifacts",
        two_factor_required: false,
        assignable: true
    },
    {
        _id: "68ee44a09ffcf5676baefd8a",
        permission_id: 1400,
        permission_name: "FLAG_MISMATCH_AIS",
        permission_description: "User can flag/unflag mismatch artifact AIS",
        two_factor_required: false,
        assignable: true
    }];