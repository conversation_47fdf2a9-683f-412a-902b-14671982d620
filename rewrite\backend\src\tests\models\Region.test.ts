import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('Region Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create Region model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Region')];

        const RegionModule = await import('../../models/Region');
        const Region = RegionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Region', expect.any(Object));
        expect(Region).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.paths.value).toBeDefined();
        expect(schemaArg.paths.value.type).toBe(String);
        expect(schemaArg.paths.value.required).toBe(true);
        expect(schemaArg.paths.value.unique).toBe(true);

        expect(schemaArg.paths.is_live).toBeDefined();
        expect(schemaArg.paths.is_live.type).toBe(Boolean);
        expect(schemaArg.paths.is_live.required).toBe(true);

        expect(schemaArg.paths.timezone).toBeDefined();
        expect(schemaArg.paths.timezone.type).toBe(String);
        expect(schemaArg.paths.timezone.required).toBe(true);
    });
});
