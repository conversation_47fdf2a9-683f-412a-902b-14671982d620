import { jest } from '@jest/globals';

const s3 = {
    config: {
        update: jest.fn()
    },
    getSignedUrl: jest.fn()
};

const s3Config = {
    buckets: {
        assets: {
            name: 'test-assets-bucket',
            region: 'us-east-1'
        },
        compressedItems: {
            name: 'test-compressed-bucket',
            region: 'us-east-1'
        }
    }
};

const getCloudfrontSignedUrl = jest.fn();
const generateS3FallbackUrl = jest.fn();
const processBatchItem = jest.fn();
const getObjectStream = jest.fn();
const uploadFileToS3 = jest.fn();
const deleteFileFromS3 = jest.fn();

export { s3, s3Config, getCloudfrontSignedUrl, generateS3FallbackUrl, processBatchItem, getObjectStream, uploadFileToS3, deleteFileFromS3 };
