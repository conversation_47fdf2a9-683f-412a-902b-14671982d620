import countries from "i18n-iso-countries";

const validAlpha3Codes = new Set(Object.keys(countries.getAlpha3Codes()));

export function alpha2ToAlpha3Code(alpha2: string): string | null {
    if (!alpha2 || typeof alpha2 !== "string") {
        return null;
    }
    const upperAlpha2 = alpha2.toUpperCase();
    const alpha3 = countries.alpha2ToAlpha3(upperAlpha2);
    return alpha3 || null;
}

export function isValidAlpha3Code(code: string): boolean {
    if (!code || typeof code !== "string") {
        return false;
    }
    const upperCode = code.toUpperCase();
    return validAlpha3Codes.has(upperCode);
}
