import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockArtifactFavourite(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

(MockArtifactFavourite as AnyObject).find = jest.fn();
(MockArtifactFavourite as AnyObject).findOne = jest.fn();
(MockArtifactFavourite as AnyObject).findOneAndDelete = jest.fn();

(MockArtifactFavourite as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
};

export default MockArtifactFavourite as any;

