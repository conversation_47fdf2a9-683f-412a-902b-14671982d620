import { NextFunction, Request, Response } from "express";
import { Valida<PERSON><PERSON>hain } from "express-validator";

const validateData = async (rules: ValidationChain[], req: Request, res: Response, next: NextFunction) => {
    try {
        await Promise.all(rules.map((rule) => rule.run(req)));

        const { validationResult } = await import("express-validator");
        const errors = validationResult(req);

        if (errors.isEmpty()) {
            next();
        } else {
            console.log(errors.array());
            return res.status(400).json({ message: errors.array()[0].msg });
        }
    } catch (error) {
        console.error("Validation error:", error);
        return res.status(400).json({ message: "Invalid input data" });
    }
};

export { validateData };
