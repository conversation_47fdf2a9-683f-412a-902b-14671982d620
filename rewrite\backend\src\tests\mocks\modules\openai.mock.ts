import { jest } from '@jest/globals';

class OpenAIMock {
    static lastInstance: any;
    static __mode: 'default' | 'missing' | 'error' = 'default';
    chat: any;
    constructor(..._args: any[]) {
        this.chat = {
            completions: {
                create: jest.fn().mockImplementation(async () => {
                    if (OpenAIMock.__mode === 'error') {
                        throw new Error('OpenAI error');
                    }
                    if (OpenAIMock.__mode === 'missing') {
                        return { choices: [{ message: { tool_calls: [] } }] } as any;
                    }
                    return {
                        choices: [
                            {
                                message: {
                                    tool_calls: [
                                        {
                                            function: {
                                                name: 'extract_vessel_metadata',
                                                arguments: '{"categories":["Ship"],"vessel_name":["Vessel A"],"type":"both"}'
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    } as any;
                })
            }
        };
        (OpenAIMock as any).lastInstance = this;
    }

    static __setMode(mode: 'default' | 'missing' | 'error') {
        OpenAIMock.__mode = mode;
    }
}

export default OpenAIMock as any;


