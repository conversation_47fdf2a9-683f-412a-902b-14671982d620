import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import vesselService from '../../services/Vessel.service';
import Vessel from '../../models/Vessel';
import User from '../../models/User';
import NotificationAlert from '../../models/NotificationAlert';
import NotificationSummary from '../../models/NotificationSummary';
import InviteToken from '../../models/InviteToken';
import ApiKey from '../../models/ApiKey';
import RegionGroup from '../../models/RegionGroup';
import { uploadFileToS3, deleteFileFromS3 } from '../../modules/awsS3';
import { escapeRegExp } from '../../utils/functions';
import sharp from 'sharp';

jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/NotificationAlert', () => require('../mocks/models/notificationAlert.mock'));
jest.mock('../../models/NotificationSummary', () => require('../mocks/models/notificationSummary.mock'));
jest.mock('../../models/InviteToken', () => require('../mocks/models/inviteToken.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/RegionGroup', () => require('../mocks/models/regionGroup.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('sharp', () => require('../mocks/modules/sharp.mock'));

describe('VesselService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('compressImageToWebp compresses image to webp format', async () => {
        const mockBuffer = Buffer.from('test');
        (sharp as any).mockReturnValue({
            resize: jest.fn().mockReturnThis(),
            webp: jest.fn().mockReturnThis(),
            toBuffer: jest.fn().mockResolvedValue(Buffer.from('compressed') as never)
        });

        const result = await vesselService.compressImageToWebp(mockBuffer);
        expect(result).toEqual(Buffer.from('compressed'));
    });

    it('uploadVesselThumbnail returns null when no file or buffer', async () => {
        const result = await vesselService.uploadVesselThumbnail({} as any);
        expect(result).toBeNull();
    });

    it('uploadVesselThumbnail uploads original and compressed images', async () => {
        const mockFile = {
            buffer: Buffer.from('test'),
            originalname: 'test.jpg',
            mimetype: 'image/jpeg'
        };

        (uploadFileToS3 as jest.Mock).mockResolvedValueOnce('original-key' as never).mockResolvedValueOnce('compressed-key' as never);
        (sharp as any).mockReturnValue({
            resize: jest.fn().mockReturnThis(),
            webp: jest.fn().mockReturnThis(),
            toBuffer: jest.fn().mockResolvedValue(Buffer.from('compressed') as never)
        });

        const result = await vesselService.uploadVesselThumbnail(mockFile);
        expect(result).toEqual({
            thumbnail_s3_key: 'original-key',
            thumbnail_compressed_s3_key: 'compressed-key'
        });
    });

    it('uploadVesselThumbnail handles file without originalname', async () => {
        const mockFile = {
            buffer: Buffer.from('test'),
            mimetype: 'image/jpeg'
        };

        (uploadFileToS3 as jest.Mock).mockResolvedValueOnce('original-key' as never).mockResolvedValueOnce('compressed-key' as never);
        (sharp as any).mockReturnValue({
            resize: jest.fn().mockReturnThis(),
            webp: jest.fn().mockReturnThis(),
            toBuffer: jest.fn().mockResolvedValue(Buffer.from('compressed') as never)
        });

        const result = await vesselService.uploadVesselThumbnail(mockFile as any);
        expect(result).toEqual({
            thumbnail_s3_key: 'original-key',
            thumbnail_compressed_s3_key: 'compressed-key'
        });
    });

    it('uploadVesselThumbnail throws on error', async () => {
        const mockFile = {
            buffer: Buffer.from('test'),
            originalname: 'test.jpg',
            mimetype: 'image/jpeg'
        };

        (uploadFileToS3 as jest.Mock).mockRejectedValue(new Error('S3 error') as never);

        await expect(vesselService.uploadVesselThumbnail(mockFile)).rejects.toThrow('Failed to upload vessel thumbnail to S3');
    });

    it('find returns vessels with query and projection', async () => {
        (Vessel.find as jest.Mock).mockResolvedValue([{ _id: '1', name: 'Vessel 1' }] as never);

        const result = await vesselService.find({ is_active: true }, { name: 1 });
        expect(result).toEqual([{ _id: '1', name: 'Vessel 1' }]);
    });

    it('find throws on error', async () => {
        (Vessel.find as jest.Mock).mockRejectedValue(new Error('DB error') as never);

        await expect(vesselService.find()).rejects.toThrow('Failed to fetch vessels');
    });

    it('fetchPaginated returns paginated results with search', async () => {
        (Vessel.countDocuments as jest.Mock).mockResolvedValue(25 as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1', region_group_id: 'group1' }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'user1', name: 'User 1', email: '<EMAIL>' }
        ] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([
            { _id: 'group1', name: 'Group 1', timezone: 'UTC' }
        ] as never);
        (escapeRegExp as jest.Mock).mockReturnValue('test');

        const result = await vesselService.fetchPaginated({ page: 2, limit: 10, search: 'test' });

        expect(result.vessels).toHaveLength(1);
        expect(result.pagination.total).toBe(25);
        expect(result.pagination.page).toBe(2);
        expect(result.pagination.totalPages).toBe(3);
    });

    it('fetchPaginated returns paginated results without search', async () => {
        (Vessel.countDocuments as jest.Mock).mockResolvedValue(5 as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (escapeRegExp as jest.Mock).mockReturnValue('');

        const result = await vesselService.fetchPaginated({ page: 1, limit: 10 });

        expect(result.vessels).toHaveLength(0);
        expect(result.pagination.total).toBe(5);
    });

    it('fetchPaginated handles vessels without region_group_id', async () => {
        (Vessel.countDocuments as jest.Mock).mockResolvedValue(1 as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1', region_group_id: null }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'user1', name: 'User 1', email: '<EMAIL>' }
        ] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (escapeRegExp as jest.Mock).mockReturnValue('');

        const result = await vesselService.fetchPaginated({});

        expect(result.vessels[0].regionGroup).toBeNull();
    });

    it('fetchPaginated throws on error', async () => {
        (Vessel.countDocuments as jest.Mock).mockRejectedValue(new Error('DB error') as never);

        await expect(vesselService.fetchPaginated({})).rejects.toThrow('Failed to fetch vessels');
    });

    it('findById returns vessel with user details', async () => {
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1' }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'user1', name: 'User 1', email: '<EMAIL>' }
        ] as never);

        const result = await vesselService.findById({ id: '507f1f77bcf86cd799439011' });

        expect(result).toEqual({
            _id: '1',
            name: 'Vessel 1',
            created_by: 'user1',
            user: { _id: 'user1', name: 'User 1', email: '<EMAIL>' }
        });
    });

    it('findById returns null when vessel not found', async () => {
        (Vessel.aggregate as jest.Mock).mockResolvedValue([] as never);

        const result = await vesselService.findById({ id: '507f1f77bcf86cd799439011' });

        expect(result).toBeNull();
    });

    it('findById throws on invalid ObjectId', async () => {
        await expect(vesselService.findById({ id: 'invalid' })).rejects.toThrow('Invalid vessel ID');
    });

    it('findById throws on error', async () => {
        (Vessel.aggregate as jest.Mock).mockRejectedValue(new Error('DB error') as never);

        await expect(vesselService.findById({ id: '507f1f77bcf86cd799439011' })).rejects.toThrow('DB error');
    });

    it('findByAssignedUnitId returns vessel by unit ID', async () => {
        (Vessel.findOne as jest.Mock).mockResolvedValue({ _id: '1', unit_id: 'unit1' } as never);

        const result = await vesselService.findByAssignedUnitId({ unitId: 'unit1' });

        expect(result).toEqual({ _id: '1', unit_id: 'unit1' });
    });

    it('findByAssignedUnitId throws when unitId is empty', async () => {
        await expect(vesselService.findByAssignedUnitId({ unitId: '' })).rejects.toThrow('unitId is required');
    });

    it('findByAssignedUnitId throws on error', async () => {
        (Vessel.findOne as jest.Mock).mockRejectedValue(new Error('DB error') as never);

        await expect(vesselService.findByAssignedUnitId({ unitId: 'unit1' })).rejects.toThrow('DB error');
    });

    it('getAllAssignedUnitIds returns all unit IDs', async () => {
        (Vessel.find as jest.Mock).mockResolvedValue([
            { unit_id: 'unit1' },
            { unit_id: 'unit2' },
            { unit_id: '' },
            { unit_id: null }
        ] as never);

        const result = await vesselService.getAllAssignedUnitIds();

        expect(result).toEqual(['unit1', 'unit2']);
    });

    it('getAllAssignedUnitIds throws on error', async () => {
        (Vessel.find as jest.Mock).mockRejectedValue(new Error('DB error') as never);

        await expect(vesselService.getAllAssignedUnitIds()).rejects.toThrow('Failed to fetch assigned unit IDs');
    });

    it('create creates vessel with thumbnail', async () => {
        const mockVessel = { _id: '507f1f77bcf86cd799439099', name: 'Vessel 1', created_by: '507f1f77bcf86cd799439011' };
        const mockFile = { buffer: Buffer.from('test'), originalname: 'test.jpg' };

        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockResolvedValue(mockVessel as never);
        (uploadFileToS3 as jest.Mock).mockResolvedValue('s3-key' as never);
        (sharp as any).mockReturnValue({
            resize: jest.fn().mockReturnThis(),
            webp: jest.fn().mockReturnThis(),
            toBuffer: jest.fn().mockResolvedValue(Buffer.from('compressed') as never)
        });
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.create({
            name: 'Vessel 1',
            thumbnail_file: mockFile as any,
            unit_id: 'unit1',
            created_by: '507f1f77bcf86cd799439011' as any,
            region_group_id: '507f1f77bcf86cd799439012',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('create creates vessel without thumbnail', async () => {
        const mockVessel = { _id: '507f1f77bcf86cd799439099', name: 'Vessel 1', created_by: '507f1f77bcf86cd799439011' };

        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.create({
            name: 'Vessel 1',
            unit_id: '',
            created_by: '507f1f77bcf86cd799439011' as any,
            region_group_id: '507f1f77bcf86cd799439012',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('create throws when unit_id already exists', async () => {
        (Vessel.findOne as jest.Mock).mockResolvedValue({ _id: 'existing' } as never);

        await expect(vesselService.create({
            name: 'Vessel 1',
            unit_id: 'existing-unit',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        })).rejects.toThrow('A vessel with this unit ID already exists');
    });

    it('create throws when region_group_id is missing', async () => {
        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: '',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        })).rejects.toThrow('Failed to create vessel');
    });

    it('create throws when home_port_location is missing', async () => {
        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            country_iso_code: 'USA'
        } as any)).rejects.toThrow('Failed to create vessel');
    });

    it('create throws when home_port_location is not an array', async () => {
        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: 'invalid' as any,
            country_iso_code: 'USA'
        })).rejects.toThrow('Failed to create vessel');
    });

    it('create throws when home_port_location has wrong length', async () => {
        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: [10] as any,
            country_iso_code: 'USA'
        })).rejects.toThrow('Failed to create vessel');
    });

    it('create throws when country_iso_code is missing', async () => {
        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: [10, 20],
            country_iso_code: ''
        })).rejects.toThrow('Failed to create vessel');
    });

    it('create handles home_port_location', async () => {
        const mockVessel = { _id: '507f1f77bcf86cd799439099', name: 'Vessel 1', created_by: '507f1f77bcf86cd799439011' };

        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.create({
            name: 'Vessel 1',
            created_by: '507f1f77bcf86cd799439011' as any,
            region_group_id: '507f1f77bcf86cd799439012',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('create throws on location bounds error', async () => {
        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockRejectedValue(new Error('Longitude/latitude is out of bounds') as never);

        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: [200, 200],
            country_iso_code: 'USA'
        })).rejects.toThrow('Location Coordinates are out of bounds');
    });

    it('create throws on general error', async () => {
        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockRejectedValue(new Error('General error') as never);

        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: 'user1' as any,
            region_group_id: 'group1',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        })).rejects.toThrow('Failed to create vessel');
    });

    it('update updates vessel with new thumbnail', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            thumbnail_s3_key: 'old-key',
            save: jest.fn().mockResolvedValue(true as never)
        };
        const mockFile = { buffer: Buffer.from('test'), originalname: 'test.jpg' };

        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (deleteFileFromS3 as jest.Mock).mockResolvedValue(true as never);
        (uploadFileToS3 as jest.Mock).mockResolvedValue('new-key' as never);
        (sharp as any).mockReturnValue({
            resize: jest.fn().mockReturnThis(),
            webp: jest.fn().mockReturnThis(),
            toBuffer: jest.fn().mockResolvedValue(Buffer.from('compressed') as never)
        });
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            name: 'Updated Vessel',
            thumbnail_file: mockFile as any
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('update removes thumbnail when remove_thumbnail is true', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            thumbnail_s3_key: 'old-key',
            thumbnail_compressed_s3_key: 'old-compressed-key',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (deleteFileFromS3 as jest.Mock).mockResolvedValue(true as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            remove_thumbnail: 'true'
        });

        expect(deleteFileFromS3).toHaveBeenCalledTimes(2);
        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('update updates region_group_id', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (RegionGroup.findById as jest.Mock).mockResolvedValue({ _id: 'group1', name: 'Group 1' } as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            region_group_id: '507f1f77bcf86cd799439012'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('update throws on invalid vessel ID', async () => {
        await expect(vesselService.update({
            id: 'invalid',
            name: 'Updated Vessel'
        })).rejects.toThrow('Failed to update vessel');
    });

    it('update throws when unit_id already exists', async () => {
        (Vessel.findOne as jest.Mock).mockResolvedValue({ _id: 'existing' } as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            unit_id: 'existing-unit'
        })).rejects.toThrow('A vessel with this unit ID already exists');
    });

    it('update throws when vessel not found', async () => {
        (Vessel.findById as jest.Mock).mockResolvedValue(null as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            name: 'Updated Vessel'
        })).rejects.toThrow('Vessel not found');
    });

    it('update throws on invalid region group ID', async () => {
        const mockVessel = { _id: '1', name: 'Vessel 1' };
        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            region_group_id: 'invalid'
        })).rejects.toThrow('Failed to update vessel');
    });

    it('update throws when region group not found', async () => {
        const mockVessel = { _id: '1', name: 'Vessel 1' };
        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (RegionGroup.findById as jest.Mock).mockResolvedValue(null as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            region_group_id: '507f1f77bcf86cd799439012'
        })).rejects.toThrow('Failed to update vessel');
    });

    it('update deactivates vessel and removes from related collections', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (NotificationAlert.updateMany as jest.Mock).mockResolvedValue(true as never);
        (NotificationSummary.updateMany as jest.Mock).mockResolvedValue(true as never);
        (InviteToken.updateMany as jest.Mock).mockResolvedValue(true as never);
        (ApiKey.updateMany as jest.Mock).mockResolvedValue(true as never);
        (User.updateMany as jest.Mock).mockResolvedValue(true as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: 'user1', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            is_active: false
        });

        expect(NotificationAlert.updateMany).toHaveBeenCalled();
        expect(NotificationSummary.updateMany).toHaveBeenCalled();
        expect(InviteToken.updateMany).toHaveBeenCalled();
        expect(ApiKey.updateMany).toHaveBeenCalled();
        expect(User.updateMany).toHaveBeenCalled();
        expect(result).toEqual({
            ...mockVessel,
            user: { _id: 'user1', name: 'User 1' }
        });
    });

    it('update handles home_port_location', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: 'user1', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            home_port_location: [10, 20]
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: 'user1', name: 'User 1' }
        });
    });

    it('update does not set country_iso_code when it is falsy', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            country_iso_code: 'USA',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: 'user1', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            name: 'Updated Vessel'
            // country_iso_code is undefined
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: 'user1', name: 'User 1' }
        });
        expect(mockVessel.country_iso_code).toBe('USA'); // Should remain unchanged
    });

    it('update sets country_iso_code when provided', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            country_iso_code: 'USA',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: 'user1', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            country_iso_code: 'CAN'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: 'user1', name: 'User 1' }
        });
        expect(mockVessel.country_iso_code).toBe('CAN');
    });

    it('update throws on location bounds error', async () => {
        const mockVessel = {
            _id: '1',
            name: 'Vessel 1',
            save: jest.fn().mockRejectedValue(new Error('Longitude/latitude is out of bounds') as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            home_port_location: [200, 200]
        })).rejects.toThrow('Location Coordinates are out of bounds');
    });

    it('update throws on general error', async () => {
        const mockVessel = {
            _id: '1',
            name: 'Vessel 1',
            save: jest.fn().mockRejectedValue(new Error('General error') as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            name: 'Updated Vessel'
        })).rejects.toThrow('Failed to update vessel');
    });

    it('create throws when findById returns null after creation', async () => {
        const mockVessel = { _id: '507f1f77bcf86cd799439099', name: 'Vessel 1', created_by: '507f1f77bcf86cd799439011' };

        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        (Vessel.create as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([] as never);

        await expect(vesselService.create({
            name: 'Vessel 1',
            created_by: '507f1f77bcf86cd799439011' as any,
            region_group_id: '507f1f77bcf86cd799439012',
            home_port_location: [10, 20],
            country_iso_code: 'USA'
        })).rejects.toThrow('Failed to create vessel');
    });

    it('update sets thumbnailS3Key to null when uploadVesselThumbnail returns null', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            thumbnail_s3_key: 'old-key',
            save: jest.fn().mockResolvedValue(true as never)
        };
        const mockFile = { buffer: Buffer.from('test'), originalname: 'test.jpg' };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (deleteFileFromS3 as jest.Mock).mockResolvedValue(true as never);
        jest.spyOn(vesselService, 'uploadVesselThumbnail').mockResolvedValue(null as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            thumbnail_file: mockFile as any
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('update throws when findById returns null after update', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([] as never);

        await expect(vesselService.update({
            id: '507f1f77bcf86cd799439011',
            name: 'Updated Vessel'
        })).rejects.toThrow('Failed to update vessel');
    });

    it('fetchPaginated handles vessels with missing user data', async () => {
        (Vessel.countDocuments as jest.Mock).mockResolvedValue(1 as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1', region_group_id: 'group1' }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([
            { _id: 'group1', name: 'Group 1', timezone: 'UTC' }
        ] as never);
        (escapeRegExp as jest.Mock).mockReturnValue('');

        const result = await vesselService.fetchPaginated({});

        expect(result.vessels[0].user).toBeNull();
        expect(result.vessels[0].regionGroup).toEqual({ _id: 'group1', name: 'Group 1', timezone: 'UTC' });
    });

    it('findById handles missing user data', async () => {
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1' }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([] as never);

        const result = await vesselService.findById({ id: '507f1f77bcf86cd799439011' });

        expect(result).toEqual({
            _id: '1',
            name: 'Vessel 1',
            created_by: 'user1',
            user: null
        });
    });

    it('update handles empty unit_id', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            unit_id: ''
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('update handles invalid home_port_location', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            home_port_location: [10] as any
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });

    it('fetchPaginated handles vessels with missing region group data', async () => {
        (Vessel.countDocuments as jest.Mock).mockResolvedValue(1 as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([
            { _id: '1', name: 'Vessel 1', created_by: 'user1', region_group_id: 'group1' }
        ] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'user1', name: 'User 1', email: '<EMAIL>' }
        ] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (escapeRegExp as jest.Mock).mockReturnValue('');

        const result = await vesselService.fetchPaginated({});

        expect(result.vessels[0].user).toEqual({ _id: 'user1', name: 'User 1', email: '<EMAIL>' });
        expect(result.vessels[0].regionGroup).toBeNull();
    });

    it('update handles non-empty unit_id', async () => {
        const mockVessel = {
            _id: '507f1f77bcf86cd799439099',
            name: 'Vessel 1',
            save: jest.fn().mockResolvedValue(true as never)
        };

        (Vessel.findById as jest.Mock).mockResolvedValue(mockVessel as never);
        (Vessel.aggregate as jest.Mock).mockResolvedValue([mockVessel] as never);
        (User.aggregate as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011', name: 'User 1' }] as never);

        const result = await vesselService.update({
            id: '507f1f77bcf86cd799439011',
            unit_id: 'unit123'
        });

        expect(result).toEqual({
            ...mockVessel,
            user: { _id: '507f1f77bcf86cd799439011', name: 'User 1' }
        });
    });
});
