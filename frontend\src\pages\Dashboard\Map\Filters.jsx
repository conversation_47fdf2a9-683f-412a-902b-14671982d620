import {
    Grid,
    Typography,
    Skeleton,
    Slider,
    FormControlLabel,
    Checkbox,
    MenuItem,
    alpha,
    FormControl,
    InputLabel,
    Select,
    ListItemText,
    OutlinedInput,
    IconButton,
} from "@mui/material";
import dayjs from "dayjs";
import { userValues } from "../../../utils";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import CustomDatePicker from "../../../components/CustomDatePicker";
import { Replay } from "@mui/icons-material";
import { useMemo } from "react";
import theme from "../../../theme";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook.jsx";
import { logEvent } from "../../../utils";
const Filters = ({
    journeyStart,
    journeyEnd,
    timeSlider,
    timeSliderKey,
    artifactsCategories,
    selectedArtifactsCategory,
    showDatapoints,
    showArtifacts,
    showAisData,
    showAudioData,
    // homePortsArtifactsMode,
    disableFiltersReset,
    setJourneyStart,
    setJourneyEnd,
    setTimeSlider,
    setTimeSliderKey,
    setSelectedArtifactsCategory,
    setShowDatapoints,
    setShowArtifacts,
    setShowAisData,
    setShowAudioData,
    // setHomePortsArtifactsMode,
    resetFilters,
    loadingVessels,
    selectedNumberOfArtifacts,
    setSelectedNumberOfArtifacts,
    artifactsType,
    setArtifactsType,
    aisArtifactsFilter,
    setAisArtifactsFilter,
}) => {
    const { timezone } = useApp();
    const { user } = useUser();
    const sliderMarks = useMemo(() => {
        const [start, end] = timeSlider;
        const marks = [];
        const oneDayInMilliseconds = 24 * 60 * 60 * 1000;

        for (let timestamp = start; timestamp <= end; timestamp += oneDayInMilliseconds) {
            marks.push({
                value: timestamp,
            });
        }

        return marks;
    }, [timeSlider]);

    return (
        <Grid container flexDirection={"column"} gap={1} color={"#FFFFFF"} paddingX={2} paddingY={2} position={"relative"} className="map-step-4">
            <IconButton
                disabled={disableFiltersReset}
                disableRipple
                sx={{
                    width: 15,
                    height: 15,
                    marginLeft: 1,
                    paddingX: 2,
                    position: "absolute",
                    top: { xs: "-50px", md: "-30px" },
                    right: { xs: "0", md: "35px" },
                }}
                onClick={(e) => {
                    e.stopPropagation();
                    resetFilters();
                }}
            >
                <Replay fontSize="small" />
            </IconButton>
            <Typography fontSize={"16px"} fontWeight={400} textAlign={"center"}>
                Date Range
            </Typography>
            {loadingVessels.length > 0 ? (
                <>
                    <Grid paddingX={4}>
                        <Skeleton variant="rounded" width="100%" height={30} />
                    </Grid>
                    <Grid container justifyContent={"space-between"}>
                        <Grid>
                            <Skeleton variant="rounded" width={100} height={30} />
                        </Grid>
                        <Grid>
                            <Skeleton variant="rounded" width={100} height={30} />
                        </Grid>
                    </Grid>
                </>
            ) : (
                <>
                    <Grid container>
                        <Grid paddingX={8} size={12}>
                            <Slider
                                valueLabelFormat={(v) =>
                                    dayjs(v)
                                        .tz(timezone)
                                        .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))
                                }
                                valueLabelDisplay="auto"
                                key={timeSliderKey}
                                defaultValue={timeSlider}
                                min={timeSlider[0]}
                                max={timeSlider[1]}
                                step={60000}
                                marks={sliderMarks}
                                onChangeCommitted={(e, v) => {
                                    setJourneyStart(dayjs(v[0]));
                                    setJourneyEnd(dayjs(v[1]));
                                    logEvent("MapFilterApplied", { filter: "dateRange", start: v[0], end: v[1] });
                                }}
                                sx={{
                                    color: alpha(theme.palette.background.default, 0.32),
                                    "& .MuiSlider-thumb": {
                                        height: 22,
                                        width: 22,
                                        border: `5px solid ${theme.palette.custom.unfocused}`,
                                    },
                                    "& .MuiSlider-mark": {
                                        height: "7px",
                                        width: "3px",
                                    },
                                }}
                            />
                        </Grid>
                        <Grid container justifyContent={"space-between"} size={12}>
                            <Grid>
                                <CustomDatePicker
                                    value={journeyStart}
                                    onChange={(v) => {
                                        setJourneyStart(v);
                                        setTimeSlider([v.valueOf(), journeyEnd === "now" ? dayjs().valueOf() : journeyEnd.valueOf()]);
                                        setTimeSliderKey((v) => v + 1);
                                        logEvent("MapFilterApplied", { filter: "startDate", value: v.valueOf() });
                                    }}
                                    maxDateTime={dayjs()}
                                    slots={{ openPickerIcon: CalendarMonthIcon }}
                                />
                            </Grid>
                            <Grid>
                                <CustomDatePicker
                                    value={journeyEnd === "now" ? dayjs() : journeyEnd}
                                    onChange={(v) => {
                                        setJourneyEnd(v);
                                        setTimeSlider([journeyStart.valueOf(), v.valueOf()]);
                                        setTimeSliderKey((v) => v + 1);
                                        logEvent("MapFilterApplied", { filter: "endDate", value: v.valueOf() });
                                    }}
                                    minDateTime={journeyStart}
                                    maxDateTime={dayjs()}
                                    slots={{ openPickerIcon: CalendarMonthIcon }}
                                    iconPosition="start"
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </>
            )}
            <Grid container justifyContent={"space-between"} gap={6}>
                <Grid>
                    <FormControlLabel
                        sx={{ gap: 1, margin: 0 }}
                        componentsProps={{ typography: { fontSize: { xs: 12, sm: 16 } } }}
                        control={
                            <Checkbox
                                checked={showDatapoints}
                                onChange={(e) => {
                                    setShowDatapoints(e.target.checked);
                                    logEvent("MapFilterApplied", { filter: "showDatapoints", value: !showDatapoints });
                                }}
                                size="small"
                                disableRipple
                                sx={{
                                    width: 10,
                                    height: 10,
                                }}
                            />
                        }
                        label={"Show Data Points"}
                    />
                </Grid>
                <Grid>
                    <FormControlLabel
                        sx={{ gap: 1, margin: 0 }}
                        componentsProps={{ typography: { fontSize: { xs: 12, sm: 16 } } }}
                        control={
                            <Checkbox
                                checked={showArtifacts}
                                onChange={(e) => {
                                    setShowArtifacts(e.target.checked);
                                    logEvent("MapFilterApplied", { filter: "showArtifacts", value: !showArtifacts });
                                }}
                                size="small"
                                disableRipple
                                sx={{
                                    width: 10,
                                    height: 10,
                                }}
                            />
                        }
                        label={"Show Artifacts"}
                    />
                </Grid>
            </Grid>
            <Grid container justifyContent={"space-between"} gap={6}>
                <Grid>
                    <FormControlLabel
                        sx={{ gap: 1, margin: 0 }}
                        componentsProps={{ typography: { fontSize: { xs: 12, sm: 16 } } }}
                        control={
                            <Checkbox
                                checked={showAisData}
                                onChange={(e) => {
                                    setShowAisData(e.target.checked);
                                    logEvent("MapFilterApplied", { filter: "showAisData", value: !showAisData });
                                }}
                                size="small"
                                disableRipple
                                sx={{
                                    width: 10,
                                    height: 10,
                                }}
                            />
                        }
                        label={"Show AIS Data"}
                    />
                </Grid>
                <Grid>
                    <FormControlLabel
                        sx={{ gap: 1, margin: 0 }}
                        componentsProps={{ typography: { fontSize: { xs: 12, sm: 16 } } }}
                        control={
                            <Checkbox
                                checked={showAudioData}
                                onChange={(e) => {
                                    setShowAudioData(e.target.checked);
                                    logEvent("MapFilterApplied", { filter: "showAudioData", value: !showAudioData });
                                }}
                                size="small"
                                disableRipple
                                sx={{
                                    width: 10,
                                    height: 10,
                                }}
                            />
                        }
                        label={"Show Audio Data"}
                    />
                </Grid>
            </Grid>
            {!artifactsCategories ? (
                <Skeleton variant="rectangular" height={40} />
            ) : artifactsCategories.length === 0 ? (
                <Typography variant="caption">No artifacts found.</Typography>
            ) : (
                <Grid item container flexDirection={"column"} gap={2} marginTop={2}>
                    <Grid>
                        <FormControl sx={{ width: "100%" }} size="small">
                            <InputLabel
                                id="artifacts-multiselect-label"
                                sx={{ color: `${showArtifacts ? "#FFFFFF" : "#9A9CA2"} !important` }}
                                shrink={true}
                            >
                                Artifacts
                            </InputLabel>
                            <Select
                                labelId="artifacts-multiselect-label"
                                id="artifacts-multiselect"
                                multiple
                                value={selectedArtifactsCategory}
                                onChange={(event) => {
                                    const value = event.target.value;
                                    if (value.includes("All")) {
                                        if (selectedArtifactsCategory.length === artifactsCategories.length) {
                                            setSelectedArtifactsCategory([]);
                                            logEvent("MapFilterApplied", { filter: "artifactsCategory", value: [] });
                                        } else {
                                            setSelectedArtifactsCategory(artifactsCategories);
                                            logEvent("MapFilterApplied", { filter: "artifactsCategory", value: artifactsCategories });
                                        }
                                    } else {
                                        setSelectedArtifactsCategory(value);
                                        logEvent("MapFilterApplied", { filter: "artifactsCategory", value });
                                    }
                                }}
                                input={<OutlinedInput label="Artifacts" />}
                                native={false}
                                displayEmpty={true}
                                disabled={!showArtifacts}
                                renderValue={(selected) => {
                                    if (selected.length === artifactsCategories.length) {
                                        return "All";
                                    } else if (selected.length === 0) {
                                        return "None Selected";
                                    } else {
                                        return `${selected.length} Selected`;
                                    }
                                }}
                                sx={{
                                    color: "#FFFFFF",
                                    textTransform: "capitalize",
                                    "&.Mui-disabled fieldset": {
                                        borderColor: "#9A9CA2 !important",
                                    },
                                    "&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg": {
                                        cursor: "not-allowed !important",
                                        color: "#9A9CA2 !important",
                                        "-webkit-text-fill-color": "#9A9CA2 !important",
                                    },
                                }}
                            >
                                <MenuItem value="All">
                                    <Checkbox
                                        checked={selectedArtifactsCategory.length === artifactsCategories.length}
                                        sx={{
                                            padding: "0 10px",
                                        }}
                                    />
                                    <ListItemText primary="All" />
                                </MenuItem>
                                {artifactsCategories.map((type, i) => (
                                    <MenuItem key={i} value={type}>
                                        <Checkbox
                                            checked={selectedArtifactsCategory.includes(type)}
                                            sx={{
                                                padding: "0 10px",
                                            }}
                                        />
                                        <ListItemText primary={type} sx={{ textTransform: "capitalize" }} />
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid>
                        <FormControl sx={{ width: "100%" }} size="small">
                            <InputLabel
                                id="artifacts-select-label"
                                sx={{ color: `${showArtifacts ? "#FFFFFF" : "#9A9CA2"} !important` }}
                                shrink={true}
                            >
                                Visible
                            </InputLabel>
                            <Select
                                labelId="artifacts-select-label"
                                id="artifacts-select"
                                value={selectedNumberOfArtifacts || 100}
                                onChange={(event) => {
                                    setSelectedNumberOfArtifacts(event.target.value);
                                    logEvent("MapFilterApplied", { filter: "numberOfArtifacts", value: event.target.value });
                                }}
                                input={<OutlinedInput label="Visible" />}
                                native={false}
                                displayEmpty={true}
                                disabled={!showArtifacts}
                                renderValue={(selected) => {
                                    if (!selected) {
                                        return "None Selected";
                                    } else {
                                        return `${selected} Artifacts`;
                                    }
                                }}
                                sx={{
                                    color: "#FFFFFF",
                                    textTransform: "capitalize",
                                    "&.Mui-disabled fieldset": {
                                        borderColor: "#9A9CA2 !important",
                                    },
                                    "&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg": {
                                        cursor: "not-allowed !important",
                                        color: "#9A9CA2 !important",
                                        "-webkit-text-fill-color": "#9A9CA2 !important",
                                    },
                                }}
                            >
                                {["all", 100, 250, 500, 1000, 2000].map((type, i) => (
                                    <MenuItem key={i} value={type}>
                                        <ListItemText primary={type} sx={{ textTransform: "capitalize" }} />
                                        {/* {type === "all" && (
                                        <Tooltip title="This may impact the performance dropdown">
                                            <Warning sx={{ color: "yellow" }} />
                                        </Tooltip>
                                    )} */}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid>
                        <FormControl sx={{ width: "100%" }} size="small">
                            <InputLabel id="artifacts-type-label" sx={{ color: `${showArtifacts ? "#FFFFFF" : "#9A9CA2"} !important` }} shrink={true}>
                                Media Type
                            </InputLabel>
                            <Select
                                labelId="artifacts-type-label"
                                id="artifacts-type"
                                label="Media Type"
                                value={artifactsType}
                                onChange={(event) => {
                                    setArtifactsType(event.target.value);
                                    logEvent("MapFilterApplied", { filter: "artifactsType", value: event.target.value });
                                }}
                                input={<OutlinedInput id="artifacts-type-input" label="Media Type" />}
                                native={false}
                                displayEmpty={true}
                                disabled={!showArtifacts}
                                renderValue={(selected) => {
                                    if (!selected) {
                                        return "None Selected";
                                    } else {
                                        return `${selected} Artifacts`;
                                    }
                                }}
                                sx={{
                                    color: "#FFFFFF",
                                    textTransform: "capitalize",
                                    "&.Mui-disabled fieldset": {
                                        borderColor: "#9A9CA2 !important",
                                    },
                                    "&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg": {
                                        cursor: "not-allowed !important",
                                        color: "#9A9CA2 !important",
                                        "-webkit-text-fill-color": "#9A9CA2 !important",
                                    },
                                }}
                            >
                                {["image", "video", "both"].map((type, i) => (
                                    <MenuItem key={i} value={type}>
                                        <ListItemText primary={type} sx={{ textTransform: "capitalize" }} />
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid>
                        <FormControl sx={{ width: "100%" }} size="small">
                            <InputLabel id="ais-artifacts-filter-label" sx={{ color: `${showArtifacts ? "#FFFFFF" : "#9A9CA2"} !important` }} shrink={true}>
                                AIS Artifacts
                            </InputLabel>
                            <Select
                                labelId="ais-artifacts-filter-label"
                                id="ais-artifacts-filter"
                                value={aisArtifactsFilter}
                                onChange={(event) => {
                                    setAisArtifactsFilter(event.target.value);
                                    logEvent("MapFilterApplied", { filter: "aisArtifactsFilter", value: event.target.value });
                                }}
                                input={<OutlinedInput label="AIS Artifacts" />}
                                native={false}
                                displayEmpty={true}
                                disabled={!showArtifacts}
                                renderValue={(selected) => {
                                    if (!selected) {
                                        return "Both AIS and Non-AIS";
                                    } else if (selected === "ais_only") {
                                        return "AIS Artifacts Only";
                                    } else if (selected === "non_ais_only") {
                                        return "Non-AIS Artifacts Only";
                                    } else {
                                        return "Both AIS and Non-AIS";
                                    }
                                }}
                                sx={{
                                    color: "#FFFFFF",
                                    textTransform: "capitalize",
                                    "&.Mui-disabled fieldset": {
                                        borderColor: "#9A9CA2 !important",
                                    },
                                    "&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg": {
                                        cursor: "not-allowed !important",
                                        color: "#9A9CA2 !important",
                                        "-webkit-text-fill-color": "#9A9CA2 !important",
                                    },
                                }}
                            >
                                <MenuItem value="both">
                                    <ListItemText primary="Both AIS and Non-AIS" />
                                </MenuItem>
                                <MenuItem value="ais_only">
                                    <ListItemText primary="AIS Artifacts Only" />
                                </MenuItem>
                                <MenuItem value="non_ais_only">
                                    <ListItemText primary="Non-AIS Artifacts Only" />
                                </MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    {/* <Grid>
                        <FormControl sx={{ width: "100%" }} size="small">
                            <InputLabel
                                id="artifact-details-label"
                                sx={{ color: `${showArtifacts ? "#FFFFFF" : "#9A9CA2"} !important` }}
                                shrink={true}
                            >
                                Artifact Details
                            </InputLabel>
                            <Select
                                labelId="artifact-details-label"
                                id="artifact-details"
                                value={homePortsArtifactsMode}
                                onChange={(event) => {
                                    setHomePortsArtifactsMode(event.target.value);
                                }}
                                input={<OutlinedInput label="Artifact Details" />}
                                native={false}
                                displayEmpty={true}
                                disabled={!showArtifacts}
                                renderValue={(selected) => {
                                    if (selected === defaultValues.homePortsFilterModes.ALL) {
                                        return "Show Both";
                                    } else if (selected === defaultValues.homePortsFilterModes.ONLY_HOME_PORTS) {
                                        return "Show Home Port Artifacts Only";
                                    } else if (selected === defaultValues.homePortsFilterModes.ONLY_NON_HOME_PORTS) {
                                        return "Show Non-Home Port Artifacts Only";
                                    }
                                }}
                                sx={{
                                    color: "#FFFFFF",
                                    textTransform: "capitalize",
                                    "&.Mui-disabled fieldset": {
                                        borderColor: "#9A9CA2 !important",
                                    },
                                    "&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg": {
                                        cursor: "not-allowed !important",
                                        color: "#9A9CA2 !important",
                                        "-webkit-text-fill-color": "#9A9CA2 !important",
                                    },
                                    minWidth: "250px",
                                }}
                            >
                                <MenuItem value={defaultValues.homePortsFilterModes.ALL}>
                                    <ListItemText primary="Show Both" />
                                </MenuItem>
                                <MenuItem value={defaultValues.homePortsFilterModes.ONLY_HOME_PORTS}>
                                    <ListItemText primary="Show Home Port Artifacts Only" />
                                </MenuItem>
                                <MenuItem value={defaultValues.homePortsFilterModes.ONLY_NON_HOME_PORTS}>
                                    <ListItemText primary="Show Non-Home Port Artifacts Only" />
                                </MenuItem>
                            </Select>
                        </FormControl>
                    </Grid> */}
                </Grid>
            )}
        </Grid>
    );
};

export default Filters;
