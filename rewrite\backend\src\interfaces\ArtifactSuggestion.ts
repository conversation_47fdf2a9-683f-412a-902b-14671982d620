import mongoose from "mongoose";

export interface IArtifactSuggestion {
    _id: mongoose.Types.ObjectId;
    search: string;
    click: number;
    impressions: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface IArtifactSynonym {
    _id: mongoose.Types.ObjectId;
    word: string;
    synonyms: string[];
    type: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface ISuggestionResult {
    suggestion: string;
    click: number;
}
