import { jest, beforeEach, describe, it, expect } from '@jest/globals';

describe('Swagger Module', () => {
    beforeEach(() => {
        jest.resetModules()
    });

    it('should export swaggerUi and swaggerDocs', () => {
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        expect(swaggerUi).toBeDefined()
        expect(swaggerDocs).toBeDefined()
        expect(swaggerConfig).toBeDefined()
    });

    it('should export swaggerUi and swaggerDocs in dev environment', () => {
        process.env.NODE_ENV = 'dev'
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        expect(swaggerUi).toBeDefined()
        expect(swaggerDocs).toBeDefined()
        expect(swaggerConfig).toBeDefined()
    });

    it('should simulate calling the response interceptor with /api/users route', () => {
        const { swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        } as any;
        let response = {
            status: 200,
            url: '/api/users',
            body: {
                jwt_token: 'token'
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });

    it('should simulate calling the response interceptor with /api/users/auth route', () => {
        const { swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        } as any;
        let response = {
            status: 200,
            url: '/api/users/auth',
            body: {
                jwt_token: 'token'
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });

    it('should simulate calling the response interceptor with /api/users/auth route with undefined jwt_token', () => {
        const { swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        } as any;
        let response = {
            status: 200,
            url: '/api/users/auth',
            body: {
                jwt_token: undefined
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });
});
