import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('EmailDomains Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create EmailDomains model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/EmailDomains')];

        const EmailDomainsModule = await import('../../models/EmailDomains');
        const EmailDomains = EmailDomainsModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('EmailDomains', expect.any(Object), 'email_domains');
        expect(EmailDomains).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.domain).toBeDefined();
        expect(schemaArg.paths.domain.type).toBe(String);
        expect(schemaArg.paths.domain.required).toBe(true);
    });
});
