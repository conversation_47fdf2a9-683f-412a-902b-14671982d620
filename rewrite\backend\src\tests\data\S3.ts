export const s3TestData = {
    bucketName: 'test-bucket',
    key: 'test-file.jpg',
    region: 'us-east-1',
    signedUrl: 'https://s3.amazonaws.com/test-bucket/test-file.jpg?AWSAccessKeyId=test&Expires=1234567890&Signature=test'
};

export const batchTestData = [
    {
        bucket_name: 'test-bucket-1',
        key: 'test-file-1.jpg',
        region: 'us-east-1'
    },
    {
        bucket_name: 'test-bucket-2',
        key: 'test-file-2.png',
        region: 'us-west-2'
    }
];

export const cloudfrontTestData = {
    key: 'test-file.jpg',
    bucketName: 'test-bucket',
    region: 'us-east-1',
    cloudfrontUrl: 'https://cloudfront.net/test-file.jpg?signature=test'
};

export const cloudfrontBatchTestData = [
    {
        bucketName: 'test-bucket-1',
        key: 'test-file-1.jpg',
        region: 'us-east-1'
    },
    {
        bucketName: 'test-bucket-2',
        key: 'test-file-2.png',
        region: 'us-west-2'
    }
];
