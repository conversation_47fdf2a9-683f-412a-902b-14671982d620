import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import apiKeyService from '../../services/ApiKey.service';
import Api<PERSON>ey from '../../models/ApiKey';
import Vessel from '../../models/Vessel';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));

describe('ApiKeyService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('fetchAll returns aggregated api keys', async () => {
        (ApiKey.aggregate as jest.Mock).mockResolvedValue([{ _id: '1' }] as never);
        const result = await apiKeyService.fetchAll();
        expect(result).toEqual([{ _id: '1' }]);
    });

    it('findById throws on invalid id', async () => {
        await expect(apiKeyService.findById({ id: 'bad' })).rejects.toThrow('Invalid API key id');
    });

    it('findById returns null when not found', async () => {
        (ApiKey.findOne as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.findById({ id: '507f1f77bcf86cd799439011' });
        expect(result).toBeNull();
    });

    it('create then re-fetches by id', async () => {
        (ApiKey.create as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        (ApiKey.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.create({ description: 'd', email: 'e', created_by: '507f1f77bcf86cd799439012' });
        expect(result).toEqual({ _id: '507f1f77bcf86cd799439011' });
    });

    it('update filters undefined fields and returns updated doc', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        (ApiKey.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.update({ id: '507f1f77bcf86cd799439011', description: 'x' });
        expect(result).toEqual({ _id: '507f1f77bcf86cd799439011' });
    });

    it('update throws on invalid id', async () => {
        await expect(apiKeyService.update({ id: 'bad', description: 'x' })).rejects.toThrow('Invalid API key id');
    });

    it('update returns null when not found', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.update({ id: '507f1f77bcf86cd799439011', description: 'x' });
        expect(result).toBeNull();
    });

    it('updateAllowedEndpoints throws on invalid endpoint', async () => {
        await expect(apiKeyService.updateAllowedEndpoints({ id: '507f1f77bcf86cd799439011', allowed_endpoints: [999999] })).rejects.toThrow('Invalid endpoint provided');
    });

    it('updateAllowedEndpoints returns updated key', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        (ApiKey.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.updateAllowedEndpoints({ id: '507f1f77bcf86cd799439011', allowed_endpoints: [101] });
        expect(result).toEqual({ _id: '507f1f77bcf86cd799439011' });
    });

    it('updateAllowedEndpoints throws on invalid id', async () => {
        await expect(apiKeyService.updateAllowedEndpoints({ id: 'bad', allowed_endpoints: [1] })).rejects.toThrow('Invalid API key id');
    });

    it('updateAllowedEndpoints returns null when not found', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.updateAllowedEndpoints({ id: '507f1f77bcf86cd799439011', allowed_endpoints: [101] });
        expect(result).toBeNull();
    });

    it('updateAllowedVessels throws when inactive vessels provided', async () => {
        (Vessel.find as jest.Mock).mockResolvedValue([{ _id: 'v1', is_active: false }] as never);
        await expect(apiKeyService.updateAllowedVessels({ id: '507f1f77bcf86cd799439011', allowed_vessels: ['v1'] })).rejects.toThrow('Cannot assign inactive vessels to API key');
    });

    it('updateAllowedVessels returns updated key', async () => {
        (Vessel.find as jest.Mock).mockResolvedValue([] as never);
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        (ApiKey.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.updateAllowedVessels({ id: '507f1f77bcf86cd799439011', allowed_vessels: ['v1'] });
        expect(result).toEqual({ _id: '507f1f77bcf86cd799439011' });
    });

    it('updateAllowedVessels throws on invalid id', async () => {
        await expect(apiKeyService.updateAllowedVessels({ id: 'bad', allowed_vessels: [] })).rejects.toThrow('Invalid API key id');
    });

    it('updateAllowedVessels returns null when not found', async () => {
        (Vessel.find as jest.Mock).mockResolvedValue([] as never);
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.updateAllowedVessels({ id: '507f1f77bcf86cd799439011', allowed_vessels: [] });
        expect(result).toBeNull();
    });

    it('updateRevocationStatus returns updated key', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        (ApiKey.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.updateRevocationStatus({ id: '507f1f77bcf86cd799439011', is_revoked: true });
        expect(result).toEqual({ _id: '507f1f77bcf86cd799439011' });
    });

    it('updateRevocationStatus throws on invalid id', async () => {
        await expect(apiKeyService.updateRevocationStatus({ id: 'bad', is_revoked: true })).rejects.toThrow('Invalid API key id');
    });

    it('updateRevocationStatus returns null when not found', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.updateRevocationStatus({ id: '507f1f77bcf86cd799439011', is_revoked: true });
        expect(result).toBeNull();
    });

    it('delete returns true when updated', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
        const result = await apiKeyService.delete({ id: '507f1f77bcf86cd799439011' });
        expect(result).toBe(true);
    });

    it('delete returns false when nothing updated', async () => {
        (ApiKey.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
        const result = await apiKeyService.delete({ id: '507f1f77bcf86cd799439011' });
        expect(result).toBe(false);
    });
});


