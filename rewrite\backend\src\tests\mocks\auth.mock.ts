import { jest } from '@jest/globals';
import jwt from 'jsonwebtoken';
import { getUser } from '../../queries/User';

export const mockUserJwtVerification = (userId: string) => {
    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userId });
};

export const mockApiKeyJwtVerification = (apiKeyId: string) => {
    (jwt.verify as jest.Mock).mockReturnValue({ api_key_id: apiKeyId });
};

export const setupAuthMocks = (
    authMethod: string,
    userOrApiKey: any,
    nonAuthToken: string,
    User: any,
    ApiKey: any,
    mockModel: boolean = true
) => {
    if (authMethod === 'user') {
        mockUserJwtVerification(userOrApiKey.nonAuthorized._id);
        
        const userWithToken = {
            ...userOrApiKey.nonAuthorized,
            jwt_tokens: [nonAuthToken.replace('Bearer ', '')]
        };

        if (mockModel){
            jest.spyOn(User, 'aggregate').mockResolvedValue([userWithToken]);
        } else {
            (getUser as jest.Mock).mockResolvedValueOnce(userWithToken as never);
        }
    } else {
        mockApiKeyJwtVerification(userOrApiKey.nonAuthorized._id);
        
        jest.spyOn(ApiKey, 'findOne').mockResolvedValue(userOrApiKey.nonAuthorized);
    }
};

export const setupAuthorizedAuthMocks = (
    authMethod: string,
    userOrApiKey: any,
    authToken: string,
    User: any,
    ApiKey: any,
    mockModel: boolean = true
) => {
    if (authMethod === 'user') {
        mockUserJwtVerification(userOrApiKey.authorized._id);
        
        const userWithToken = {
            ...userOrApiKey.authorized,
            jwt_tokens: [authToken.replace('Bearer ', '')]
        };

        if (mockModel){
            jest.spyOn(User, 'aggregate').mockResolvedValue([userWithToken]);
        } else {
            (getUser as jest.Mock).mockResolvedValueOnce(userWithToken as never);
        }
    } else {
        mockApiKeyJwtVerification(userOrApiKey.authorized._id);
        
        jest.spyOn(ApiKey, 'findOne').mockResolvedValue(userOrApiKey.authorized);
    }
};

export default {
    mockUserJwtVerification,
    mockApiKeyJwtVerification,
    setupAuthMocks,
    setupAuthorizedAuthMocks
};