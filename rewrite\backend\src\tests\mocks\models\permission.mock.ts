import { jest } from '@jest/globals';

export const find = jest.fn();
export const findOne = jest.fn();
export const findById = jest.fn();
export const create = jest.fn();
export const findByIdAndUpdate = jest.fn();
export const findByIdAndDelete = jest.fn();
export const countDocuments = jest.fn();
export const aggregate = jest.fn();

export const mockPermission = {
    find,
    findOne,
    findById,
    create,
    findByIdAndUpdate,
    findByIdAndDelete,
    countDocuments,
    aggregate,
};

export const setupPermissionTest = () => {
    return {
        mockPermission,
    };
};

export const cleanupPermissionTest = () => {
    jest.clearAllMocks();
};

export default mockPermission;

