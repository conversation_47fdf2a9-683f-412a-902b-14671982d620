import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('InviteToken Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create InviteToken model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/InviteToken')];

        const InviteTokenModule = await import('../../models/InviteToken');
        const InviteToken = InviteTokenModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('InviteToken', expect.any(Object), 'invite_tokens');
        expect(InviteToken).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.token).toBeDefined();
        expect(schemaArg.paths.token.type).toBe(String);
        expect(schemaArg.paths.token.required).toBe(true);

        expect(schemaArg.paths.invited_by).toBeDefined();
        expect(schemaArg.paths.invited_by.type).toBeDefined();
        expect(schemaArg.paths.invited_by.required).toBe(true);

        expect(schemaArg.paths.email).toBeDefined();
        expect(schemaArg.paths.email.type).toBe(String);
        expect(schemaArg.paths.email.required).toBe(true);

        expect(schemaArg.paths.organization_id).toBeDefined();
        expect(schemaArg.paths.organization_id.type).toBeDefined();

        expect(schemaArg.paths.role_id).toBeDefined();
        expect(schemaArg.paths.role_id.type).toBe(Number);
        expect(schemaArg.paths.role_id.required).toBe(true);

        expect(schemaArg.paths.role).toBeDefined();
        expect(schemaArg.paths.role.type).toBe(String);
        expect(schemaArg.paths.role.required).toBe(true);

        expect(schemaArg.paths.allowed_vessels).toBeDefined();
        expect(schemaArg.paths.allowed_vessels.type).toBeDefined();
        expect(schemaArg.paths.allowed_vessels.default).toEqual([]);

        expect(schemaArg.paths.short_token).toBeDefined();
        expect(schemaArg.paths.short_token.type).toBe(String);
        expect(schemaArg.paths.short_token.required).toBe(true);
        expect(schemaArg.paths.short_token.unique).toBe(true);

        expect(schemaArg.paths.is_used).toBeDefined();
        expect(schemaArg.paths.is_used.type).toBe(Boolean);
        expect(schemaArg.paths.is_used.default).toBe(false);

        expect(schemaArg.paths.is_deleted).toBeDefined();
        expect(schemaArg.paths.is_deleted.type).toBe(Boolean);
        expect(schemaArg.paths.is_deleted.default).toBe(false);
    });
});
