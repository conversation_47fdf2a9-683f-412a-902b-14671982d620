import { jest } from '@jest/globals';

export const createMockRole = (overrides: any = {}) => ({
    _id: '507f1f77bcf86cd799439013',
    role_id: 1,
    role_name: 'Admin',
    hierarchy_number: 1,
    denied_permissions: [],
    ...overrides
});

export const find = jest.fn();
export const findOne = jest.fn();
export const findById = jest.fn();
export const create = jest.fn();
export const updateOne = jest.fn();
export const deleteOne = jest.fn();
export const aggregate = jest.fn();
export const countDocuments = jest.fn();
export const findOneAndDelete = jest.fn();
export const bulkWrite = jest.fn();

export default {
    find,
    findOne,
    findById,
    create,
    updateOne,
    deleteOne,
    aggregate,
    countDocuments,
    findOneAndDelete,
    bulkWrite,
};

export const createMockRoleDocument = (data: any = {}) => {
    const roleData = createMockRole(data);
    
    return {
        ...roleData,
        save: (jest.fn() as any).mockResolvedValue(roleData),
        remove: (jest.fn() as any).mockResolvedValue(roleData),
        toObject: jest.fn().mockReturnValue(roleData),
        toJSON: jest.fn().mockReturnValue(roleData)
    };
};
