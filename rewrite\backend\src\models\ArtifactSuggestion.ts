import mongoose from "mongoose";
import db from "../modules/db";
import { IArtifactSuggestion } from "../interfaces/ArtifactSuggestion";

const ArtifactSuggestionSchema = new mongoose.Schema({
    search: { type: String, required: true },
    click: { type: Number, default: 0 },
    impressions: { type: Number, default: 0 },
});

ArtifactSuggestionSchema.index({ search: 1 }, { unique: true });

const ArtifactSuggestion = db.qm.model<IArtifactSuggestion>("ArtifactSuggestion", ArtifactSuggestionSchema, "artifact_suggestions");

export default ArtifactSuggestion;
