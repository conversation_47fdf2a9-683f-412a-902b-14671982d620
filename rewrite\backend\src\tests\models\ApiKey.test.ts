import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ApiKey Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ApiKey model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ApiKey')];

        const ApiKeyModule = await import('../../models/ApiKey');
        const ApiKey = ApiKeyModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ApiKey', expect.any(Object), 'api_keys');
        expect(ApiKey).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const apiKeyDefault = schemaArg.paths.api_key.default();
        expect(typeof apiKeyDefault).toBe('string');
        expect(apiKeyDefault.length).toBe(32);

        expect(schemaArg.paths.is_deleted.default).toBe(false);
        expect(schemaArg.paths.is_revoked.default).toBe(false);
        expect(schemaArg.paths.requests.default).toBe(0);
        expect(schemaArg.paths.requests_endpoints.default).toEqual({});
        expect(schemaArg.paths.allowed_vessels.default).toEqual([]);

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});