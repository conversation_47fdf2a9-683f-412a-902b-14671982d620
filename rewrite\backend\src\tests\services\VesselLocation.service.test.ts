import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import vesselLocationService from '../../services/VesselLocation.service';
import db from '../../modules/db';
import { getSimplifiedCoords, getLocationsCollections } from '../../utils/functions';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

function makeAsyncCursor(items: any[]) {
    const cursor: any = {
        hint: jest.fn().mockReturnThis(),
        batchSize: jest.fn().mockReturnThis(),
        [Symbol.asyncIterator]: async function* () {
            for (const it of items) yield it;
        },
    };
    return cursor;
}

describe('VesselLocationService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('findByDateRange uses optimized collection when available', async () => {
        (db as any).locationsOptimized.db = { listCollections: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([{}] as never) }) };
        (db as any).locationsOptimized.collection = jest.fn().mockReturnValue({ find: jest.fn().mockReturnValue(makeAsyncCursor([{ _id: 1 }])) });
        const res = await vesselLocationService.findByDateRange({ dateRange: ['2024-05-01', '2024-05-15'], vesselIds: ['V'], projection: { _id: 1 } });
        expect(res.length).toBe(1);
    });

    it('findByDateRange uses raw collection and optimizes when optimized missing', async () => {
        (db as any).locationsOptimized.db = { listCollections: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([] as never) }) };
        (db as any).locationsRaw.collection = jest.fn().mockReturnValue({ find: jest.fn().mockReturnValue(makeAsyncCursor([{ _id: 1, metadata: { onboardVesselId: 'V' } }])) });
        (getSimplifiedCoords as any).mockReturnValueOnce([{ _id: 1, metadata: { onboardVesselId: 'V' }, timestamp: new Date().toISOString() }] as never);
        const res = await vesselLocationService.findByDateRange({ dateRange: ['2024-05-01', '2024-05-15'], vesselIds: ['V'], projection: { _id: 1 } });
        expect(res.length).toBe(1);
    });

    it('calculateOptimalBatchSize adjusts for optimized flag', () => {
        const a = vesselLocationService.calculateOptimalBatchSize(['2024-01-01', '2024-01-11'], 0);
        const b = vesselLocationService.calculateOptimalBatchSize(['2024-01-01', '2024-01-11'], 1);
        expect(a).toBeGreaterThan(b);
    });

    it('optimizeUngroupedCoords groups by vessel and simplifies', () => {
        (getSimplifiedCoords as any).mockReturnValueOnce([{ id: 1 }, { id: 2 }] as never);
        const res = vesselLocationService.optimizeUngroupedCoords([
            { metadata: { onboardVesselId: 'A' }, timestamp: '2024-01-01' } as any,
            { metadata: { onboardVesselId: 'A' }, timestamp: '2024-01-02' } as any,
        ]);
        expect(Array.isArray(res)).toBe(true);
    });

    it('findLastKnownLocation returns single vessel last location', async () => {
        (db as any).lookups.collection = jest.fn().mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 1 } as never) });
        const res = await vesselLocationService.findLastKnownLocation({ vesselId: '507f1f77bcf86cd799439011', projection: { _id: 1 } });
        expect(res).toEqual({ _id: 1 });
    });

    it('findLastKnownLocation throws when vesselIds without projection.vessel_id', async () => {
        await expect(
            vesselLocationService.findLastKnownLocation({ vesselIds: ['507f1f77bcf86cd799439011'], projection: {} as any }),
        ).rejects.toThrow('vessel_id must be provided in projection');
    });

    it('findLastKnownLocation returns grouped results with nulls for missing', async () => {
        (db as any).lookups.collection = jest.fn().mockReturnValue({
            find: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([{ vessel_id: 'A', x: 1 }] as never) }),
        });
        const res = (await vesselLocationService.findLastKnownLocation({
            vesselIds: ['A', 'B'],
            projection: { vessel_id: 1 },
        })) as Record<string, any>;
        expect(res['A']).toEqual({ vessel_id: 'A', x: 1 });
        expect(res['B']).toBeNull();
    });

    it('findClosestLocation returns null when no collections', async () => {
        (getLocationsCollections as jest.Mock).mockResolvedValueOnce([] as never);
        const res = await vesselLocationService.findClosestLocation({ vesselId: 'V', timestampISO: '2024-01-01T00:00:00.000Z' });
        expect(res).toBeNull();
    });

    it('findClosestLocation returns closest location', async () => {
        (getLocationsCollections as jest.Mock).mockResolvedValueOnce([
            {
                aggregate: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([
                        {
                            _id: 1,
                            timestamp: new Date().toISOString(),
                            groundSpeed: 1,
                            isStationary: false,
                            latitude: 20,
                            longitude: 10,
                        },
                    ] as never),
                }),
            },
        ] as never);
        const res = await vesselLocationService.findClosestLocation({ vesselId: 'V', timestampISO: '2024-01-01T00:00:00.000Z' });
        expect(res?.latitude).toBe(20);
        expect(res?.longitude).toBe(10);
    });

    it('findClosestLocation throws on internal error', async () => {
        (getLocationsCollections as jest.Mock).mockResolvedValueOnce([
            { aggregate: jest.fn().mockImplementation(() => { throw new Error('x'); }) },
        ] as never);
        await expect(
            vesselLocationService.findClosestLocation({ vesselId: 'V', timestampISO: '2024-01-01T00:00:00.000Z' }),
        ).rejects.toThrow('Failed to find closest location');
    });

    it('findLastKnownLocation throws when neither vesselId nor vesselIds provided', async () => {
        await expect(
            vesselLocationService.findLastKnownLocation({ projection: { _id: 1 } } as any),
        ).rejects.toThrow('Either vesselId or vesselIds must be provided');
    });

    it('findByDateRange throws when date range spans different months', async () => {
        await expect(
            vesselLocationService.findByDateRange({ 
                dateRange: ['2024-05-01', '2024-06-15'], 
                vesselIds: ['V'], 
                projection: { _id: 1 } 
            }),
        ).rejects.toThrow('Date range must be within the same month');
    });

    it('findLastKnownLocation handles string vesselId conversion', async () => {
        (db as any).lookups.collection = jest.fn().mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 1 } as never) });
        const res = await vesselLocationService.findLastKnownLocation({ vesselId: '507f1f77bcf86cd799439011', projection: { _id: 1 } });
        expect(res).toEqual({ _id: 1 });
    });

    it('findLastKnownLocation handles string vesselIds conversion', async () => {
        (db as any).lookups.collection = jest.fn().mockReturnValue({
            find: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([{ vessel_id: 'A', x: 1 }] as never) }),
        });
        const res = (await vesselLocationService.findLastKnownLocation({
            vesselIds: ['A', 'B'],
            projection: { vessel_id: 1 },
        })) as Record<string, any>;
        expect(res['A']).toEqual({ vessel_id: 'A', x: 1 });
        expect(res['B']).toBeNull();
    });

    it('findClosestLocation returns null when no latitude/longitude', async () => {
        (getLocationsCollections as jest.Mock).mockResolvedValueOnce([
            {
                aggregate: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([
                        {
                            _id: 1,
                            timestamp: new Date().toISOString(),
                            groundSpeed: 1,
                            isStationary: false,
                        },
                    ] as never),
                }),
            },
        ] as never);
        const res = await vesselLocationService.findClosestLocation({ vesselId: 'V', timestampISO: '2024-01-01T00:00:00.000Z' });
        expect(res).toBeNull();
    });

    it('findLastKnownLocation handles ObjectId vesselId', async () => {
        const objectId = new (require('mongoose').Types.ObjectId)();
        (db as any).lookups.collection = jest.fn().mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 1 } as never) });
        const res = await vesselLocationService.findLastKnownLocation({ vesselId: objectId, projection: { _id: 1 } });
        expect(res).toEqual({ _id: 1 });
    });

    it('findLastKnownLocation handles ObjectId vesselIds', async () => {
        const objectId = new (require('mongoose').Types.ObjectId)();
        (db as any).lookups.collection = jest.fn().mockReturnValue({
            find: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([{ vessel_id: objectId, x: 1 }] as never) }),
        });
        const res = (await vesselLocationService.findLastKnownLocation({
            vesselIds: [objectId, 'B'],
            projection: { vessel_id: 1 },
        })) as Record<string, any>;
        expect(res[objectId.toString()]).toEqual({ vessel_id: objectId, x: 1 });
        expect(res['B']).toBeNull();
    });
});


