import { Grid, Typography, MenuItem, Tooltip, CircularProgress, Box, Chip, IconButton, Menu, Skeleton } from "@mui/material";
import { useEffect, useState } from "react";
import axiosInstance from "../../../../axios";
import theme from "../../../../theme";
import { ArrowDropDown, SentimentVeryDissatisfied } from "@mui/icons-material";
import NotificationsIcon from "@mui/icons-material/Notifications";
import NotificationsOffIcon from "@mui/icons-material/NotificationsOff";
import { DataGrid } from "@mui/x-data-grid";
import DeleteSummaryModal from "./DeleteSummaryModal";
import EnableSummaryModal from "./EnableSummaryModal";
import SummaryCustomFooter from "./SummaryCustomFooter";
import DeleteButton from "../../../../components/DeleteButton";
import EditButton from "../../../../components/EditButton";
import CreateEditSummaryModal from "./CreateEditModal";

const Summary = ({ vessels, showSummaryModal, setShowSummaryModal, emailDomains, isEditModal, setIsEditModal }) => {
    const [summary, setSummary] = useState([]);
    const [filteredSummary, setFilteredSummary] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const [totalCount, setTotalCount] = useState(0);
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const [editSummary, setEditSummary] = useState();

    const [deleteKey, setDeleteKey] = useState();
    const [deleting, setDeleting] = useState();

    const [enableKey, setEnableKey] = useState();
    const [enable, setEnable] = useState();
    const [anchorEl, setAnchorEl] = useState({ element: null, id: null });

    const [notificationAddLoad, setNotificationAddLoad] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [parentSize, setParentSize] = useState({ width: 0, height: 0 });

    useEffect(() => {
        const updateSize = () => {
            // Find the child with class `MuiDataGrid-main`
            const mainElement = document.querySelector("#parent-ref .MuiDataGrid-main");
            if (mainElement) {
                const { clientWidth, clientHeight } = mainElement;
                setParentSize({ width: clientWidth, height: clientHeight });
            }
        };

        updateSize();
        window.addEventListener("resize", updateSize);
        return () => window.removeEventListener("resize", updateSize);
    }, []);

    const fetchSummary = async () => {
        setIsLoading(true);
        try {
            const params = new URLSearchParams({ page, page_size: rowsPerPage });

            const { data, total_documents } = await axiosInstance.get(`/summaryReports?${params.toString()}`).then((res) => res.data);
            // .catch(err => {
            //     console.error(`Error fetching notifications`, err)
            // });

            if (Array.isArray(data) && data.length > 0) {
                const addedKey = data.map((e, i) => ({ serial: i + 1, ...e }));
                setSummary(addedKey);
                setTotalCount(total_documents);
            } else {
                setSummary([]);
            }
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error(`Error fetching artifacts in Events`, err);
        }
    };

    useEffect(() => {
        setFilteredSummary(summary);
    }, [summary]);

    useEffect(() => {
        fetchSummary();
    }, [page, rowsPerPage, deleting, notificationAddLoad, enable]); //Instead of refresing the api we have to emit event from backend

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
        setPage(1);
    };

    const columns = [
        {
            field: "serial",
            headerName: "",
            maxWidth: 50,
            renderCell: (index) => {
                return (page - 1) * rowsPerPage + index.row.serial;
            },
        },
        {
            field: "vessel_ids",
            headerName: "Vessels",
            minWidth: 250,
            renderCell: (params) => {
                if (!vessels || vessels.length === 0) {
                    return <Skeleton variant="text" animation="pulse" width={250} sx={{ display: "flex", alignItems: "center", height: "100%" }} />;
                }

                const vesselIds = params.row.vessel_ids || [];

                let vesselNames = [];

                // Empty array means "all selected"
                if (vesselIds.length === 0) {
                    vesselNames = vessels.map((v) => v.name);
                } else {
                    vesselNames = vessels.filter((v) => vesselIds.includes(v.vessel_id)).map((v) => v.name);
                }
                // deprecated title based implementation, leads to consistency issues when updating
                // if (!Array.isArray(params.row.title)) {
                //     params.row.title = params.row.title ? [params.row.title] : [];
                // }
                // const title =
                //     params.row.title && params.row.title.length > 0
                //         ? params.row.title.includes("all")
                //             ? vessels.map((v) => v.name)
                //             : params.row.title
                //         : [];
                const handleClick = (event) => {
                    setAnchorEl({ element: event.currentTarget, id: params.row._id });
                };
                const handleClose = () => {
                    setAnchorEl({ element: null, id: null });
                };
                return (
                    <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                        <Chip
                            sx={{
                                paddingLeft: 0.5,
                                paddingRight: 2,
                                display: "flex",
                                flexDirection: "row-reverse",
                                width: "fit-content",
                                minWidth: { xs: "100%", xl: "50%" },
                                borderRadius: "5px",
                                justifyContent: "space-between",
                                cursor: "pointer",
                            }}
                            icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                            label="Observing Vessels"
                            onClick={handleClick}
                        />
                        <Menu
                            anchorEl={anchorEl.element}
                            open={anchorEl.id === params.row._id}
                            onClose={handleClose}
                            sx={{
                                maxHeight: "400px",
                                "& .MuiMenu-paper": {
                                    maxWidth: `${anchorEl?.offsetWidth}px !important`,
                                    width: `${anchorEl?.offsetWidth}px !important`,
                                    transition: "none !important",
                                },
                                "& .MuiMenuItem-root": {
                                    whiteSpace: "normal",
                                    wordBreak: "break-word",
                                },
                            }}
                        >
                            {vesselNames.map((tit, index) => (
                                <MenuItem key={index} onClick={handleClose}>
                                    {tit}
                                </MenuItem>
                            ))}
                        </Menu>
                    </Box>
                );
            },
            valueGetter: (value) => value.row?.vessel_ids || "",
        },
        {
            field: "preference",
            headerName: "Summary Preference",
            minWidth: 200,
            renderCell: ({ row }) => <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>{row.preference.join(", ")}</Box>,
        },
        // {
        //     field: "receivers",
        //     headerName: "Configured Emails",
        //     minWidth: 100,
        //     renderCell: ({ row }) => <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>{row.receivers.length + 1}</Box>,
        // },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 150,
            headerAlign: "center",
            renderCell: (params) => (
                <Grid container justifyContent={"center"} spacing={1}>
                    <Grid>
                        <EditButton
                            onClick={() => {
                                setEditSummary(params.row);
                                setShowSummaryModal(true);
                                setIsEditModal(true);
                            }}
                        />
                    </Grid>
                    <Grid>
                        {enable === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <Tooltip enterDelay={300} title={params.row.is_enabled ? "Enabled" : "Disabled"} placement="bottom">
                                <IconButton
                                    data-testid="handle-enable-key"
                                    onClick={() => setEnableKey(params.row)}
                                    sx={{
                                        background: "#E600000D",
                                        border: `1px solid ${theme.palette.custom.borderColor}`,
                                        borderRadius: "5px",
                                        padding: "8px",
                                    }}
                                >
                                    {params.row.is_enabled ? (
                                        <NotificationsIcon color="success" sx={{ fontSize: "18px" }} />
                                    ) : (
                                        <NotificationsOffIcon color="error" sx={{ fontSize: "18px" }} />
                                    )}
                                </IconButton>
                            </Tooltip>
                        )}
                    </Grid>
                    <Grid>
                        {deleting === params.row._id ? <CircularProgress size={18} /> : <DeleteButton onClick={() => setDeleteKey(params.row)} />}
                    </Grid>
                </Grid>
            ),
            sortable: false,
        },
    ];

    const columnsWithouFilters = [
        ...columns.map((col) => ({
            ...col,
            filterable: false,
            sortable: false,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
            flex: 1,
        })),
    ];
    const noRowsOverlay = () => (
        <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
            <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
            <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                No data available
            </Typography>
        </Grid>
    );

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                {
                    <DataGrid
                        loading={isLoading || vessels.length > 0 ? false : true}
                        disableRowSelectionOnClick
                        rows={filteredSummary}
                        columns={columnsWithouFilters}
                        getRowId={(row) => row._id}
                        slots={{
                            footer: () => (
                                <SummaryCustomFooter
                                    page={page}
                                    rowsPerPage={rowsPerPage}
                                    totalRows={totalCount}
                                    onPageChange={handlePageChange}
                                    onRowsPerPageChange={handlePageSizeChange}
                                />
                            ),
                            noRowsOverlay,
                        }}
                    />
                }
            </Grid>
            <DeleteSummaryModal deleteKey={deleteKey} setDeleteKey={setDeleteKey} setDeleting={setDeleting} />
            {enableKey && <EnableSummaryModal enableKey={enableKey} setEnableKey={setEnableKey} setEnable={setEnable} />}
            {editSummary !== undefined && vessels.length > 0 && isEditModal && (
                <CreateEditSummaryModal
                    showSummaryModal={showSummaryModal}
                    setShowSummaryModal={setShowSummaryModal}
                    editSummary={editSummary}
                    setEditSummary={setEditSummary}
                    notificationAddLoad={notificationAddLoad}
                    setNotificationAddLoad={setNotificationAddLoad}
                    vessels={vessels}
                    emailDomains={emailDomains}
                    isEditModal={true}
                />
            )}
            {vessels && vessels.length > 0 && !isEditModal && (
                <CreateEditSummaryModal
                    showSummaryModal={showSummaryModal}
                    setShowSummaryModal={setShowSummaryModal}
                    editSummary={editSummary}
                    setEditSummary={setEditSummary}
                    notificationAddLoad={notificationAddLoad}
                    setNotificationAddLoad={setNotificationAddLoad}
                    vessels={vessels}
                    emailDomains={emailDomains}
                    isEditModal={false}
                />
            )}
        </Grid>
    );
};

export default Summary;
