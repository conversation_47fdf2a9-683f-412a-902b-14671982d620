import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import artifactFlagService from '../../services/ArtifactFlag.service';
import ArtifactFlag from '../../models/ArtifactFlag';
import db from '../../modules/db';

jest.mock('../../models/ArtifactFlag', () => require('../mocks/models/artifactFlag.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));

describe('ArtifactFlagService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('flagArtifact throws when artifact not found', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
        await expect(artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('Artifact not found');
    });

    it('flagArtifact throws when already flagged', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A' } as never) });
        (ArtifactFlag.findOne as jest.Mock).mockResolvedValue({ _id: 'F' } as never);
        await expect(artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('You have already flagged this artifact');
    });

    it('flagArtifact creates and saves flag', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A' } as never) });
        (ArtifactFlag.findOne as jest.Mock).mockResolvedValue(null as never);
        const flag = await artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24));
        expect(flag).toBeDefined();
    });

    it('getFlaggedArtifacts returns [] when no ids', async () => {
        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([] as never);
        const res = await artifactFlagService.getFlaggedArtifacts();
        expect(res).toEqual([]);
    });

    it('getFlaggedArtifacts maps artifacts and filters archived', async () => {
        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'A1', flags: [{ _id: 'F', flaggedBy: 'U', flaggedByUser: { _id: 'U' }, flaggedAt: 'T' }], flagCount: 1, latestFlagDate: 'T' },
        ] as never);
        (db.qmai.collection as jest.Mock).mockReturnValue({
            find: jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([
                    { _id: { toString: () => 'A1' }, portal: {}, other: 1 },
                    { _id: { toString: () => 'A2' }, portal: { is_archived: true } },
                ] as never),
            } as never),
        });
        const res = await artifactFlagService.getFlaggedArtifacts();
        expect(res.length).toBe(1);
        expect(res[0].flags[0].user).toEqual({ _id: 'U' });
    });

    it('getFlaggedArtifacts drops entries with missing artifact', async () => {
        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            { _id: 'A2', flags: [], flagCount: 1, latestFlagDate: 'T' },
        ] as never);
        (db.qmai.collection as jest.Mock).mockReturnValue({
            find: jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([
                    { _id: { toString: () => 'A1' }, portal: {} },
                ] as never),
            } as never),
        });
        const res = await artifactFlagService.getFlaggedArtifacts();
        expect(res).toEqual([]);
    });

    it('unflagArtifact throws when flag missing', async () => {
        (ArtifactFlag.findOneAndDelete as jest.Mock).mockResolvedValue(null as never);
        await expect(artifactFlagService.unflagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('Flag not found');
    });

    it('unflagArtifact returns deleted flag', async () => {
        (ArtifactFlag.findOneAndDelete as jest.Mock).mockResolvedValue({ _id: 'F' } as never);
        const res = await artifactFlagService.unflagArtifact('a'.repeat(24), 'u'.repeat(24));
        expect(res).toEqual({ _id: 'F' });
    });

    it('getUserFlaggedArtifactIds maps ids', async () => {
        (ArtifactFlag.find as any).mockResolvedValue([{ artifactId: { toString: () => 'A1' } }]);
        const res = await artifactFlagService.getUserFlaggedArtifactIds('u'.repeat(24));
        expect(res).toEqual(['A1']);
    });

    it('removeAllFlagsFromArtifact returns deleteMany result', async () => {
        (ArtifactFlag.deleteMany as any).mockResolvedValue({ deletedCount: 3 });
        const res = await artifactFlagService.removeAllFlagsFromArtifact('a'.repeat(24));
        expect(res).toEqual({ deletedCount: 3 });
    });
});


