import { CircularProgress, Grid } from "@mui/material";
import { useEffect, useState, memo, useCallback, useRef } from "react";
import axiosInstance from "../../../axios";
import GroupedDetailModal from "./GroupedDetailModal";
import FilterEventModal from "./FilterEventModal";
import { useParams } from "react-router-dom";
import theme from "../../../theme";
import { getSocket } from "../../../socket";
import VirtualizedCardList from "./VirtualizedCardList";
import { groupArtifacts, logEvent } from "../../../utils";
import useGroupRegions from "../../../hooks/GroupRegionHook";

const Events = ({ showFilterModal, setShowFilterModal, filters, setFilters, vessels, tab, onCompletion, onLoadingChange }) => {
    const { id } = useParams();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const hasMore = useRef(true);
    const [page, setPage] = useState(1);
    const [filterItem, setFilterItem] = useState({});
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();
    const loadMoreTimeoutRef = useRef();
    const listRef = useRef();
    const originalGroupsRef = useRef([]); // Store original group arrays for regrouping
    const { regions } = useGroupRegions();

    const [selectedTime, setSelectedTime] = useState("");
    const [selectedType, setSelectedType] = useState("");
    const [selectedArea, setSelectedArea] = useState([]);
    const [selectedVessel, setSelectedVessel] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState([]);
    const [timeStart, setTimeStart] = useState(new Date());
    const [timeEnd, setTimeEnd] = useState(new Date());
    const [selectedSize, setSelectedSize] = useState([]);
    const [selectedColor, setSelectedColor] = useState([]);
    const [selectedWeapon, setSelectedWeapon] = useState([]);
    const [selectedHostVessel, setSelectedHostVessel] = useState("non_host");
    const [regionGroups, setRegionGroups] = useState([]);
    const [selectedAisFilter, setSelectedAisFilter] = useState("both");

    // Initialize with default host_vessel filter
    useEffect(() => {
        if (Object.keys(filters).length === 0) {
            setFilters({ host_vessel: false });
        }
    }, []);

    // TODO: review code for performance optimizations

    const fetchFilters = async () => {
        try {
            const filterItems = await axiosInstance
                .get("/artifacts/filters")
                .then((res) => res.data)
                .catch((err) => {
                    console.error(`Error fetching filters in Events`, err);
                });
            if (Object.keys(filterItems).length !== 0) {
                setFilterItem(filterItems);
            }
        } catch (err) {
            console.error(`Error fetching filters in Events`, err);
        }
    };


    const fetchArtifacts = async (isSocketTriggered = false, isLoadMore = false) => {
        if (!isSocketTriggered) {
            setIsLoading(true);
        }

        try {
            const currentPage = isLoadMore ? page + 1 : 1;
            const payload = {
                page: currentPage,
                pageSize: 100,
                filters,
                group: 1,
            };

            // If id is present, fetch single artifact
            if (id) {
                payload.filters = { ...payload.filters, id };
                payload.pageSize = 1;
            }

            const response = await axiosInstance.post("/artifacts", payload);
            const { artifacts, totalCount, groupedArtifacts } = response.data;
            if (artifacts.length === 0 && totalCount === 0) {
                setEvents([]);
                setPage(1);
                setIsLoading(false);
                return;
            }
            originalGroupsRef.current = [...originalGroupsRef.current, ...groupedArtifacts];
            const processedArtifacts = groupArtifacts(artifacts, groupedArtifacts).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            if (isLoadMore) {
                setEvents((prev) => [...prev, ...processedArtifacts]);
                setPage(currentPage);
            } else {
                setEvents(processedArtifacts);
                setPage(1);
            }

            hasMore.current = artifacts.length < (id ? 1 : totalCount);

            setIsLoading(false);
            if (id && artifacts.length > 0) {
                const artifact = artifacts[0];
                const vesselInfo = vessels.find((v) => v.unit_id === artifact.unit_id);
                setSelectedCard({
                    ...artifact,
                    vesselName: vesselInfo?.name || vesselInfo?.unit_id,
                });
                setShowDetailModal(true);
            }
        } catch (err) {
            console.error(`Error fetching artifacts in Events`, err);
            setIsLoading(false);
        }
    };

    const handleLoadMore = useCallback(() => {
        if (!isLoading && hasMore.current && !id) {
            // Clear any existing timeout
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }

            // Set a new timeout
            loadMoreTimeoutRef.current = setTimeout(() => {
                fetchArtifacts(false, true);
            }, 500);
        }
    }, [isLoading, hasMore.current, id, fetchArtifacts]);

    const fetchRegionGroups = async () => {
        if (regions) {
            setRegionGroups(regions);
        }
    };
    useEffect(() => {
        fetchRegionGroups();
    }, [regions]);

    useEffect(() => {
        fetchFilters();
        if (sessionStorage.getItem("eventPath")) {
            sessionStorage.removeItem("eventPath");
        }
    }, []);

    useEffect(() => {
        console.log("[filterArtifactsNearHomePorts] events", events);
        setFilteredEvents(events);
    }, [events]);

    useEffect(() => {
        fetchArtifacts();
    }, [filters, id]);

    useEffect(() => {
        if (tab !== "events") {
            setEvents((prev) => prev.slice(0, 100));
            setPage(1);
            setIsLoading(false);
            if (listRef.current) {
                listRef.current.scrollToTop();
            }
        }
    }, [tab]);
    useEffect(() => {
        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;
            setEvents((prev) => {
                if (updatedArtifact?.portal?.is_archived) {
                    return prev.map((event) => {
                        // Handle grouped artifacts
                        if (event.isGroup && event.groupArtifacts) {
                            const updatedGroupArtifacts = event.groupArtifacts.filter(
                                (artifact) => artifact._id !== updatedArtifact._id
                            );
                            // Remove the entire group if no artifacts remain
                            return updatedGroupArtifacts.length === 0 ? null : {
                                ...event,
                                groupArtifacts: updatedGroupArtifacts
                            };
                        }
                        // Handle artifacts with duplications
                        if (event.duplications) {
                            const updatedDuplications = event.duplications.filter(
                                (artifact) => artifact._id !== updatedArtifact._id
                            );
                            // If the main artifact is archived, remove the entire event
                            if (event._id === updatedArtifact._id) {
                                return null;
                            }
                            // If only duplications are archived, update the duplications array
                            return {
                                ...event,
                                duplications: updatedDuplications
                            };
                        }
                        // Handle individual artifacts
                        return event._id === updatedArtifact._id ? null : event;
                    }).filter((event) => event !== null && event !== undefined);
                } else {
                    // Handle unarchiving - add artifact back to the list
                    const artifactGroup = originalGroupsRef.current.find((group) => group.includes(updatedArtifact._id));
                    const sortEvents = (events) => events.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

                    // Check if artifact already exists in the list (shouldn't happen for unarchived artifacts)
                    const existingIndex = prev.findIndex((event) => event._id === updatedArtifact._id);
                    if (existingIndex !== -1) {
                        // Update existing artifact
                        const newEvents = [...prev];
                        newEvents[existingIndex] = { ...updatedArtifact, isGroup: false };
                        return sortEvents(newEvents);
                    }

                    if (!artifactGroup) {
                        return sortEvents([...prev, { ...updatedArtifact, isGroup: false }]);
                    }

                    // Find existing group with artifacts from same original group
                    const existingGroupIndex = prev.findIndex(
                        (event) => event.isGroup && event.groupArtifacts?.some((ga) => artifactGroup.includes(ga._id)),
                    );

                    if (existingGroupIndex !== -1) {
                        // Add to existing group
                        const newEvents = [...prev];
                        newEvents[existingGroupIndex] = {
                            ...newEvents[existingGroupIndex],
                            groupArtifacts: [...newEvents[existingGroupIndex].groupArtifacts, updatedArtifact],
                        };
                        return sortEvents(newEvents);
                    }

                    // Check for individual artifacts from same group
                    const individualArtifacts = prev.filter((event) => !event.isGroup && artifactGroup.includes(event._id));

                    if (individualArtifacts.length > 0) {
                        // Form group with individuals
                        const allGroupArtifacts = [...individualArtifacts, updatedArtifact];
                        const newEvents = prev.filter((event) => event.isGroup || !artifactGroup.includes(event._id));
                        newEvents.push({ ...allGroupArtifacts[0], isGroup: true, groupArtifacts: allGroupArtifacts });
                        return sortEvents(newEvents);
                    }

                    // Add as individual
                    return sortEvents([...prev, { ...updatedArtifact, isGroup: false }]);
                }
            });
        };

        socket.on("artifact/changed", handleArtifactChanged);

        return () => {
            socket.off("artifact/changed", handleArtifactChanged);
            // Clear timeout on unmount
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, []);

    const autoRefreshTimerRef = useRef();
    useEffect(() => {
        if (id) return;
        if (autoRefreshTimerRef.current) clearInterval(autoRefreshTimerRef.current);

        autoRefreshTimerRef.current = setInterval(
            () => {
                console.info("Auto-refreshing events (5-minute interval)");
                fetchArtifacts(true);
            },
            5 * 60 * 1000,
        );

        return () => {
            if (autoRefreshTimerRef.current) {
                clearInterval(autoRefreshTimerRef.current);
            }
        };
    }, [filters, fetchArtifacts, id]);

    useEffect(() => {
        if (typeof onCompletion.current !== "function") {
            onCompletion.current = (isLoading) => {
                setIsLoading(isLoading);
                setEvents([]);
            };
        }
    }, [onCompletion]);

    useEffect(() => {
        onLoadingChange(isLoading);
    }, [isLoading, onLoadingChange]);

    useEffect(() => {
        if (showDetailModal && selectedCard && selectedCard.artifact_id) {
            logEvent("EventViewed", { artifactId: selectedCard.artifact_id });
        }
    }, [showDetailModal, selectedCard]);

    useEffect(() => {
        if (showDetailModal && selectedCard && selectedCard.artifact_id) {
            logEvent("EventViewed", { artifactId: selectedCard.artifact_id });
        }
    }, [showDetailModal, selectedCard]);

    return (
        <>
            {isLoading && events.length === 0 ? (
                <Grid
                    container
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    height={{ xs: "90%", sm: "90%" }}
                    overflow={"auto"}
                    marginBottom={2}
                    size="grow"
                >
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                </Grid>
            ) : (
                <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
                    <Grid
                        container
                        overflow={"auto"}
                        display={"block"}
                        border={`1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"10px"}
                        padding={"10px 24px"}
                        size="grow"
                    >
                        <Grid container height={"100%"} overflow={"hidden"} ref={virtualizedListContainerRef}>
                            <VirtualizedCardList
                                ref={listRef}
                                events={filteredEvents}
                                setShowDetailModal={setShowDetailModal}
                                setSelectedCard={setSelectedCard}
                                isLoading={isLoading}
                                onLoadMore={handleLoadMore}
                                hasMore={hasMore.current && !id} // Disable load more in single view
                                containerRef={virtualizedListContainerRef}
                            />
                        </Grid>
                    </Grid>
                    <GroupedDetailModal
                        showDetailModal={showDetailModal}
                        setShowDetailModal={setShowDetailModal}
                        selectedCard={selectedCard}
                        setSelectedCard={setSelectedCard}
                        id={id}
                    />
                    <FilterEventModal
                        showFilterModal={showFilterModal}
                        vessels={vessels}
                        setFilters={setFilters}
                        setShowFilterModal={setShowFilterModal}
                        events={events}
                        setFilteredEvents={setFilteredEvents}
                        filterItems={filterItem}
                        filters={filters}
                        selectedTime={selectedTime}
                        setSelectedTime={setSelectedTime}
                        selectedType={selectedType}
                        setSelectedType={setSelectedType}
                        selectedArea={selectedArea}
                        setSelectedArea={setSelectedArea}
                        selectedVessel={selectedVessel}
                        setSelectedVessel={setSelectedVessel}
                        selectedCategory={selectedCategory}
                        setSelectedCategory={setSelectedCategory}
                        timeStart={timeStart}
                        setTimeStart={setTimeStart}
                        timeEnd={timeEnd}
                        setTimeEnd={setTimeEnd}
                        selectedSize={selectedSize}
                        setSelectedSize={setSelectedSize}
                        selectedColor={selectedColor}
                        setSelectedColor={setSelectedColor}
                        selectedWeapon={selectedWeapon}
                        setSelectedWeapon={setSelectedWeapon}
                        selectedHostVessel={selectedHostVessel}
                        setSelectedHostVessel={setSelectedHostVessel}
                        selectedAisFilter={selectedAisFilter}
                        setSelectedAisFilter={setSelectedAisFilter}
                        regionGroups={regionGroups}
                    />
                </Grid>
            )}
        </>
    );
};

export default memo(Events);
