import { createClient, RedisClientType } from "redis";

let redisClient: RedisClientType | null = null;

/**
 * Get or create a shared Redis client instance
 * Uses SHARED_REDIS_URL environment variable
 * @returns Promise<RedisClientType> - Connected Redis client
 */
export const getSharedRedisClient = async (): Promise<RedisClientType> => {
    if (!redisClient) {
        const redisUrl = process.env.SHARED_REDIS_URL;
        if (!redisUrl) {
            throw new Error("SHARED_REDIS_URL is not defined");
        }

        redisClient = createClient({ url: redisUrl });
        redisClient.on("error", (err) => console.error("Redis client error:", err));
        await redisClient.connect();
    } else if (!redisClient.isOpen) {
        // Reconnect if connection was lost
        await redisClient.connect();
    }
    return redisClient;
};

/**
 * Close the shared Redis client connection
 * Useful for graceful shutdown
 */
export const closeSharedRedisClient = async (): Promise<void> => {
    if (redisClient && redisClient.isOpen) {
        await redisClient.quit();
        redisClient = null;
    }
};
