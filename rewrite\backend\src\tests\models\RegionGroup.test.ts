import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

const mockNormalizeName = jest.fn((name: string) => name.toLowerCase());

describe('RegionGroup Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create RegionGroup model with proper schema and hooks', async () => {
        jest.doMock('../../utils/functions', () => ({ normalizeName: mockNormalizeName }));
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/RegionGroup')];

        const RegionGroupModule = await import('../../models/RegionGroup');
        const RegionGroup = RegionGroupModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('RegionGroup', expect.any(Object), 'regions_groups');
        expect(RegionGroup).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);
        expect(schemaArg.paths.name.unique).toBe(true);

        expect(schemaArg.paths.timezone).toBeDefined();
        expect(schemaArg.paths.timezone.type).toBe(String);
        expect(schemaArg.paths.timezone.required).toBe(true);

        expect(schemaArg.paths.created_by).toBeDefined();
        expect(schemaArg.paths.created_by.type).toBeDefined();
        expect(schemaArg.paths.created_by.required).toBe(true);

        expect(schemaArg.paths.creation_timestamp).toBeDefined();
        expect(schemaArg.paths.creation_timestamp.type).toBe(Date);
        expect(schemaArg.paths.creation_timestamp.required).toBe(true);
        expect(schemaArg.paths.creation_timestamp.default).toBeDefined();

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.pre).toHaveBeenCalledWith("save", expect.any(Function));
        expect(schemaArg.index).toHaveBeenCalledWith({ name: 1 }, { unique: true, collation: { locale: "en", strength: 2 } });

        const preSaveHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preSaveHookCall) {
            const preSaveHookFn = preSaveHookCall[1];
            const mockDoc = { name: 'Test Region Group' };
            const mockNext = jest.fn();
            preSaveHookFn.call(mockDoc, mockNext);
            expect(mockNormalizeName).toHaveBeenCalledWith('Test Region Group');
            expect(mockNext).toHaveBeenCalled();
        }
    });
});
