import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import { addFavouriteArtifact, deleteFavouriteArtifact, getUserFavouriteArtifacts, isVesselProvisioned } from '../../services/ArtifactFavourites.service';
import ArtifactFavourite from '../../models/ArtifactFavourites';
import Vessel from '../../models/Vessel';
import { qmai } from '../mocks/modules/db.mock';
import { permissions } from '../../utils/permissions';
import { canAccessVessel, validateError } from '../../utils/functions';

jest.mock('../../models/ArtifactFavourites', () => require('../mocks/models/artifactFavourites.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));

function mockRes() {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    return res;
}

describe('ArtifactFavourites.service', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('isVesselProvisioned throws when user is missing', async () => {
        await expect(isVesselProvisioned({ user: undefined, api_key: 'A' }, 'V')).rejects.toThrow('User is required');
    });

    it('isVesselProvisioned throws when user permissions are missing', async () => {
        await expect(isVesselProvisioned({ user: { _id: 'U' }, api_key: 'A' }, 'V')).rejects.toThrow('User permissions are required');
    });

    it('isVesselProvisioned throws when vessel_id is missing', async () => {
        await expect(isVesselProvisioned({ user: { _id: 'U', permissions: [] }, api_key: 'A' }, undefined as any)).rejects.toThrow('Vessel ID is required');
    });

    it('addFavouriteArtifact returns 400 when missing fields', async () => {
        const req: any = { user: {}, body: {} };
        const res: any = mockRes();
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(400);
    });

    it('addFavouriteArtifact returns 404 when artifact not found', async () => {
        const req: any = { user: { _id: 'U' }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(404);
    });

    it('addFavouriteArtifact returns 403 when vessel not provisioned (vessel missing)', async () => {
        const req: any = { user: { _id: 'U', permissions: [] }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A', onboard_vessel_id: { toString: () => 'V' } } as never) });
        (Vessel.findOne as jest.Mock).mockResolvedValue(null as never);
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(403);
    });

    it('addFavouriteArtifact returns 403 when vessel not provisioned (no access)', async () => {
        const req: any = { user: { _id: 'U', permissions: [] }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A', onboard_vessel_id: { toString: () => 'V' } } as never) });
        (Vessel.findOne as jest.Mock).mockResolvedValue({ _id: 'V', is_active: true } as never);
        (canAccessVessel as jest.Mock).mockReturnValueOnce(false as never);
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(403);
    });

    it('addFavouriteArtifact returns 400 when already exists', async () => {
        const req: any = { user: { _id: 'U', permissions: [{ permission_id: permissions.accessAllVessels }] }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A', onboard_vessel_id: { toString: () => 'V' } } as never) });
        (ArtifactFavourite.findOne as any).mockResolvedValue({ _id: 'F' });
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(400);
    });

    it('addFavouriteArtifact returns 201 on success', async () => {
        const req: any = { user: { _id: 'U', permissions: [{ permission_id: permissions.accessAllVessels }] }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A', onboard_vessel_id: { toString: () => 'V' } } as never) });
        (ArtifactFavourite.findOne as any).mockResolvedValue(null);
        const save = jest.fn().mockResolvedValue(true as never);
        (ArtifactFavourite as any).prototype.save = save;
        await addFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(201);
        expect(save).toHaveBeenCalled();
    });

    it('deleteFavouriteArtifact returns 400 when missing fields', async () => {
        const req: any = { user: {}, body: {} };
        const res: any = mockRes();
        await deleteFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(400);
    });

    it('deleteFavouriteArtifact returns 404 when not found', async () => {
        const req: any = { user: { _id: 'U' }, body: { artifact_id: 'A' } };
        const res: any = mockRes();
        (ArtifactFavourite.findOneAndDelete as any).mockResolvedValue(null);
        await deleteFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(404);
    });

    it('deleteFavouriteArtifact returns 200 on success', async () => {
        const req: any = { user: { _id: 'U' }, body: { artifact_id: 'A' } };
        const res: any = mockRes();
        (ArtifactFavourite.findOneAndDelete as any).mockResolvedValue({ _id: 'X' });
        await deleteFavouriteArtifact(req, res);
        expect(res.status).toHaveBeenCalledWith(200);
    });

    it('addFavouriteArtifact handles unexpected error via validateError', async () => {
        const req: any = { user: { _id: 'U' }, body: { artifact_id: 'a'.repeat(24) } };
        const res: any = mockRes();
        (qmai.collection as jest.Mock).mockImplementation(() => { throw new Error('boom'); });
        await addFavouriteArtifact(req, res);
        expect((validateError as jest.Mock)).toHaveBeenCalled();
    });

    it('deleteFavouriteArtifact handles unexpected error via validateError', async () => {
        const req: any = { user: { _id: 'U' }, body: { artifact_id: 'A' } };
        const res: any = mockRes();
        (ArtifactFavourite.findOneAndDelete as any).mockImplementation(() => { throw new Error('boom'); });
        await deleteFavouriteArtifact(req, res);
        expect((validateError as jest.Mock)).toHaveBeenCalled();
    });

    it('getUserFavouriteArtifacts handles unexpected error via validateError', async () => {
        const req: any = { user: { _id: 'U', permissions: [{ permission_id: permissions.accessAllVessels }] } };
        const res: any = mockRes();
        (ArtifactFavourite.find as any).mockReturnValue({ select: jest.fn().mockReturnValue({ sort: jest.fn().mockResolvedValue([
            { artifact_id: { toString: () => 'A1' }, createdAt: 1 },
        ] as never) }) });
        (qmai.collection as any).mockImplementation(() => { throw new Error('boom'); });
        await getUserFavouriteArtifacts(req, res);
        expect((validateError as jest.Mock)).toHaveBeenCalled();
    });

    it('getUserFavouriteArtifacts returns 400 when user missing', async () => {
        const req: any = { user: {} };
        const res: any = mockRes();
        await getUserFavouriteArtifacts(req, res);
        expect(res.status).toHaveBeenCalledWith(400);
    });

    it('getUserFavouriteArtifacts builds query with accessAllVessels and maintains order', async () => {
        const req: any = { user: { _id: 'U', permissions: [{ permission_id: permissions.accessAllVessels }] } };
        const res: any = mockRes();
        (ArtifactFavourite.find as any).mockReturnValue({
            select: jest.fn().mockReturnValue({
                sort: jest.fn().mockResolvedValue([
                    { artifact_id: { toString: () => 'A1' }, createdAt: 2 },
                    { artifact_id: { toString: () => 'A2' }, createdAt: 1 },
                ] as never)
            })
        });
        (qmai.collection as any).mockReturnValue({
            find: jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([
                    { _id: { toString: () => 'A1' }, portal: {}, value: 1 },
                    { _id: { toString: () => 'A2' }, portal: {}, value: 2 },
                ] as never)
            })
        });
        await getUserFavouriteArtifacts(req, res);
        expect(res.status).toHaveBeenCalledWith(200);
        const payload = res.send.mock.calls[0][0];
        expect(payload.artifacts.map((a: any) => a.value)).toEqual([1, 2]);
    });

    it('getUserFavouriteArtifacts restricts by allowed vessels when no accessAllVessels', async () => {
        const req: any = { user: { _id: 'U', permissions: [], allowed_vessels: ['V1'] } };
        const res: any = mockRes();
        (ArtifactFavourite.find as any).mockReturnValue({
            select: jest.fn().mockReturnValue({
                sort: jest.fn().mockResolvedValue([
                    { artifact_id: { toString: () => 'A1' }, createdAt: 1 },
                ] as never)
            })
        });
        (Vessel.find as any).mockResolvedValue([{ _id: 'V1' }]);
        (qmai.collection as any).mockReturnValue({
            find: jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([
                    { _id: { toString: () => 'A1' }, portal: {}, value: 1, onboard_vessel_id: 'V1' },
                ] as never)
            })
        });
        await getUserFavouriteArtifacts(req, res);
        expect(res.status).toHaveBeenCalledWith(200);
    });
});


