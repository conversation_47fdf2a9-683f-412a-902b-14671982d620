import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('SessionLog Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create SessionLog model with proper schema and hooks', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/SessionLog')];

        const SessionLogModule = await import('../../models/SessionLog');
        const SessionLog = SessionLogModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('SessionLog', expect.any(Object), 'logs_sessions');
        expect(SessionLog).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const connectTimestamp = schemaArg.paths.connect_timestamp.default();
        expect(typeof connectTimestamp).toBe('string');
        expect(connectTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const environment = schemaArg.paths.environment.default();
        expect(typeof environment).toBe('string');

        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockLog = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', socket_id: 'socket-123' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockLog);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockLog);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});