import globals from "globals";
import tseslint from "typescript-eslint";
import prettier from "eslint-plugin-prettier";
import prettierConfig from "eslint-config-prettier";
import unusedImports from "eslint-plugin-unused-imports";

/** @type {import('eslint').Linter.Config[]} */
export default [
    {
        files: ["**/*.ts", "**/*.tsx"],
        languageOptions: {
            parser: tseslint.parser,
            parserOptions: {},
            globals: {
                ...globals.node,
                ...globals.browser,
            },
        },
        plugins: {
            "@typescript-eslint": tseslint.plugin,
            prettier,
            "unused-imports": unusedImports,
        },
        rules: {
            ...tseslint.configs.recommended.rules,
            "@typescript-eslint/no-unused-vars": [
                "error",
                {
                    args: "all",
                    argsIgnorePattern: "^_",
                    varsIgnorePattern: "^_",
                    caughtErrorsIgnorePattern: "^_",
                },
            ],
            "no-unused-vars": "off",
            "unused-imports/no-unused-imports": "error",
            "prettier/prettier": [
                "error",
                {
                    endOfLine: "auto",
                    semi: true,
                    trailingComma: "all",
                    arrowParens: "always",
                    printWidth: 150,
                    tabWidth: 4,
                },
            ],
        },
        settings: {
            prettier: prettierConfig,
        },
    },
    {
        ignores: ["node_modules/", ".github/", "coverage/", "*.config.js"],
    },
];
