import axios from "axios";

const ENVIRONMENT = process.env.NODE_ENV;
const SLACK_WEBHOOK_URL = process.env.SLACK_LOGS_WEBHOOK_URL;

if (!SLACK_WEBHOOK_URL) throw new Error("[notifyLog.js] SLACK_WEBHOOK_URL is missing in the .env");
if (!ENVIRONMENT) throw new Error("[notifyLog.js] ENVIRONMENT is missing in the .env");

type TSeverity = "fatal" | "error" | "warning" | "info";

const slackTemplate = ({ severity, message, stack }: { severity: TSeverity; message: string; stack: string }) => ({
    blocks: [
        {
            type: "header",
            text: {
                type: "plain_text",
                text: `${severity === "error" ? "Error detected" : severity === "fatal" ? "FATAL error detected" : severity === "warning" ? "Warning message" : "Info message"} in backend`,
                emoji: true,
            },
        },
        {
            type: "divider",
        },
        {
            type: "section",
            fields: [
                {
                    type: "mrkdwn",
                    text: `*Environment:*\n${ENVIRONMENT}`,
                },
                {
                    type: "mrkdwn",
                    text: `*Severity:*\n${severity === "fatal" ? ":rotating_light:" : severity === "error" ? ":exclamation:" : severity === "warning" ? ":warning:" : ":information_source:"} *${severity.toUpperCase()}*`,
                },
                {
                    type: "mrkdwn",
                    text: `*Timestamp (UTC):*\n${new Date().toISOString()}`,
                },
            ],
        },
        {
            type: "section",
            text: {
                type: "mrkdwn",
                text: `*Message:*\n${message}`,
            },
        },
        {
            type: "divider",
        },
        {
            type: "context",
            elements: [
                {
                    type: "mrkdwn",
                    text: `*Stack trace:*\n${stack}`,
                },
            ],
        },
    ],
});

export const postLogToSlack = async ({ severity, message, stack }: { severity: TSeverity; message: string; stack?: string }) => {
    if (!["portal", "staging", "dev"].includes(ENVIRONMENT)) return console.warn("[postLogToSlack] Not in cloud environment, skipping log to Slack");
    if (!severity || !message || !stack) console.warn("[postLogToSlack] severity, message, stack are required");

    const body = slackTemplate({
        severity: severity || "unknown",
        message: message || "Unknown",
        stack: stack || "Unknown",
    });
    await axios.post(SLACK_WEBHOOK_URL, body);
    console.log("[postLogToSlack] Log sent to Slack");

    return;
};
