import request from 'supertest';
import app from '../../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthorizedAuthMocks } from '../../mocks/auth.mock';
import awsKinesis from '../../../modules/awsKinesis';
import vesselService from '../../../services/Vessel.service';
import streamService from '../../../services/Stream.service';
import { canAccessVessel, validateError } from '../../../utils/functions';
import User from '../../../models/User';
import ApiKey from '../../../models/ApiKey';

jest.mock('../../../modules/db', () => require('../../mocks/modules/db.mock'));
jest.mock('../../../modules/awsKinesis', () => ({
    __esModule: true,
    default: {
        getDashStreamingSessionURL_V2: jest.fn(),
    },
    getDashStreamingSessionURL_V2: jest.fn(),
}));
jest.mock('../../../services/Stream.service', () => require('../../mocks/services/streamService.mock'));
jest.mock('../../../services/Vessel.service', () => require('../../mocks/services/vesselService.mock'));
jest.mock('../../../utils/functions', () => require('../../mocks/utils/functions.mock'));
jest.mock('../../../models/User', () => require('../../mocks/models/user.mock'));
jest.mock('../../../models/ApiKey', () => require('../../mocks/models/apiKey.mock'));

describe('Kinesis V2 API', () => {
    describe('GET /api/v2/kinesis/dashStreamingSessionURL', () => {
        const runTests = (authMethod: 'user' | 'api-key', generateToken: (id: string) => string, userOrApiKey: any) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (streamService.fetchSingle as jest.Mock).mockResolvedValue({ unit_id: 'QSX0001' } as never);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439012' } as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (validateError as jest.Mock).mockImplementation((_err: any, res: any) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                    (awsKinesis.getDashStreamingSessionURL_V2 as jest.Mock).mockResolvedValue('http://example.com/stream' as never);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.authorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/v2/kinesis/dashStreamingSessionURL');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if required params missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.authorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if stream does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (streamService.fetchSingle as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 403 if vessel not accessible', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect([403, 500]).toContain(res.status);
                });

                it('should return 200 and session url', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('data');
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (awsKinesis.getDashStreamingSessionURL_V2 as jest.Mock).mockImplementation(() => { throw new Error('kinesis'); });
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });

                it('should return 400 if streamMode invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=BAD')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if startTimestamp is not ISO8601', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE&startTimestamp=notadate')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if totalDuration is not numeric', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE&totalDuration=abc')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 403 if stream resolved but vessel missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL?streamName=S&region=r&streamMode=LIVE')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if streamName is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../../middlewares/validator').validateData;
                    require('../../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/v2/kinesis/dashStreamingSessionURL')
                        .set('Authorization', authToken)
                        .send({ streamName: null });
                    expect(res.status).toBe(400);

                    require('../../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        const { generateUserToken, authorizedUser, nonAuthorizedUser } = require('../../data/Auth');
        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


