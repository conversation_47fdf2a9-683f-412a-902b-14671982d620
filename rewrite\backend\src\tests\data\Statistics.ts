
export const statisticsList = [
    {
        _id: "670d5c2cbc6f7a464e65ba7f",
        stats: {
            totalVesselsDetected: 19,
            totalVesselsSuperCategorized: {
                PleasureCraft: 3,
                Fishing: 3,
                Military: 1
            },
            totalVesselsSubCategorized: {
                Banca: 4,
                SmallBoat: 2,
                PatrolBoat: 1
            },
            totalSmartmastsAtSea: 0,
            totalSmartmastsOnline: 3,
            totalVesselsDetectedbySensors: {
                "683df46c073245cf0fd62bc2": 19
            }
        },
        fromTimestamp: "2024-10-13T21:00:00.000Z",
        toTimestamp: "2024-10-14T12:00:00.000Z",
        type: "daily",
    },
    {
        _id: '507f1f77bcf86cd799439012',
        stats: {
            totalVesselsDetected: 5,
            totalSmartmastsOnline: 45,
            activeUsers: 25,
            dataProcessed: 8000
        },
        fromTimestamp: '2024-10-01T00:00:00.000Z',
        toTimestamp: '2024-10-01T23:59:59.999Z',
        type: 'daily',
        createdAt: '2025-09-25T18:52:48.014Z',
        updatedAt: '2025-09-25T18:52:48.014Z',
    },
    {
        _id: "671a8deafdc3cb151a2f5607",
        stats: {
            totalArtifactsWithAtleastOneVessel: {
                "confidenceAbove40": 481,
                "confidenceAbove80": 481
            },
            totalVesselsSuperCategorized: {
                "Pleasure Craft": 104,
                "Fishing": 331,
                "Military": 14,
                "Special Craft": 10,
                "Cargo": 2,
                "Tugs": 1
            },
            totalVesselsSubCategorized: {
                "Banca": 191,
                "Small Boat": 36,
                "Patrol Boat": 10,
                "Rowboat": 2,
                "Outrigger Canoe": 15,
                "Fishing Boat": 20,
                "Rigid Inflatable Boat": 1,
                "Speedboat": 2,
                "Fishing Vessel": 30,
                "Sinking Vessel": 1,
                "Sailing Boat": 5,
                "Gondola": 1,
                "Rigid Inflatable Boat (RIB)": 1,
                "Dhow": 3,
                "Canoe": 11,
                "Traditional Boat": 8,
                "Bangka": 35,
                "Coast Guard Vessel": 6,
                "Banca Boat": 5,
                "Drone": 1,
                "Sailing Vessel": 3,
                "Small Fishing Boat": 6,
                "Traditional Fishing Boat": 2,
                "Banka": 8,
                "Buoy": 1,
                "Trimaran": 1,
                "Unmanned Surface Vehicle": 1,
                "Trawler": 1,
                "Dinghy": 2,
                "Sailing Yacht": 3,
                "Sailboat": 6,
                "Motorboat": 2,
                "Container Ship": 1,
                "Boat": 1,
                "Traditional Canoe": 1,
                "Longtail boat": 1,
                "Naval Ship": 1,
                "Patrol Vessel": 1,
                "Tugboat": 1
            },
            listOfTextsExtracted: [
                "ALENA",
                "COAST GUARD",
                "NAVY",
                "PHILIPPINE COAST GUARD",
                "SEA-DOO",
                "MALIGAYA",
                "COCONUT",
                "JOCALYN",
                "Banca",
                "JESSICA",
                "JHEN",
                "BIRHEN NG LOURDES",
                "JUKUNG BALI",
                "JHON WIL",
                "TIKMA",
                "PAGASA",
                "CAPTAIN KEN",
                "CRIS 3",
                "TAURUS",
                "JONATHAN",
                "JUNIOR",
                "BELLE",
                "JHANN",
                "JHON MARK",
                "TALISAYAN",
                "JHONELYN",
                "M/BCA",
                "TUGOS",
                "JHOSIE",
                "IMMACULATE",
                "TRES MARIAS",
                "TANCHOY",
                "TANJAY",
                "TAMON",
                "TAMARAW",
                "TANAY & AMALIA",
                "MARIA",
                "JHONELLE",
                "KATHLEEN",
                "PC-004",
                "SANTINO",
                "MANG",
                "MAYETTE",
                "SABANG",
                "JRC",
                "BANCAS",
                "JHEN CARL",
                "JAMES",
                "GUMASA",
                "GABRIELLE",
                "JHON PAUL",
                "MARITES",
                "RUTH",
                "JHON",
                "PORT",
                "NOVA",
                "JHUN MARC",
                "JUNNELYN",
                "Bangka",
                "JESUS",
                "INAH 5",
                "TOMAS",
                "C",
                "TANCA",
                "MAERSK",
                "HAMBURG",
                "NEW",
                "GUARD",
                "4403",
                "4401",
                "4406",
                "4402",
                "Coast Guard"
            ],
            totalVesselsWithCountryFlag: {
                "Unknown": 1,
                "Red and yellow": 1,
                "Philippines": 2,
                "United States": 1
            },
            totalVesselsByHoursUTC: {
                "0": 31,
                "1": 18,
                "2": 14,
                "3": 14,
                "4": 25,
                "5": 39,
                "6": 37,
                "7": 112,
                "8": 86,
                "9": 65,
                "10": 15,
                "11": 1,
                "12": 0,
                "13": 0,
                "14": 0,
                "15": 0,
                "16": 0,
                "17": 0,
                "18": 0,
                "19": 0,
                "20": 0,
                "21": 1,
                "22": 11,
                "23": 15
            },
            totalVesselsByWeekDayHoursUTC: {
                "2024-10-13T22:00:00.000Z": 2,
                "2024-10-13T23:00:00.000Z": 3,
                "2024-10-14T00:00:00.000Z": 1,
                "2024-10-14T02:00:00.000Z": 1,
                "2024-10-14T04:00:00.000Z": 1,
                "2024-10-14T05:00:00.000Z": 6,
                "2024-10-14T06:00:00.000Z": 3,
                "2024-10-14T07:00:00.000Z": 1,
                "2024-10-14T11:00:00.000Z": 1,
                "2024-10-15T00:00:00.000Z": 2,
                "2024-10-15T01:00:00.000Z": 1,
                "2024-10-15T02:00:00.000Z": 5,
                "2024-10-15T03:00:00.000Z": 3,
                "2024-10-15T04:00:00.000Z": 4,
                "2024-10-15T05:00:00.000Z": 4,
                "2024-10-15T06:00:00.000Z": 1,
                "2024-10-15T07:00:00.000Z": 6,
                "2024-10-15T08:00:00.000Z": 4,
                "2024-10-15T09:00:00.000Z": 4,
                "2024-10-15T22:00:00.000Z": 2,
                "2024-10-15T23:00:00.000Z": 5,
                "2024-10-16T00:00:00.000Z": 1,
                "2024-10-16T01:00:00.000Z": 9,
                "2024-10-16T02:00:00.000Z": 4,
                "2024-10-16T03:00:00.000Z": 10,
                "2024-10-16T04:00:00.000Z": 16,
                "2024-10-16T05:00:00.000Z": 28,
                "2024-10-16T06:00:00.000Z": 20,
                "2024-10-16T07:00:00.000Z": 103,
                "2024-10-16T08:00:00.000Z": 81,
                "2024-10-16T09:00:00.000Z": 59,
                "2024-10-16T10:00:00.000Z": 15,
                "2024-10-16T23:00:00.000Z": 3,
                "2024-10-17T00:00:00.000Z": 1,
                "2024-10-17T01:00:00.000Z": 3,
                "2024-10-17T02:00:00.000Z": 1,
                "2024-10-17T04:00:00.000Z": 3,
                "2024-10-17T05:00:00.000Z": 1,
                "2024-10-17T06:00:00.000Z": 1,
                "2024-10-17T07:00:00.000Z": 2,
                "2024-10-17T08:00:00.000Z": 1,
                "2024-10-17T22:00:00.000Z": 1,
                "2024-10-17T23:00:00.000Z": 4,
                "2024-10-18T00:00:00.000Z": 13,
                "2024-10-18T01:00:00.000Z": 2,
                "2024-10-18T03:00:00.000Z": 1,
                "2024-10-18T04:00:00.000Z": 1,
                "2024-10-18T06:00:00.000Z": 12,
                "2024-10-18T09:00:00.000Z": 1,
                "2024-10-19T00:00:00.000Z": 13,
                "2024-10-19T01:00:00.000Z": 3,
                "2024-10-19T02:00:00.000Z": 3,
                "2024-10-19T09:00:00.000Z": 1,
                "2024-10-19T21:00:00.000Z": 1,
                "2024-10-19T22:00:00.000Z": 6
            },
            experimental: {
                totalVesselsSuperCategorizedWithBoundingBoxOccupancy5: {
                    "Fishing": 104,
                    "Military": 5,
                    "Special Craft": 5,
                    "Pleasure Craft": 18
                }
            },
            totalVesselsDetectedbySensors: {
                "683df46c073245cf0fd62bc2": 484
            },
            totalSensorsDurationAtSea: {
                "683df46c073245cf0fd62bbf": 2267121,
                "68402564e9b65fa69e0c042b": 0,
                "683df46c073245cf0fd62bc2": 37399919,
                "683df46b073245cf0fd62bbc": 0
            },
            totalSensorsOnlineDuration: {
                "683df46c073245cf0fd62bbf": *********,
                "68402564e9b65fa69e0c042b": 0,
                "683df46c073245cf0fd62bc2": *********,
                "683df46b073245cf0fd62bbc": *********
            },
            totalSmartmastsDistanceTraveled: {
                "683df46c073245cf0fd62bbf": 160976.50425414086,
                "68402564e9b65fa69e0c042b": 0,
                "683df46c073245cf0fd62bc2": 199477.17996913017,
                "683df46b073245cf0fd62bbc": 1235.9800926787816
            }
        },
        fromTimestamp: "2024-10-13T16:00:00.000Z",
        toTimestamp: "2024-10-20T15:59:59.000Z",
        type: "weekly",
    },
];