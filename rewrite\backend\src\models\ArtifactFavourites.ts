import mongoose from "mongoose";
import db from "../modules/db";
import User from "./User";
import ioEmitter from "../modules/ioEmitter";
import { IArtifactFavourite } from "src/interfaces/ArtifactFavourite";

const schema = mongoose.Schema;
const ArtifactFavouritesSchema = new schema(
    {
        user_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: User,
            required: true,
        },
        artifact_id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: true,
    },
);

ArtifactFavouritesSchema.post("save", (favourite) => {
    if (!favourite) return;
    ioEmitter.emit("notifyAll", { name: `favourites/changed`, data: favourite.toObject() });
});
ArtifactFavouritesSchema.post("findOneAndDelete", (favourite) => {
    if (!favourite) return;
    ioEmitter.emit("notifyAll", { name: `favourites/changed`, data: favourite.toObject() });
});

const ArtifactFavourites = db.qm.model<IArtifactFavourite>("ArtifactFavourites", ArtifactFavouritesSchema, "artifact_favourites");

export default ArtifactFavourites;
