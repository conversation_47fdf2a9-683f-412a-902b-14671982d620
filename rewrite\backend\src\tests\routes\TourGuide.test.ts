import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import TourGuide from '../../models/TourGuide';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/TourGuide', () => require('../mocks/models/tourGuide.mock'));

describe('TourGuide API', () => {
    describe('GET /api/tourGuides', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/tourGuides');
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).get('/api/tourGuides').set('Authorization', nonAuthToken);
                    expect([401, 403, 200]).toContain(res.status);
                });

                it('should return 200 and list tour guides for authorized user; 500 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (TourGuide.find as jest.Mock).mockResolvedValueOnce([{ _id: '1', user_id: '1', maps: true, streams: false, events: true, notifications: false }] as never);
                    const res = await request(app).get('/api/tourGuides').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(Array.isArray(res.body)).toBe(true);
                        expect(res.body[0]).toHaveProperty('_id');
                        expect(res.body[0]).toHaveProperty('user_id');
                        expect(res.body[0]).toHaveProperty('maps');
                        expect(res.body[0]).toHaveProperty('streams');
                        expect(res.body[0]).toHaveProperty('events');
                        expect(res.body[0]).toHaveProperty('notifications');
                    } else {
                        expect(res.status).toBe(500);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (TourGuide.find as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).get('/api/tourGuides').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/tourGuides', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/tourGuides').send({ maps: true });
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', nonAuthToken).send({ maps: true });
                    expect([401, 403, 201]).toContain(res.status);
                });

                it('should return 400 if no fields are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 if maps is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ maps: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streams is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ streams: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if events is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ events: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if notifications is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ notifications: 'invalid' });
                    expect(res.status).toBe(400);
                });

                if (authMethod === 'user') {
                    it('should return 409 if tour guide already exists', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (TourGuide.findOne as jest.Mock).mockResolvedValueOnce({ _id: '1', user_id: '1', maps: true, streams: false, events: true, notifications: false } as never);
                        const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ maps: true });
                        expect(res.status).toBe(409);
                        expect(res.body).toHaveProperty('message');
                    });

                    it('should return 201 and create tour guide successfully', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (TourGuide.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                        (TourGuide.create as jest.Mock).mockResolvedValueOnce({ _id: '1', user_id: '1', maps: true, streams: false, events: true, notifications: false } as never);
                        const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ maps: true, streams: false, events: true, notifications: false });
                        expect(res.status).toBe(201);
                        expect(res.body).toHaveProperty('message');
                    });

                    // it('should return 500 on first try-catch error', async () => {
                    //     setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    //     (TourGuide.findOne as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                    //     const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ maps: true });
                    //     expect(res.status).toBe(500);
                    // });

                    it('should return 500 on second try-catch error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (TourGuide.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                        (TourGuide.create as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).post('/api/tourGuides').set('Authorization', authToken).send({ maps: true });
                        expect(res.status).toBe(500);
                    });
                }

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/tourGuides', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/tourGuides').send({ maps: true });
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', nonAuthToken).send({ maps: true });
                    expect([401, 403, 200]).toContain(res.status);
                });

                it('should return 400 if no fields are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 if maps is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ maps: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if streams is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ streams: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if events is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ events: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if notifications is not boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ notifications: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and update tour guide for authorized user; 500 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (TourGuide.findOneAndUpdate as jest.Mock).mockResolvedValueOnce({ _id: '1', user_id: '1', maps: true, streams: false, events: true, notifications: false } as never);
                    const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ maps: true, streams: false, events: true, notifications: false });
                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(res.body).toHaveProperty('message');
                        expect(res.body).toHaveProperty('tourGuide');
                    } else {
                        expect(res.status).toBe(500);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (TourGuide.findOneAndUpdate as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).patch('/api/tourGuides').set('Authorization', authToken).send({ maps: true });
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
