declare module "swagger-ui-express" {
    import { RequestHandler } from "express";

    export const serve: RequestHandler[];
    export function setup(
        swaggerDoc: object,
        options?: object,
        customCss?: string,
        customfavIcon?: string,
        swaggerUrl?: string,
        customJs?: string,
        explorer?: boolean,
    ): RequestHandler;

    const _default: {
        serve: typeof serve;
        setup: typeof setup;
    };

    export default _default;
}
