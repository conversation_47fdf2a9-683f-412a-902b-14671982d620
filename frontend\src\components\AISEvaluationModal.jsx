import { Box, Button, FormControl, FormControlLabel, Grid, Modal, Radio, RadioGroup, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import ModalContainer from "./ModalContainer";
import artifactController from "../controllers/Aritfact.controller";


const AISEvaluationModal = ({ initialState, onClose, flagAISArtifactId, isAisFlagged, setIsAisFlagged }) => {
    // console.log("AIS data open modal", isAisFlagged)
    const [selected, setSelected] = useState(isAisFlagged)
    const [isLoading, setIsLoading] = useState(false);

    // Update selected state when modal opens or isAisFlagged changes
    useEffect(() => {
        if (initialState && isAisFlagged) {
            setSelected(isAisFlagged);
        }
        else if (initialState && !isAisFlagged) {
            setSelected(null);
        }
    }, [initialState, isAisFlagged]);

    const handleSubmit = async (e) => {
        e.stopPropagation();
        setIsLoading(true);
        if (!selected || !flagAISArtifactId) return
        try {
            const response = await artifactController.setAisDiscrepancy(flagAISArtifactId, selected);
            // console.log("AIS response -> ", response)
            // Update the flag state based on the response
            setIsAisFlagged(response.state);
            // Close the modal after successful submission
            onClose();
        } catch (error) {
            console.error("Error updating AIS discrepancy:", error);
        } finally {
            setIsLoading(false);
        }
    };


    const aisEvaluationOptions = [
        { label: "Both Smartmast detection and AIS match the vessel in the artifact", value: "both_matched" },
        { label: "AIS matches the vessel in the artifact, but smartmast detection is incorrect", value: "detection_not_matched" },
        { label: "Smartmast detection matches the vessel in the artifact, but AIS is incorrect", value: "ais_not_matched" },
        { label: "Neither Smartmast nor AIS detection match the vessel in the artifact", value: "both_not_matched" },
    ]

    return (
        <Modal open={initialState} onClose={onClose}>
            <ModalContainer title="AIS Evaluation" showDivider>
                <Grid >
                    <Typography>
                        Please select one option.
                    </Typography>
                    <FormControl>
                        <RadioGroup
                            value={selected}
                            onChange={(e) => setSelected(e.target.value)}
                            sx={{ gap: 1 }}
                        >
                            {
                                aisEvaluationOptions.map((option) => (
                                    <FormControlLabel
                                        value={option.value}
                                        control={
                                             <Radio
                                                 size="small"
                                                 sx={{
                                                     color: 'white',
                                                     '&.Mui-checked': {
                                                         color: '#0066FF',
                                                     },
                                                     '& .MuiSvgIcon-root': {
                                                         backgroundColor: 'white',
                                                         borderRadius: '50%',
                                                         border: '2px solid white',
                                                         fontSize: '1.2rem',
                                                     }
                                                 }}
                                             />
                                        }
                                        label={option.label}
                                        sx={{ marginBottom: 1 }}
                                    />
                                ))
                            }
                        </RadioGroup>
                    </FormControl>
                    <Box display="flex" justifyContent="center" gap={1}>
                        <Button variant="outlined" onClick={onClose} disabled={isLoading}>Cancel</Button>
                        <Button variant="contained" onClick={handleSubmit} disabled={isLoading || !selected}>
                            {isLoading ? "Submitting..." : "Submit"}
                        </Button>
                    </Box>
                </Grid>
            </ModalContainer>
        </Modal>

    );
};

export default AISEvaluationModal;