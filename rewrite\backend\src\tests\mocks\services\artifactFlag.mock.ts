import { jest } from '@jest/globals';

export const flagArtifact = jest.fn();
export const unflagArtifact = jest.fn();
export const getFlaggedArtifacts = jest.fn();
export const getUserFlaggedArtifactIds = jest.fn();
export const removeAllFlagsFromArtifact = jest.fn();

export default {
    flagArtifact,
    unflagArtifact,
    getFlaggedArtifacts,
    getUserFlaggedArtifactIds,
    removeAllFlagsFromArtifact,
};


