import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ArtifactSuggestion Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ArtifactSuggestion model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactSuggestion')];

        const ArtifactSuggestionModule = await import('../../models/ArtifactSuggestion');
        const ArtifactSuggestion = ArtifactSuggestionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ArtifactSuggestion', expect.any(Object), 'artifact_suggestions');
        expect(ArtifactSuggestion).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.search).toBeDefined();
        expect(schemaArg.paths.search.type).toBe(String);
        expect(schemaArg.paths.search.required).toBe(true);

        expect(schemaArg.paths.click).toBeDefined();
        expect(schemaArg.paths.click.type).toBe(Number);
        expect(schemaArg.paths.click.default).toBe(0);

        expect(schemaArg.paths.impressions).toBeDefined();
        expect(schemaArg.paths.impressions.type).toBe(Number);
        expect(schemaArg.paths.impressions.default).toBe(0);

        expect(schemaArg.index).toHaveBeenCalledWith({ search: 1 }, { unique: true });
    });
});
