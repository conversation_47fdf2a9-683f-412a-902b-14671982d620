import { Typography, Card, CardContent, useTheme, Grid, Tooltip } from "@mui/material";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import { useApp } from "../../../hooks/AppHook";
import { getTimeAgo } from "../../../utils";

const SensorCard = ({ stream, view, selectedStream, onSelect, artifactIndicator = {}, viewedArtifacts = [], vesselInfo = [] }) => {
    const { devMode, showIDs } = useApp();
    const theme = useTheme();

    const artifactData = (artifactIndicator[stream.VesselId] || []).filter((a) => !viewedArtifacts.includes(a._id));
    const artifactLength = artifactData.length;

    const vessel = vesselInfo.find((v) => v.vessel_id === stream.VesselId);

    const getLastOnlineDisplay = () => {
        if (stream?.IsLive) return null;
        if (!vessel?.last_online_at) {
            return (
                <Typography sx={{ fontSize: "11px", color: "grey", fontWeight: 500, display: "flex", alignItems: "center", gap: 0.5, minWidth: "fit-content" }}>
                    Never online
                </Typography>
            );
        }

        const timeAgo = getTimeAgo(vessel.last_online_at);

        return (
            <Typography sx={{ fontSize: "11px", color: "grey", fontWeight: 500, display: "flex", alignItems: "center", gap: 0.5, minWidth: "fit-content" }}>
                {timeAgo}
            </Typography>
        );
    };

    return (
        <Grid size="auto">
            <Card
                elevation={0}
                sx={{
                    borderRadius: 0,
                    color: theme.palette.background.default,
                    cursor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "not-allowed" : "pointer",
                    display: "flex",
                    backgroundColor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "primary.light",
                    transition: "0.2s",
                    ":hover": {
                        backgroundColor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "#464F59",
                        transition: "0.2s",
                    },
                    height: { xs: "100%", lg: "auto" },
                }}
                onClick={() => {
                    if (selectedStream.StreamName === stream.StreamName || view === "Mosaic") return;
                    onSelect(stream);
                }}
            >
                <CardContent
                    sx={{
                        width: "100%",
                        padding: 0,
                        "&:last-child": {
                            paddingBottom: 0,
                        },
                    }}
                >
                    <Grid container alignItems={"center"} gap={1.8} padding={{ xs: 1, lg: 1 }}>
                        <Grid>
                            <Grid alignItems={"center"} gap={1} size="auto" sx={{ paddingLeft: "12px" }}>
                                <FiberManualRecordIcon
                                    sx={{
                                        display: "block",
                                        color: stream.IsLive ? theme.palette.custom.live : theme.palette.custom.offline,
                                        fontSize: "16px",
                                        lineHeight: "20px",
                                    }}
                                />
                            </Grid>
                        </Grid>
                        <Grid
                            container
                            flexDirection={"row"}
                            alignItems={"center"}
                            justifyContent={"space-between"}
                            gap={0.5}
                            size={{
                                xs: "grow",
                                lg: "grow",
                            }}
                        >
                            <Grid>
                                <Typography fontSize={15} lineHeight={"25px"} fontWeight={600} display="flex" alignItems="center" gap="6px">
                                    {`${stream.VesselName || "Unregistered"}${devMode || showIDs ? ` (${stream.StreamName})` : ""}`}
                                    {artifactLength > 0 && (
                                        <>
                                            <Typography component="span" sx={{ color: "red", fontSize: "14px", fontWeight: 500 }}>
                                                {artifactLength}
                                            </Typography>
                                            <img
                                                src="/icons/sensor_alert_icon.svg"
                                                alt="Artifact Indicator"
                                                width={18}
                                                height={18}
                                                style={{ padding: 0, margin: -4 }}
                                            />
                                        </>
                                    )}
                                </Typography>
                            </Grid>
                            <Grid>
                                {getLastOnlineDisplay()}
                            </Grid>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
        </Grid>
    );
};

export default SensorCard;
