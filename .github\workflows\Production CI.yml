name: Production CI

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read
  actions: read
  statuses: write

jobs:
  build:
    runs-on: ubuntu-latest

    env:
      NODE_VERSION: '20'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            rewrite/backend/package-lock.json

      - name: Set BUILD_ID
        id: buildid
        run: echo "BUILD_ID=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT

      # ---------- AUTH TO AWS ----------
      - name: Configure A<PERSON> credentials (Access Keys)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      # ---------- UPDATE ENV FILES ----------
      - name: Update frontend .env File
        run: |
            aws ssm get-parameter \
            --name "/quartermaster/prod/env/frontend" \
            --with-decryption \
            --query "Parameter.Value" \
            --output text \
            --region us-east-2 > ./frontend/.env

      - name: Update backend .env File
        run: |
            aws ssm get-parameter \
            --name "/quartermaster/prod/env/backend" \
            --with-decryption \
            --query "Parameter.Value" \
            --output text \
            --region us-east-2 > ./rewrite/backend/.env

      # ---------- FRONTEND ----------
      - name: Install and build frontend
        working-directory: ./frontend
        run: |
          npm ci
          BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build
          mkdir -p dist
          cp -r dist-${{ steps.buildid.outputs.BUILD_ID }}/* dist/
          rm -rf dist-${{ steps.buildid.outputs.BUILD_ID }}

      # ---------- BACKEND ----------
      - name: Install and build backend
        working-directory: ./rewrite/backend
        run: |
          npm ci
          BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build:ci
          mkdir -p dist
          cp -r dist-${{ steps.buildid.outputs.BUILD_ID }}/* dist/
          rm -rf dist-${{ steps.buildid.outputs.BUILD_ID }}

      # ---------- GENERATE BUILD ARTIFACTS ----------
      - name: Generate build artifacts
        run: |
          mkdir -p build_artifacts/frontend build_artifacts/rewrite/backend
          cp -r package.json build_artifacts/
          cp -r frontend/dist/ frontend/package.json frontend/package-lock.json build_artifacts/frontend
          cp -r rewrite/backend/dist/ rewrite/backend/package.json rewrite/backend/package-lock.json build_artifacts/rewrite/backend
          tar -czf ./build_artifacts.tar.gz -C ./build_artifacts .

      # ---------- UPLOAD TO S3 ----------
      - name: Upload to S3
        run: |
          aws s3 cp ./build_artifacts.tar.gz s3://quartermaster-web-application-builds/prod/latest --only-show-errors

      # ---------- INITIATE INSTANCE REFRESH ----------
      - name: Initiate instance refresh
        run: |
          aws autoscaling start-instance-refresh \
            --auto-scaling-group-name "Quartermaster Web App" \
            --preferences '{"MinHealthyPercentage":50, "SkipMatching":false}' \
            --region us-east-2
          
##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
      - name: Set URL based on branch
        run: |
          echo "URL=https://portal.quartermaster.us" >> $GITHUB_ENV

      - name: Notify Slack on success
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took
          custom_payload: |
            {
              "attachments": [
                {
                  "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                  "text": `\nProduction CI (Autoscaled) - ${{ runner.name }}\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on success
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took
          custom_payload: |
            {
              "attachments": [
                {
                  "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                  "text": `\nProduction CI (Autoscaled) - ${{ runner.name }}\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}