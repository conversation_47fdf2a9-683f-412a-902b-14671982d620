#!/usr/bin/env sh
. "$(dirname "$0")/_/husky.sh"

echo "Running backend quality checks before push..."

cd rewrite/backend || exit 1

echo "1) Lint (no warnings)..."
npm run -s lint:check || exit 1

echo "2) Prettier format check..."
npm run -s format:check || exit 1

echo "3) Tests with coverage..."
npm test --silent || exit 1

echo "4) Coverage threshold (avg S/B/F/L >= 95%)..."
npm run -s coverage:check || exit 1

echo "All checks passed. Proceeding with push."

