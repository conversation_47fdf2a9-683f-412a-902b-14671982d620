import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import vesselOnlineLookupService from '../../services/VesselOnlineLookup.service';
import VesselOnlineLookup from '../../models/VesselOnlineLookup';
import mongoose from 'mongoose';

jest.mock('../../models/VesselOnlineLookup', () => require('../mocks/models/vesselOnlineLookup.mock'));

const mockConsoleError = jest.spyOn(console, 'error').mockImplementation(() => { });

describe('VesselOnlineLookupService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockConsoleError.mockClear();
    });

    describe('findByVesselIds', () => {
        it('should return vessel online lookup data for valid vessel IDs', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'];
            const mockVesselOnlineLookups = [
                {
                    _id: new mongoose.Types.ObjectId(),
                    vessel_id: new mongoose.Types.ObjectId(vesselIds[0]),
                    last_online_at: new Date('2025-01-15T10:30:00.000Z'),
                    updated_at: new Date('2025-01-15T10:30:00.000Z')
                },
                {
                    _id: new mongoose.Types.ObjectId(),
                    vessel_id: new mongoose.Types.ObjectId(vesselIds[1]),
                    last_online_at: new Date('2025-01-15T11:30:00.000Z'),
                    updated_at: new Date('2025-01-15T11:30:00.000Z')
                }
            ];

            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue(mockVesselOnlineLookups as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual(mockVesselOnlineLookups);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: vesselIds.map(id => new mongoose.Types.ObjectId(id)) }
            });
            expect(VesselOnlineLookup.find).toHaveBeenCalledTimes(1);
        });

        it('should return empty array when no vessel IDs provided', async () => {
            const vesselIds: string[] = [];
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue([] as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: [] }
            });
        });

        it('should return empty array when no matching vessels found', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue([] as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: vesselIds.map(id => new mongoose.Types.ObjectId(id)) }
            });
        });

        it('should handle single vessel ID', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            const mockVesselOnlineLookup = {
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(vesselIds[0]),
                last_online_at: new Date('2025-01-15T10:30:00.000Z'),
                updated_at: new Date('2025-01-15T10:30:00.000Z')
            };
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue([mockVesselOnlineLookup] as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([mockVesselOnlineLookup]);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: [new mongoose.Types.ObjectId(vesselIds[0])] }
            });
        });

        it('should handle vessels with null last_online_at', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            const mockVesselOnlineLookup = {
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(vesselIds[0]),
                last_online_at: null,
                updated_at: new Date('2025-01-15T10:30:00.000Z')
            };
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue([mockVesselOnlineLookup] as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([mockVesselOnlineLookup]);
            expect(result[0].last_online_at).toBeNull();
        });

        it('should handle vessels with null updated_at', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            const mockVesselOnlineLookup = {
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(vesselIds[0]),
                last_online_at: new Date('2025-01-15T10:30:00.000Z'),
                updated_at: null
            };
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue([mockVesselOnlineLookup] as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([mockVesselOnlineLookup]);
            expect(result[0].updated_at).toBeNull();
        });

        it('should handle database error and return empty array', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            const mockError = new Error('Database connection failed');
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(mockError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', mockError);
            expect(mockConsoleError).toHaveBeenCalledTimes(1);
        });

        it('should handle mongoose validation error', async () => {
            const vesselIds = ['invalid-id'];
            const mockError = new Error('Cast to ObjectId failed for value "invalid-id"');
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(mockError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', mockError);
        });

        it('should handle network timeout error', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011'];
            const timeoutError = new Error('MongoTimeoutError');
            timeoutError.name = 'MongoTimeoutError';
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(timeoutError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', timeoutError);
        });

        it('should handle large number of vessel IDs', async () => {
            const vesselIds = Array.from({ length: 100 }, (_, i) => `507f1f77bcf86cd7994390${i.toString().padStart(2, '0')}`);
            const mockVesselOnlineLookups = vesselIds.map((id, index) => ({
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(id),
                last_online_at: new Date(`2025-01-15T${10 + index}:30:00.000Z`),
                updated_at: new Date(`2025-01-15T${10 + index}:30:00.000Z`)
            }));
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue(mockVesselOnlineLookups as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual(mockVesselOnlineLookups);
            expect(result).toHaveLength(100);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: vesselIds.map(id => new mongoose.Types.ObjectId(id)) }
            });
        });

        it('should handle mixed valid and invalid vessel IDs', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011', 'invalid-id', '507f1f77bcf86cd799439012'];
            const mockVesselOnlineLookups = [
                {
                    _id: new mongoose.Types.ObjectId(),
                    vessel_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
                    last_online_at: new Date('2025-01-15T10:30:00.000Z'),
                    updated_at: new Date('2025-01-15T10:30:00.000Z')
                }
            ];
            (VesselOnlineLookup.find as jest.Mock).mockResolvedValue(mockVesselOnlineLookups as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual(mockVesselOnlineLookups);
            expect(VesselOnlineLookup.find).toHaveBeenCalledWith({
                vessel_id: { $in: vesselIds.map(id => new mongoose.Types.ObjectId(id)) }
            });
        });

        it('should handle undefined vessel IDs gracefully', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011', undefined as any, '507f1f77bcf86cd799439012'];
            const mockError = new Error('Cast to ObjectId failed for value "undefined"');
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(mockError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', mockError);
        });

        it('should handle null vessel IDs gracefully', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011', null as any, '507f1f77bcf86cd799439012'];
            const mockError = new Error('Cast to ObjectId failed for value "null"');
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(mockError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', mockError);
        });

        it('should handle empty string vessel IDs', async () => {
            const vesselIds = ['507f1f77bcf86cd799439011', '', '507f1f77bcf86cd799439012'];
            const mockError = new Error('Cast to ObjectId failed for value ""');
            (VesselOnlineLookup.find as jest.Mock).mockRejectedValue(mockError as never);

            const result = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            expect(result).toEqual([]);
            expect(mockConsoleError).toHaveBeenCalledWith('Error fetching vessel online lookup data:', mockError);
        });

        it('should handle concurrent calls with different vessel IDs', async () => {
            const vesselIds1 = ['507f1f77bcf86cd799439011'];
            const vesselIds2 = ['507f1f77bcf86cd799439012'];
            const mockResult1 = [{
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(vesselIds1[0]),
                last_online_at: new Date('2025-01-15T10:30:00.000Z'),
                updated_at: new Date('2025-01-15T10:30:00.000Z')
            }];
            const mockResult2 = [{
                _id: new mongoose.Types.ObjectId(),
                vessel_id: new mongoose.Types.ObjectId(vesselIds2[0]),
                last_online_at: new Date('2025-01-15T11:30:00.000Z'),
                updated_at: new Date('2025-01-15T11:30:00.000Z')
            }];

            (VesselOnlineLookup.find as jest.Mock)
                .mockResolvedValueOnce(mockResult1 as never)
                .mockResolvedValueOnce(mockResult2 as never);

            const [result1, result2] = await Promise.all([
                vesselOnlineLookupService.findByVesselIds(vesselIds1),
                vesselOnlineLookupService.findByVesselIds(vesselIds2)
            ]);

            expect(result1).toEqual(mockResult1);
            expect(result2).toEqual(mockResult2);
            expect(VesselOnlineLookup.find).toHaveBeenCalledTimes(2);
        });
    });
});