import mongoose from "mongoose";
import { IUser } from "./User";

export interface ISessionLog {
    _id: mongoose.Types.ObjectId;
    user_id: mongoose.Types.ObjectId;
    connect_timestamp: Date;
    disconnect_timestamp?: Date | null;
    device?: string;
    browser?: string;
    environment: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface ISessionLogWithUser extends ISessionLog {
    user: IUser;
}
