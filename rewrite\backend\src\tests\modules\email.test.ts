import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { sendEmail } from '../../modules/email';

const sendMailMock = (jest.fn() as any).mockResolvedValue(undefined);

jest.mock('googleapis', () => ({
    google: {
        auth: {
            OAuth2: jest.fn().mockImplementation(() => ({
                setCredentials: jest.fn(),
                getAccessToken: (jest.fn() as any).mockResolvedValue({ token: 'access-token' }),
            })),
        },
    },
}));

jest.mock('nodemailer', () => ({
    __esModule: true,
    default: {
        createTransport: jest.fn(() => ({
            sendMail: sendMailMock,
        })),
    },
}));

describe('Email Module', () => {
    const ORIGINAL_ENV = process.env;

    beforeEach(() => {
        process.env = {
            ...ORIGINAL_ENV,
            CLIENT_ID: 'cid',
            CLIENT_SECRET: 'csecret',
            REDIRECT_URI: 'http://localhost',
            REFRESH_TOKEN: 'refresh',
            MAIL_USER: '<EMAIL>',
        } as any;
        jest.clearAllMocks();
        jest.resetModules();
    });

    afterEach(() => {
        process.env = ORIGINAL_ENV;
    });

    it('should successfully send email when valid parameters are provided', async () => {
        const result = await sendEmail({ to: '<EMAIL>', subject: 'Sub', html: '<p>Hi</p>' });
        expect(result).toBe('Email sent');
        expect(sendMailMock).toHaveBeenCalledWith({ to: '<EMAIL>', subject: 'Sub', html: '<p>Hi</p>' });
    });

    it('should handle SMTP failures and propagate error messages to caller', async () => {
        sendMailMock.mockRejectedValueOnce(new Error('SMTP down'));
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        await expect(sendEmail({ to: '<EMAIL>', subject: 'Sub', html: '<p>Hi</p>' })).rejects.toThrow('SMTP down');
        expect(consoleSpy).toHaveBeenCalled();
        consoleSpy.mockRestore();
    });
});