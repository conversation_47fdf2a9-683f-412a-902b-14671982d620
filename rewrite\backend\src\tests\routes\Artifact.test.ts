import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import request from "supertest";
import app from '../../server';
import { artifactsList } from '../data/Artifacts';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { qm, qmai } from '../mocks/modules/db.mock';
import vesselService from '../../services/Vessel.service';
import { vesselsList } from '../data/Vessels';
import { canAccessVessel, groupByImage, validateError, groupArtifactsByDuplicateIndex, generateTimeSeries, userHasPermissions, generateZip } from '../../utils/functions';
import Vessel from '../../models/Vessel';
import { getObjectStream, processBatchItem } from '../../modules/awsS3';
import ioEmitter from '../../modules/ioEmitter';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));
jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn()
}));

describe('Artifacts API', () => {

    describe('POST /api/artifacts/', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (vesselService.findById as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue(vesselsList as never);
                    (groupByImage as jest.Mock).mockReturnValue(artifactsList.slice(0, 10) as never);
                    (groupArtifactsByDuplicateIndex as jest.Mock).mockReturnValue([artifactsList] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue(artifactsList as never),
                                        }),
                                    }),
                                }),
                            }),
                            toArray: jest.fn().mockResolvedValue(artifactsList as never),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50 as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 400 if payload is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch artifacts successfully with valid data', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .set('Referer', '/docs')
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], page: 1, pageSize: 10, filters: {} });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(500);
                });

                it('should return paginated results when page and pageSize are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body.artifacts).toHaveLength(10);
                    expect(res.body).toHaveProperty('totalCount', 50);
                });

                it('should return filtered results based on provided filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            filters: {
                                categories: ['metal'],
                                start_time: 1622547800,
                                end_time: 1622547900,
                                colors: ['red'],
                                sizes: ['small'],
                                type: 'image',
                                vessel_ids: artifactsList.map((artifact) => artifact.onboard_vessel_id),
                                country_flags: ['Alaska'],
                                weapons: ['Missile'],
                                host_vessel: true,
                            },
                            favourites: 1,
                            group: 1,
                            page: 1,
                            pageSize: 10
                        });

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toBeInstanceOf(Array);
                });

                it('should return 400 for invalid page value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 0, pageSize: 10 });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid pageSize value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 0 });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid group value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 2 });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid filters object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid projection fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, projection: { invalidField: 1 } });

                    expect(res.status).toBe(400);
                });

                it('should handle filters with specific artifact ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([artifactsList[0]] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            filters: { id: '66fa86dc64ecc5217496b976' }
                        });

                    expect(res.status).toBe(200);
                });

                it('should handle filters with time range', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 5) as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(5 as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            filters: {
                                start_time: '2024-08-20T01:17:51.524Z',
                                end_time: '2024-08-20T01:21:03.319Z'
                            }
                        });

                    expect(res.status).toBe(200);
                });

                it('should throw 403 if vessel is not authorized', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            filters: { vessel_ids: ['vessel-unauthorized'] }
                        });

                    expect(res.status).toBe(403);
                });

                it('should handle filters with type video and host_vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            projection: {},
                            filters: { type: 'video', host_vessel: false }
                        });

                    expect(res.status).toBe(200);
                });

                it('should return 400 if projection is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            projection: null
                        });

                    expect(res.status).toBe(400);
                });

                it('should handle empty results', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (groupByImage as jest.Mock).mockReturnValue([] as never);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toHaveLength(0);
                    expect(res.body.totalCount).toBe(50);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:vesselName', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            limit: jest.fn().mockReturnValue((artifactsList.slice(0, 10) as never)),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50 as never),
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/vesselA')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 400 if payload is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid', startTimestamp: 'invalid', endTimestamp: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch artifacts successfully with valid data', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(null as never),
                            limit: jest.fn().mockReturnValue((null as never)),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50 as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .set('Referer', '/docs')
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800, endTimestamp: 1622547900 });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(500);
                });

                it('should return paginated results when page and pageSize are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(200);
                    expect(res.body.length).toBe(10);
                });

                it('should return 400 if endTimestamp is provided but startTimestamp is not', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], endTimestamp: 1622547900 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("startTimestamp is required when endTimestamp is provided");
                });

                it('should return 404 if vessel is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(null as never);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe("Unit does not exist or is no longer active");
                });

                it('should return 403 if vessel is not accessible', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe("Cannot access artifacts for 'vesselA'");
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });


    describe('GET /api/artifacts/filters', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/filters');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch filters if the user is authorized or 403 for api-key authorization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    qm.collection.mockReturnValue(({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            }),
                        }),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                        }),
                    }));
                    qmai.collection.mockReturnValue(({
                        distinct: jest.fn().mockResolvedValue(["small", " Small ", "LARGE", null, "large"] as never),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ name: 'Missile', count: 15 }, { name: 'Gun', count: 12 }] as never),
                        }),
                    }));

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    expect(res.body).toHaveProperty('superCategories');
                    expect(res.body).toHaveProperty('countryFlags');
                    expect(res.body).toHaveProperty('sizes');
                    expect(res.body).toHaveProperty('weapons');
                    expect(res.body.sizes).toEqual(["Small", "Large"]);
                    expect(res.body.weapons).toEqual(["Missile", "Gun"]);
                });

                it('should return 500 if an error occurs while fetching filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/activityIndicators/bulk', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.findById as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    qmai.collection.mockReturnValue({
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ _id: 'v1', artifacts: [] }] as never),
                        }),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/activityIndicators/bulk');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if timestamps are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()] });
                    expect(res.status).toBe(400);
                });

                it('should return 403 if no accessible vessels', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(403);
                });

                it('should return grouped results', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(200);
                });

                it('should return 400 if vesselIds is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if startTimestamp is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()] });
                    expect(res.status).toBe(400);

                    require('../../middlewares/validator').validateData = originalValidateData;
                });

                it('should return 500 if an error occurs while fetching activity indicators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findById as jest.Mock).mockRejectedValue(new Error('Database error') as never);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/archived', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([{ _id: 'a1' }] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/archived');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid pagination', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/archived?page=0&pageSize=0')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return archived artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching archived artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        countDocuments: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);

                    require('../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/archive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({ value: { _id: validId } } as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockResolvedValue({ value: null } as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should archive artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while archiving artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/unarchive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({ value: { _id: validId } } as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockResolvedValue({ value: null } as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should unarchive artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while unarchiving artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/hoursAggregatedCount', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        collection: jest.fn(),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([
                                { timestamp: new Date(), count: 3 },
                                { timestamp: new Date(), count: 2 },
                            ] as never),
                        }),
                    } as never);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    (generateTimeSeries as jest.Mock).mockReturnValue({
                        timestamp: new Date(),
                        count: 3,
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/hoursAggregatedCount');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid params', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/hoursAggregatedCount?startTimestamp=abc&endTimestamp=xyz&interval=0')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return aggregated counts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const now = Date.now();
                    const res = await request(app)
                        .get(`/api/artifacts/hoursAggregatedCount?startTimestamp=${now - 3600000}&endTimestamp=${now}&interval=5`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching aggregated counts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const now = Date.now();
                    qmai.collection.mockReturnValue({
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        })
                    });
                    const res = await request(app)
                        .get(`/api/artifacts/hoursAggregatedCount?startTimestamp=${now - 3600000}&endTimestamp=${now}&interval=5`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/bulk', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    const vesselIdsForTest = vesselsList.slice(0, 2).map((v: any) => v._id.toString());
                    const mockArtifacts = artifactsList.slice(0, 5).map((a, idx) => ({
                        ...a,
                        onboard_vessel_id: vesselIdsForTest[idx % vesselIdsForTest.length],
                        image_path: a.image_path || `artifacts/${idx}.jpg`,
                    }));
                    (vesselService.find as jest.Mock).mockResolvedValue(vesselsList as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(mockArtifacts as never),
                            limit: jest.fn().mockResolvedValue(mockArtifacts as never),
                        }),
                    });
                    const actualUtils: any = jest.requireActual('../../utils/functions');
                    (groupByImage as jest.Mock).mockImplementation(actualUtils.groupByImage);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 when endTimestampISO is provided without startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011&endTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 200 with artifacts list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const vesselIdsParam = vesselsList.slice(0, 2).map((v: any) => v._id.toString()).join(',');
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=${vesselIdsParam}&startTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken)
                        .set('Referer', '/docs');
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        })
                    });
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011,507f1f77bcf86cd799439013&startTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/detail/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(artifactsList[0] as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(true as never);
                    (processBatchItem as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/detail/not-an-id`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return artifact detail', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 403 when user does not have permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue({ _id: validId, onboard_vessel_id: null } as never),
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 403 when user does not have vessel permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel.findById as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    (userHasPermissions as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 500 if an error occurs while fetching artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/download', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue({
                            _id: validId,
                            unit_id: 'prototype-32',
                            // onboard_vessel_name: 'Vessel A',
                            bucket_name: 'b',
                            aws_region: 'us-east-1',
                            image_path: 'path/image.jpg',
                            timestamp: new Date().toISOString(),
                        } as never),
                    });
                    (validateError as any).mockImplementation((_err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error' });
                    });
                    (getObjectStream as jest.Mock).mockReturnValue({
                        on: jest.fn((_event, callback: any) => {
                            if (callback) callback();
                            return { on: jest.fn() };
                        }),
                    });
                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, callback: any) => {
                                if (event === 'error') {
                                    setTimeout(() => callback(new Error('Stream error')), 10);
                                }
                            }),
                            pipe: jest.fn().mockReturnValue({
                                on: jest.fn((event, callback: any) => {
                                    if (event === 'finish') {
                                        callback();
                                    }
                                }).mockReturnValue({
                                    on: jest.fn((event, callback: any) => {
                                        if (event === 'error') {
                                            setTimeout(() => callback(new Error('Pipe error')), 10);
                                        }
                                    }),
                                } as never),
                            }),
                        }),
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(true as never);
                    (processBatchItem as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/download`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 500 and stream zip', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/ais-discrepancy', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';
            const userId = userOrApiKey.authorized._id;

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/ais-discrepancy`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid artifact ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/invalid-id/ais-discrepancy')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(null as never),
                    });
                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should flag artifact with AIS discrepancy when not currently flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should unflag artifact when currently flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true
                        }
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should handle artifact with no portal object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should handle artifact with portal object but no ais_discrepancy field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {}
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should return 500 if database error occurs during findOne', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockRejectedValue(new Error('Database connection error') as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database connection error');
                });

                it('should return 500 if database error occurs during findOneAndUpdate', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Update failed') as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Update failed');
                });

                it('should handle multiple flag/unflag cycles correctly', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    let mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: { ...mockArtifact, portal: { ...mockArtifact.portal, ais_discrepancy: true } }
                        } as never),
                    });

                    const res1 = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res1.status).toBe(200);
                    expect(res1.body.message).toBe('AIS flag selection updated ');
                    expect(res1.body.state).toBe('both_not_matched');

                    mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: { ...mockArtifact, portal: { ...mockArtifact.portal, ais_discrepancy: false } }
                        } as never),
                    });

                    const res2 = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_matched' })
                        .set('Authorization', authToken);

                    expect(res2.status).toBe(200);
                    expect(res2.body.message).toBe('AIS flag selection updated ');
                    expect(res2.body.state).toBe('both_matched');
                });

                it('should preserve existing portal fields when updating', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false,
                            other_field: 'existing_value',
                            another_field: 123
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                ...mockArtifact,
                                portal: {
                                    ...mockArtifact.portal,
                                    ais_discrepancy: true,
                                    ais_discrepancy_reporter: userId,
                                    ais_discrepancy_timestamp: expect.any(Date)
                                }
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/archived', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    image_path: 'test-image.jpg',
                                                    video_path: 'test-video.mp4',
                                                    thumbnail_image_path: 'test-thumbnail.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/archived');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if user lacks manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(false);

                    const res = await request(app)
                        .get('/api/artifacts/archived')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch archived artifacts with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body).toHaveProperty('page', 1);
                    expect(res.body).toHaveProperty('pageSize', 10);
                    expect(res.body).toHaveProperty('totalCount', 1);
                    expect(res.body.artifacts[0]).toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    image_path: 'test-image.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    video_path: 'test-video.mp4',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).not.toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    thumbnail_image_path: 'test-thumbnail.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).not.toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).toHaveProperty('thumbnail_url');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database error');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/archive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                video_path: 'test-video.mp4',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                    (ioEmitter.emit as jest.Mock).mockImplementation(() => { });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/507f1f77bcf86cd799439011/archive');
                    expect(res.status).toBe(401);
                });

                it('should return 200 when user has manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 and archive artifact with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Artifact archived successfully');
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                video_path: 'test-video.mp4',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should return 404 if artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: null
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('POST /api/artifacts/:id/unarchive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                video_path: 'test-video.mp4',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                    (ioEmitter.emit as jest.Mock).mockImplementation(() => { });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/507f1f77bcf86cd799439011/unarchive');
                    expect(res.status).toBe(401);
                });

                it('should return 200 when user has manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 and unarchive artifact with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Artifact unarchived successfully');
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                video_path: 'test-video.mp4',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should return 404 if artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: null
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database error');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });
});
