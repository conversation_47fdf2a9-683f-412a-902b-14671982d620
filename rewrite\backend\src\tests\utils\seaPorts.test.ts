import { describe, it, expect } from '@jest/globals';
import seaPorts from '../../utils/seaPorts';

describe('seaPorts', () => {
    it('should export seaPorts array with correct structure', () => {
        expect(seaPorts).toBeDefined();
        expect(Array.isArray(seaPorts)).toBe(true);
    });

    it('should contain port objects with lat and lng properties', () => {
        expect(seaPorts.length).toBeGreaterThan(0);
        seaPorts.forEach(port => {
            expect(port).toHaveProperty('lat');
            expect(port).toHaveProperty('lng');
            expect(typeof port.lat).toBe('number');
            expect(typeof port.lng).toBe('number');
        });
    });

    it('should contain expected port coordinates', () => {
        expect(seaPorts[0]).toEqual({ lat: 8.3333147, lng: 117.207345905 });
        expect(seaPorts[1]).toEqual({ lat: 9.742842744827062, lng: 118.72865546293953 });
    });

    it('should have valid latitude and longitude ranges', () => {
        seaPorts.forEach(port => {
            expect(port.lat).toBeGreaterThanOrEqual(-90);
            expect(port.lat).toBeLessThanOrEqual(90);
            expect(port.lng).toBeGreaterThanOrEqual(-180);
            expect(port.lng).toBeLessThanOrEqual(180);
        });
    });
});
