import mongoose from "mongoose";

export interface IInviteToken {
    _id: mongoose.Types.ObjectId | string;
    token: string;
    invited_by: mongoose.Types.ObjectId | string;
    email: string;
    organization_id: mongoose.Types.ObjectId | string;
    role_id: number;
    role: string;
    allowed_vessels: string[];
    short_token: string;
    is_used: boolean;
    is_deleted: boolean;
    creation_timestamp: Date;
}
