import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import ArtifactSuggestion from '../../models/ArtifactSuggestion';
import ArtifactSynonym from '../../models/ArtifactSynonym';
import Vessel from '../../models/Vessel';
import UserCompletionLogs from '../../models/UserCompletionLogs';
import RegionGroup from '../../models/RegionGroup';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import OpenAI from 'openai';
import { validateError, cleanSuggestion } from '../../utils/functions';
import { qmai } from '../mocks/modules/db.mock';
import { openaiClient } from '../../routes/ArtifactCompletions.route';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/RegionGroup', () => require('../mocks/models/regionGroup.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/ArtifactSuggestion', () => require('../mocks/models/artifactSuggestion.mock'));
jest.mock('../../models/ArtifactSynonym', () => require('../mocks/models/artifactSynonym.mock'));
jest.mock('../../models/UserCompletionLogs', () => require('../mocks/models/userCompletionLogs.mock'));
jest.mock('../../modules/spellingCorrector', () => require('../mocks/modules/spellingCorrector.mock'));
jest.mock('openai', () => require('../mocks/modules/openai.mock').default);
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

describe('ArtifactCompletions API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
        const setMode = (OpenAI)['__setMode'];
        if (setMode) setMode('default');
    });

    describe('POST /api/completions', () => {
        const runTests = function (authMethod, generateToken, userOrApiKey) {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([] as never),
                        }),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439012', name: 'Vessel A' }] as never),
                        }),
                        distinct: jest.fn().mockResolvedValue([] as never),
                    });
                    (RegionGroup.find as jest.Mock).mockResolvedValue([{
                        
                            _id: '507f1f77bcf86cd799439012',
                            name: 'Vessel A',
                            timezone: "UTC",
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            created_by: '507f1f77bcf86cd799439012',
                            creation_timestamp: new Date(),
                            vessels: [{ vessel_id: '507f1f77bcf86cd799439012', unit_id: 'prototype-1', name: 'Vessel A' }]
                        
                    }] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439012', name: 'Vessel A' }] as never);
                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"categories":["Ship"],"vessel_name":["Vessel A"], "start_time": "2025-01-01", "end_time": "2025-01-01"}'
                                    }
                                }]
                            }
                        }]
                    } as never);
                });

                it('should return 200 and normalized metadata for authorized', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439012', type: 'category', word: 'Ship', synonyms: ['Ship'] }] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439012', name: 'Vessel A' }] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'vessel Vessel A in Ship' });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('type', 'both');
                    expect(res.body).toHaveProperty('vessel_ids');
                    expect(UserCompletionLogs.create).toHaveBeenCalled();
                });

                it('should return 400 if tool call missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: []
                            }
                        }]
                    } as never);
                    const setMode = (OpenAI)['__setMode'];
                    if (setMode) setMode('missing');

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'anything' });

                    expect(res.status).toBe(400);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (openaiClient.chat.completions.create as jest.Mock).mockRejectedValueOnce(new Error('Tool call missing') as never);
                    const setMode = (OpenAI)['__setMode'];
                    if (setMode) setMode('error');

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'anything' });

                    expect(res.status).toBe(500);
                });

                it('should handle invalid date parsing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"start_time": "invalid-date", "end_time": "2025-01-01"}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'test with invalid date' });

                    expect(res.status).toBe(200);
                    expect(res.body.start_time).toBeNull();
                    expect(res.body.end_time).toBe('2025-01-01');
                });

                it('should handle existing suggestion update', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"categories":["Ship"]}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    const existingSuggestion = {
                        _id: '507f1f77bcf86cd799439012',
                        search: 'test',
                        click: null,
                        save: jest.fn().mockResolvedValue({} as never)
                    };

                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(existingSuggestion as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'test' });

                    expect(res.status).toBe(200);
                    expect(existingSuggestion.save).toHaveBeenCalled();
                    expect(existingSuggestion.click).toBe(1);
                });

                it('should handle single vessel name (not array)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"vessel_name":"Single Vessel"}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439012', name: 'Single Vessel' }] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'single vessel' });

                    expect(res.status).toBe(200);
                    expect(res.body.vessel_ids).toEqual(['507f1f77bcf86cd799439012']);
                });

                it('should handle time field conversion', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"time":"2025-01-01T12:00:00Z"}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'specific time' });

                    expect(res.status).toBe(200);
                    expect(res.body.start_time).toBe('2025-01-01T12:00:00Z');
                    expect(res.body.end_time).toBe('2025-01-01T12:00:00Z');
                    expect(res.body.time).toBeUndefined();
                });

                it('should handle non-array fields normalization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"country_flags":"US","colors":"red","sizes":"large"}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'non-array fields' });

                    expect(res.status).toBe(200);
                    expect(res.body.country_flags).toEqual(['US']);
                    expect(res.body.colors).toEqual(['red']);
                    expect(res.body.sizes).toEqual(['large']);
                });


                it('should handle missing type field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"categories":["Ship"]}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'no type' });

                    expect(res.status).toBe(200);
                    expect(res.body.type).toBe('both');
                });

                it('should handle non-string type field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"type":123,"categories":["Ship"]}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'non-string type' });

                    expect(res.status).toBe(200);
                    expect(res.body.type).toBe('both');
                });

                it('should handle null array fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    
                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"country_flags":null,"colors":null,"sizes":null}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'null fields' });

                    expect(res.status).toBe(200);
                    expect(res.body.country_flags).toBeNull();
                    expect(res.body.colors).toBeNull();
                    expect(res.body.sizes).toBeNull();
                });


                it('should return 400 for invalid body', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: '' });

                    expect(res.status).toBe(400);
                });


                it('should handle cache logic for prompt', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    
                    (openaiClient.chat.completions.create as jest.Mock).mockResolvedValue({
                        choices: [{
                            message: {
                                tool_calls: [{
                                    function: {
                                        name: 'extract_vessel_metadata',
                                        arguments: '{"categories":["Ship"]}'
                                    }
                                }]
                            }
                        }]
                    } as never);

                    (cleanSuggestion as any).mockImplementation((text: string) => text);
                    (ArtifactSynonym.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (Vessel.find as jest.Mock).mockResolvedValueOnce([] as never);
                    (ArtifactSuggestion.findOne as jest.Mock).mockResolvedValueOnce(null as never);
                    (ArtifactSuggestion.create as jest.Mock).mockResolvedValueOnce({} as never);

                    // First call to populate cache
                    const res1 = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'first call' });

                    expect(res1.status).toBe(200);

                    // Second call to test cache
                    const res2 = await request(app)
                        .post('/api/completions')
                        .set('Authorization', authToken)
                        .send({ text: 'second call' });

                    expect(res2.status).toBe(200);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .post('/api/completions')
                        .send({ text: 'hello' });

                    expect(res.status).toBe(401);
                });

                it('starts monitoring interval when not in test env', async () => {
                    jest.resetModules();
                    await jest.isolateModulesAsync(async () => {
                        const prevEnv = process.env.NODE_ENV;
                        process.env.NODE_ENV = 'dev';
                        const setIntervalSpy = jest.spyOn(global, 'setInterval').mockImplementation((callback, _interval) => {
                            callback();
                            return {} as NodeJS.Timeout;
                        });
                        require('../../routes/ArtifactCompletions.route');
                        expect(setIntervalSpy).toHaveBeenCalled();
                        setIntervalSpy.mockRestore();
                        process.env.NODE_ENV = prevEnv;
                    });
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});