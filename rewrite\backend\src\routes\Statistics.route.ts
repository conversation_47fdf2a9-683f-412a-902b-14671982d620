import express from "express";
import { validateError } from "../utils/functions";
import { validateData } from "../middlewares/validator";
import { query } from "express-validator";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import isAuthenticated from "../middlewares/auth";
import hasPermission from "../middlewares/hasPermission";
import { permissions } from "../utils/permissions";
import Statistics from "../models/Statistics";
import { FilterQuery } from "mongoose";
import { IStatistics } from "src/interfaces/Statistics";

const router = express.Router();

// Helper function to limit arrays to 5 elements and objects to 5 keys for Swagger
export const limitStatsData = (data: any, limit: boolean = false): any => {
    if (Array.isArray(data)) {
        return data.slice(0, 5);
    } else if (data && typeof data === "object") {
        let entries = Object.entries(data);
        if (limit) {
            entries = entries.slice(0, 5);
        }
        const limitedObject: any = {};

        entries.forEach(([key, value]) => {
            if (Array.isArray(value)) {
                limitedObject[key] = value.slice(0, 5);
            } else if (value && typeof value === "object") {
                limitedObject[key] = limitStatsData(value, true);
            } else {
                limitedObject[key] = value;
            }
        });

        return limitedObject;
    }
    return data;
};

const apiLimiter = rateLimit({
    windowMs: 60 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_STATISTICS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.viewStatistics]),
    (req, res, next) =>
        validateData(
            [
                query("type")
                    .isString()
                    .isIn(["daily", "weekly"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const requestURL = req.get("Referer");
            const isSwagger = requestURL ? requestURL.includes("/docs") : false;

            const { type } = req.query;

            const query: FilterQuery<IStatistics> = {};
            if (type) query.type = type;

            const limit = isSwagger ? 4 : undefined;
            const statistics = await Statistics.find(
                query,
                {},
                {
                    sort: { fromTimestamp: -1 },
                    limit: limit,
                },
            );

            // For Swagger, limit arrays to 5 elements and objects to 5 keys in stats
            if (isSwagger) {
                const limitedStatistics = statistics.map((stat) => {
                    const limitedStat = { ...stat.toObject() };
                    if (limitedStat.stats && typeof limitedStat.stats === "object") {
                        limitedStat.stats = limitStatsData(limitedStat.stats);
                    }
                    return limitedStat;
                });
                res.json(limitedStatistics);
            } else {
                res.json(statistics);
            }
        } catch (err) {
            validateError(err, res);
        }
    },
);

/**
 * @swagger
 * tags:
 *   - name: Statistics
 *     description: Fetch aggregate statistics
 * components:
 *   schemas:
 *     Statistics:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the statistics
 *           example: 67942a74a7f838634a00190a
 *         stats:
 *           type: object
 *           description: Mixed type containing the statistics data
 *           example:
 *             totalVesselsDetected: 34
 *             totalSmartmastsOnline: 300
 *         fromTimestamp:
 *           type: string
 *           format: date-time
 *           description: The starting timestamp for the statistics period
 *           example: "2024-09-30T00:00:00.000Z"
 *         toTimestamp:
 *           type: string
 *           format: date-time
 *           description: The ending timestamp for the statistics period
 *           example: "2024-10-07T00:00:00.000Z"
 *         type:
 *           type: string
 *           enum: [daily, weekly]
 *           description: The type of statistics (daily or weekly)
 *           example: "weekly"
 */

/**
 * @swagger
 * /statistics:
 *   get:
 *     summary: Fetch aggregate statistics
 *     description: Retrieves a list of statistics records, either daily or weekly based on the query parameter.<br/>Rate limited to 10 requests every 60 seconds
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [daily, weekly]
 *         description: Type of statistics to fetch (daily or weekly). Optional, if not provided returns all.
 *         required: false
 *     responses:
 *       200:
 *         description: A list of statistics records
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Statistics'
 *       400:
 *         description: Invalid request, usually when the provided type is incorrect.
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       403:
 *         description: Forbidden, the user doesn't have permission to view statistics.
 *       500:
 *         description: Internal server error
 */

export default router;
