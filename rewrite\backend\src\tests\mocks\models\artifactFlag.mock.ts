import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockArtifactFlag(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

(MockArtifactFlag as AnyObject).find = jest.fn();
(MockArtifactFlag as AnyObject).findOne = jest.fn();
(MockArtifactFlag as AnyObject).aggregate = jest.fn();
(MockArtifactFlag as AnyObject).deleteMany = jest.fn();
(MockArtifactFlag as AnyObject).findOneAndDelete = jest.fn();

(MockArtifactFlag as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
};

export default MockArtifactFlag as any;


