import mongoose from "mongoose";

export interface ICoordinates {
    type: string;
    coordinates: [number, number];
}

export interface ILocation {
    _id: mongoose.Types.ObjectId | string;
    latitude: number;
    longitude: number;
    heading: number;
    groundSpeed: number;
    timestamp: Date;
}

export interface ILocationRaw {
    _id: mongoose.Types.ObjectId;
    location: ICoordinates;
    headingMotion: number;
    groundSpeed: number;
    timestamp: Date;
    metadata: {
        onboardVesselId: mongoose.Types.ObjectId | string;
        unitId?: string;
    };
    details: Record<string, string | number | boolean>;
    isStationary: boolean;
    accuracyHeading: number;
}

export interface ILocationOptimized {
    _id: mongoose.Types.ObjectId;
    location: ICoordinates;
    headingMotion: number;
    groundSpeed: number;
    timestamp: Date;
    metadata: {
        onboardVesselId: mongoose.Types.ObjectId | string;
        unitId?: string;
    };
    details: Record<string, string | number | boolean>;
    isStationary: boolean;
    accuracyHeading: number;
}

export interface ILastLocation {
    _id: mongoose.Types.ObjectId | string;
    vessel_id: mongoose.Types.ObjectId | string;
    data: {
        location: ICoordinates;
        timestamp: Date;
        groundSpeed: number;
        isStationary: boolean;
        metadata?: {
            unitId?: string;
        };
    };
}

export interface IClosestLocation {
    _id: mongoose.Types.ObjectId | string;
    timestamp: Date;
    groundSpeed: number;
    isStationary: boolean;
    latitude: number;
    longitude: number;
}
