import mongoose from "mongoose";
import { IArtifact } from "./Artifact";

export interface IArtifactFlag {
    _id: mongoose.Types.ObjectId;
    artifactId: mongoose.Types.ObjectId;
    flaggedBy: mongoose.Types.ObjectId;
    flaggedAt: Date;
}

export interface IFlaggedArtifact {
    _id: mongoose.Types.ObjectId;
    artifactId: mongoose.Types.ObjectId;
    artifact: IArtifact;
    flags: Array<{
        _id: mongoose.Types.ObjectId;
        flaggedBy: mongoose.Types.ObjectId;
        flaggedByUser: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        flaggedAt: Date;
        user: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        created_at: Date;
    }>;
    flagCount: number;
    latestFlagDate: Date;
}
