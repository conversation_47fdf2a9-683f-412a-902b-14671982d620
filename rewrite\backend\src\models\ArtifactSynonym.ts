import mongoose from "mongoose";
import db from "../modules/db";
import { IArtifactSynonym } from "../interfaces/ArtifactSuggestion";

const ArtifactSynonymSchema = new mongoose.Schema({
    type: { type: String, required: true },
    word: { type: String, required: true },
    synonyms: { type: [String], required: true },
});

const ArtifactSynonym = db.qm.model<IArtifactSynonym>("ArtifactSynonym", ArtifactSynonymSchema, "artifact_synonyms");

export default ArtifactSynonym;
