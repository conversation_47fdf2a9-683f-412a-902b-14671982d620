import mongoose from 'mongoose';

export const vesselsList = [
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
        unit_id: 'prototype-30',
        name: '<PERSON><PERSON> MRRV-9701',
        thumbnail_s3_key: 'vessels/thumbnail/507f1f77bcf86cd799439011.jpg',
        is_active: true,
        region_group_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439012'),
        home_port_location: {
            type: 'Point',
            coordinates: [120.9842, 14.5995]
        }
    },
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439013'),
        unit_id: 'prototype-31',
        name: 'BRP Gabriela Silang OPV-8301',
        thumbnail_s3_key: 'vessels/thumbnail/507f1f77bcf86cd799439013.jpg',
        is_active: false,
        region_group_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439014'),
        home_port_location: {
            type: 'Point',
            coordinates: [121.0000, 14.6000]
        }
    }
];

export const streamsList = [
    {
        unit_id: 'prototype-30',
        region: 'ap-southeast-1',
        is_live: true
    },
    {
        unit_id: 'prototype-31',
        region: 'us-east-1',
        is_live: false
    }
];

export const regionGroupsList = [
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439012'),
        timezone: '+08:00',
        vessel_ids: [new mongoose.Types.ObjectId('507f1f77bcf86cd799439011')]
    },
    {
        _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439014'),
        timezone: '+05:00',
        vessel_ids: [new mongoose.Types.ObjectId('507f1f77bcf86cd799439013')]
    }
];
