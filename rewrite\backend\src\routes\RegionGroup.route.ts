import express, { Request, Response } from "express";
import hasPermission from "../middlewares/hasPermission";
import { permissions } from "../utils/permissions";
import { validateData } from "../middlewares/validator";
import { body, param } from "express-validator";
import { validateError } from "../utils/functions";
import mongoose, { isValidObjectId } from "mongoose";
import { isValidTimezoneOffset } from "../utils/timezonesList";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import regionGroupService from "../services/RegionGroup.service";
// import RegionGroup from "../models/RegionGroup";
import { IRegionGroup } from "../interfaces/RegionGroup";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_REGION_GROUPS), isAuthenticated, async (req: Request, res: Response) => {
    try {
        const regionGroups: IRegionGroup[] = await regionGroupService.find({ req });
        res.json(regionGroups);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        // body("vessel_ids")
        //     .isArray()
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        // body("vessel_ids.*")
        //     .isString()
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { name, timezone } = req.body as { name: string; timezone: string };
            // const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
            // const vesselsAlreadyInGroup = await RegionGroup.find({ vessel_ids: { $in: objectIds } }, { _id: 1 });
            // if (vesselsAlreadyInGroup.length > 0) {
            //     res.status(409).json({ message: `Vessel is already assigned to a region group.` });
            //     return;
            // }
            const regionGroup: IRegionGroup = await regionGroupService.create({
                name,
                timezone,
                // vessel_ids: vessel_ids.map((id) => mongoose.Types.ObjectId(id)),
                created_by: new mongoose.Types.ObjectId(req.user._id),
            });
            res.json({ message: `Region group has been created`, regionGroup });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("timezone")
            .isString()
            .notEmpty()
            .custom(isValidTimezoneOffset)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        // body("vessel_ids")
        //     .isArray()
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        // body("vessel_ids.*")
        //     .isString()
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const data: { name?: string; timezone?: string } = req.body;
            // const { vessel_ids } = data;
            // const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
            // const vesselsAlreadyInGroup = await RegionGroup.find({ _id: { $ne: req.params.id }, vessel_ids: { $in: objectIds } }, { _id: 1 });
            // if (vesselsAlreadyInGroup.length > 0) {
            //     res.status(409).json({ message: `Vessel is already assigned to a region group.` });
            //     return;
            // }
            const result: IRegionGroup | null = await regionGroupService.update({ id: req.params.id, ...data });

            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group '${data.name}' has been edited`, regionGroup: result });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_REGION_GROUP),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRegionsGroups]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;

            const result: boolean | null = await regionGroupService.delete({ id });
            if (!result) return res.status(400).json({ message: `Region group does not exist` });

            return res.json({ message: `Region group has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * tags:
 *   name: Region Groups
 *   description: Fetch region groups
 * components:
 *   schemas:
 *     RegionGroup:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the region group
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: Name of the region group
 *           example: Philipines
 *         timezone:
 *           type: string
 *           description: Timezone of the region group
 *           example: +8:00
 *         vessels:
 *           type: array
 *           description: Information about the vessels in the region group
 *           items:
 *             type: object
 *             properties:
 *               vessel_id:
 *                 type: string
 *                 description: Document Id of the vessel
 *                 example: 67942a74a7f838634a00190a
 *               unit_id:
 *                 type: string
 *                 description: Unit id of the camera
 *                 example: prototype-24
 *               name:
 *                 type: string
 *                 description: Name of the vessel
 *                 example: BRP Bagacay (MRRV-4410)
 *         created_by:
 *           type: string
 *           description: Document Id of the user who created the region group
 *           example: 67942a74a7f838634a00190a
 *         vessel_ids:
 *           type: array
 *           description: A list of vessel ids in the region group
 *           items:
 *             type: string
 *             example: "762486b8-d22b-4813-b488-a4242017a47b"
 *         creationTimestamp:
 *           type: string
 *           description: Creation timestamp of the region group
 *           example: 2025-06-24T10:00:00.000Z
 */

/**
 * @swagger
 * /regionGroups:
 *   get:
 *     summary: Fetch all region groups
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Region Groups]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of region groups
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/RegionGroup'
 *       500:
 *         description: Server error
 */
