import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ArtifactSynonym Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ArtifactSynonym model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactSynonym')];

        const ArtifactSynonymModule = await import('../../models/ArtifactSynonym');
        const ArtifactSynonym = ArtifactSynonymModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ArtifactSynonym', expect.any(Object), 'artifact_synonyms');
        expect(ArtifactSynonym).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.type).toBeDefined();
        expect(schemaArg.paths.type.type).toBe(String);
        expect(schemaArg.paths.type.required).toBe(true);

        expect(schemaArg.paths.word).toBeDefined();
        expect(schemaArg.paths.word.type).toBe(String);
        expect(schemaArg.paths.word.required).toBe(true);

        expect(schemaArg.paths.synonyms).toBeDefined();
        expect(schemaArg.paths.synonyms.type).toEqual([String]);
        expect(schemaArg.paths.synonyms.required).toBe(true);
    });
});
