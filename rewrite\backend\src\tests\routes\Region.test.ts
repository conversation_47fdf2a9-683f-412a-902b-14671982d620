import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { regionsList } from '../data/Regions';
import Region from '../mocks/models/region.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Region', () => require('../mocks/models/region.mock'));

describe('Region API', () => {
    describe('GET /api/regions', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/regions');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch all regions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Region as any).find.mockResolvedValueOnce(regionsList);

                    const res = await request(app)
                        .get('/api/regions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(regionsList);
                    expect((Region as any).find).toHaveBeenCalledWith();
                });

                it('should return 200 with empty array when no regions found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Region as any).find.mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/regions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual([]);
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Region as any).find.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/regions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});