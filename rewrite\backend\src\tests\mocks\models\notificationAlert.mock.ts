import { jest } from '@jest/globals';

const NotificationAlert = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    create: jest.fn(),
    countDocuments: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    populate: jest.fn().mockReturnThis(),
    exec: jest.fn(),
    save: jest.fn(),
    toObject: jest.fn(),
    toJSON: jest.fn(),
    _id: '507f1f77bcf86cd799439011',
    super_category: ['Ship'],
    sub_category: ['Battleship'],
    country_flags: ['US'],
    type: 'email',
    title: ['Test Alert'],
    vessel_ids: ['507f1f77bcf86cd799439012'],
    receivers: ['<EMAIL>'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_enabled: true,
    created_by: '507f1f77bcf86cd799439013'
};

export default NotificationAlert;
