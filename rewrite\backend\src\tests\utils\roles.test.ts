import { describe, it, expect } from '@jest/globals';

const { roles } = require('../../utils/roles');

describe('roles', () => {
    it('should export roles object with correct structure', () => {
        expect(roles).toBeDefined();
        expect(typeof roles).toBe('object');
    });

    it('should contain super_admin role', () => {
        expect(roles.super_admin).toBe(1);
    });

    it('should have numeric role values', () => {
        const values = Object.values(roles);
        expect(values.every(value => typeof value === 'number')).toBe(true);
    });
});
