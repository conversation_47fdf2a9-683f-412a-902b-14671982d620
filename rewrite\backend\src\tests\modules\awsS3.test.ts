import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { Readable } from 'node:stream';

const originalEnv = process.env;

const mockS3 = {
    listObjectsV2: jest.fn(),
    headObject: jest.fn(),
    upload: jest.fn(),
    deleteObject: jest.fn(),
    getObject: jest.fn(),
    getSignedUrl: jest.fn().mockReturnValue('https://signed-url'),
    copyObject: jest.fn(),
    putObject: jest.fn(),
    config: {
        update: jest.fn(),
    },
};

const mockCloudFrontClient = {
    send: (jest.fn() as any),
};

const mockSharpInstance = {
    resize: jest.fn().mockReturnThis(),
    jpeg: jest.fn().mockReturnThis(),
    toBuffer: (jest.fn() as any).mockResolvedValue(Buffer.from('resized-bytes')),
};
const mockSharp = jest.fn(() => mockSharpInstance);
const mockGetSignedUrl = jest.fn();
const mockGetContentTypeFromFileExtension = jest.fn();
const mockConsoleLogObjectSize = jest.fn();

const mockDb = {
    qmai: {
        collection: jest.fn(() => ({
            updateMany: jest.fn(),
        })),
    },
};

jest.mock('aws-sdk', () => {
    return {
        S3: jest.fn(() => mockS3),
        config: {
            update: jest.fn(),
        },
    };
});

jest.mock('@aws-sdk/client-cloudfront', () => ({
    CloudFrontClient: jest.fn(() => mockCloudFrontClient),
    ListDistributionsCommand: jest.fn(),
}));

jest.mock('@aws-sdk/cloudfront-signer', () => ({
    getSignedUrl: mockGetSignedUrl,
}));

jest.mock('sharp', () => mockSharp);

jest.mock('../../modules/db', () => ({
    __esModule: true,
    default: mockDb,
}));

jest.mock('../../utils/functions', () => ({
    getContentTypeFromFileExtension: mockGetContentTypeFromFileExtension,
    consoleLogObjectSize: mockConsoleLogObjectSize,
}));

describe('AWS S3 Module', () => {
    beforeEach(() => {
        process.env = {
            ...originalEnv,
            AWS_ACCESS_KEY_ID: 'test-access-key',
            AWS_SECRET_ACCESS_KEY: 'test-secret-key',
            AWS_COMPRESSED_ITEMS_BUCKET: 'test-compressed-bucket',
            AWS_COMPRESSED_ITEMS_REGION: 'us-east-1',
            CLOUDFRONT_KEY_PAIR_ID: 'test-key-pair-id',
            CLOUDFRONT_PRIVATE_KEY: 'test-private-key'
        };
        jest.clearAllMocks();
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    it('should throw error when AWS credentials environment variables are not configured', () => {
        delete process.env.AWS_ACCESS_KEY_ID;
        delete process.env.AWS_SECRET_ACCESS_KEY;

        expect(() => {
            require('../../modules/awsS3');
        }).toThrow('AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY must be set in env variables');
    });

    it('should initialize S3 and CloudFront clients with configured AWS credentials', () => {
        const AWS = require('aws-sdk');
        const { CloudFrontClient } = require('@aws-sdk/client-cloudfront');

        require('../../modules/awsS3');

        expect(AWS.S3).toHaveBeenCalledWith({
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            },
        });

        expect(CloudFrontClient).toHaveBeenCalledWith({
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            },
            region: 'us-east-1',
        });
    });

    it('should export S3 configuration with proper bucket and region settings', () => {
        const awsS3Module = require('../../modules/awsS3');

        expect(awsS3Module.s3Config).toBeDefined();
        expect(awsS3Module.s3Config.buckets.assets.name).toBe('quartermaster-assets');
        expect(awsS3Module.s3Config.buckets.assets.region).toBe('us-east-1');
        expect(awsS3Module.s3Config.buckets.compressedItems.name).toBe(process.env.AWS_COMPRESSED_ITEMS_BUCKET);
        expect(awsS3Module.s3Config.buckets.compressedItems.region).toBe(process.env.AWS_COMPRESSED_ITEMS_REGION);
        expect(awsS3Module.s3Config.settings.signatureVersion).toBe('v4');
    });

    it('should export all required S3 and CloudFront operation functions', () => {
        const awsS3Module = require('../../modules/awsS3');

        expect(typeof awsS3Module.uploadFileToS3).toBe('function');
        expect(typeof awsS3Module.deleteFileFromS3).toBe('function');
        expect(typeof awsS3Module.getS3Object).toBe('function');
        expect(typeof awsS3Module.getCloudfrontSignedUrl).toBe('function');
        expect(typeof awsS3Module.generateS3FallbackUrl).toBe('function');
        expect(typeof awsS3Module.processBatchItem).toBe('function');
    });

    it('should successfully upload files to S3 with proper bucket and key configuration', async () => {
        const awsS3Module = require('../../modules/awsS3');

        mockS3.upload.mockReturnValue({
            promise: (jest.fn() as any).mockResolvedValue({ Key: 'test-key-generated' })
        });

        const mockFile = {
            buffer: Buffer.from('test file content'),
            originalname: 'test-file.jpg',
            mimetype: 'image/jpeg'
        };

        const result = await awsS3Module.uploadFileToS3(
            mockFile,
            { name: 'test-bucket', region: 'us-east-1' },
            'test-key'
        );

        expect(mockS3.upload).toHaveBeenCalled();
        expect(result).toBe('test-key-generated');
    });

    it('should return provided key when filePath already contains file extension', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.upload.mockReturnValue({
            promise: (jest.fn() as any).mockResolvedValue({ Key: 'file.jpg' })
        });
        const mockFile = { buffer: Buffer.from('x'), originalname: 'x.dat', mimetype: 'application/octet-stream' };
        const key = await awsS3Module.uploadFileToS3(mockFile as any, { name: 'b', region: 'us-east-1' }, 'file.jpg');
        expect(key).toBe('file.jpg');
    });

    it('should throw descriptive error message when S3 upload operation fails', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.upload.mockReturnValue({
            promise: (jest.fn() as any).mockRejectedValue(new Error('upload-failed'))
        });
        const mockFile = { buffer: Buffer.from('x'), originalname: 'x.jpg', mimetype: 'image/jpeg' };
        await expect(
            awsS3Module.uploadFileToS3(mockFile as any, { name: 'b', region: 'us-east-1' }, 'dir')
        ).rejects.toThrow('Failed to upload file to S3: upload-failed');
    });

    it('should successfully delete files from S3 using bucket and key parameters', async () => {
        const awsS3Module = require('../../modules/awsS3');

        mockS3.deleteObject.mockReturnValue({
            promise: (jest.fn() as any).mockResolvedValue({})
        });

        await awsS3Module.deleteFileFromS3({ name: 'test-bucket', region: 'us-east-1' }, 'test-key');

        expect(mockS3.deleteObject).toHaveBeenCalled();
    });

    it('should retrieve file objects from S3 with proper content type and body data', async () => {
        const awsS3Module = require('../../modules/awsS3');

        const mockReq = { promise: (jest.fn() as any).mockResolvedValue({ Body: Buffer.from('x'), ContentType: 'image/jpeg' }) };
        mockS3.getObject.mockReturnValue(mockReq);
        const resultPromise = await awsS3Module.getS3Object('test-bucket', 'us-east-1', 'test-key', true);
        expect(mockS3.getObject).toHaveBeenCalledWith({ Bucket: 'test-bucket', Key: 'test-key' });
        expect(resultPromise).toEqual({ Body: Buffer.from('x'), ContentType: 'image/jpeg' });
    });

    it('should handle CloudFront signed URL generation (returns null without cache)', () => {
        const awsS3Module = require('../../modules/awsS3');
        const result = awsS3Module.getCloudfrontSignedUrl({ fileName: 'path/file.jpg', bucketName: 'some-bucket' });
        expect(result).toBeNull();
    });

    it('getCloudfrontSignedUrl throws on invalid params', () => {
        const awsS3Module = require('../../modules/awsS3');
        expect(() => awsS3Module.getCloudfrontSignedUrl({ fileName: '', bucketName: 'b' })).toThrow('fileName is required');
        expect(() => awsS3Module.getCloudfrontSignedUrl({ fileName: 'x', bucketName: '' })).toThrow('bucketName is required');
    });

    it('should handle S3 fallback URL generation', () => {
        const awsS3Module = require('../../modules/awsS3');
        const result = awsS3Module.generateS3FallbackUrl('test-bucket', 'test-key.jpg', 'us-east-1');
        expect(result).toBeDefined();
    });

    it('generateS3FallbackUrl throws when fileType missing', () => {
        const awsS3Module = require('../../modules/awsS3');
        expect(() => awsS3Module.generateS3FallbackUrl('b', 'file.', 'us-east-1')).toThrow('fileType not found');
    });

    it('buildThumbnailImage resizes and uploads thumbnail (no DB update when ETag missing)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Body: Buffer.from('orig') }) });
        mockS3.putObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ ETag: '' }) });
        const out = await awsS3Module.buildThumbnailImage('bucket', 'us-east-1', 'dir/file.jpg', 'unit');
        expect(mockSharp).toHaveBeenCalled();
        expect(mockS3.putObject).toHaveBeenCalled();
        expect(out).toBeUndefined();
    });

    it('buildThumbnailImage catches errors from S3 and rethrows', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockRejectedValue(new Error('s3-get-error')) });
        await expect(awsS3Module.buildThumbnailImage('bucket', 'us-east-1', 'dir/file.jpg', 'unit')).rejects.toThrow('s3-get-error');
    });

    it('buildThumbnailImage throws when compressed bucket not configured', async () => {
        const _awsS3Module = require('../../modules/awsS3');
        const prevBucket = process.env.AWS_COMPRESSED_ITEMS_BUCKET;
        const prevRegion = process.env.AWS_COMPRESSED_ITEMS_REGION;
        process.env.AWS_COMPRESSED_ITEMS_BUCKET = '' as any;
        process.env.AWS_COMPRESSED_ITEMS_REGION = '' as any;
        jest.resetModules();
        const reloaded = require('../../modules/awsS3');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Body: Buffer.from('orig') }) });
        await expect(reloaded.buildThumbnailImage('bucket', 'us-east-1', 'dir/file.jpg', 'unit')).rejects.toThrow('Compressed items bucket configuration is missing');
        process.env.AWS_COMPRESSED_ITEMS_BUCKET = prevBucket;
        process.env.AWS_COMPRESSED_ITEMS_REGION = prevRegion;
    });

    it('processBatchItem should use CloudFront URL when available (seed cache)', async () => {
        jest.resetModules();
        mockCloudFrontClient.send.mockResolvedValue({
            DistributionList: {
                Items: [
                    {
                        DomainName: 'd111111abcdef8.cloudfront.net',
                        Origins: { Items: [{ DomainName: 'quartermaster-assets.s3.us-east-1.amazonaws.com' }] },
                    },
                ],
            },
        });
        mockGetSignedUrl.mockReturnValue('https://d111111abcdef8.cloudfront.net/k.jpg');
        jest.isolateModules(async () => {
            const awsS3Module = require('../../modules/awsS3');
            await new Promise((r) => setTimeout(r, 0));
            const res = awsS3Module.processBatchItem({ bucketName: 'quartermaster-assets', key: 'k.jpg', region: 'us-east-1' });
            expect(res.signedUrl).toBe('https://d111111abcdef8.cloudfront.net/k.jpg');
        });
    });

    it('getItemObject returns request or promise and handles errors', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const reqObj: any = { some: 'request' };
        mockS3.getObject.mockReturnValueOnce(reqObj);
        const req = await awsS3Module.getS3Object('b', 'us-east-1', 'k', false);
        expect(req).toBe(reqObj);
        mockS3.getObject.mockImplementationOnce(() => { throw new Error('boom'); });
        await expect(awsS3Module.getS3Object('b', 'us-east-1', 'k', false)).rejects.toThrow('boom');
    });

    it('getItemObjectPart serves full file (200) and invalid range (416)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockResolvedValue({ ContentLength: 100, ETag: 'etag' }) });
        const createReadStream = () => Readable.from(Buffer.alloc(10));
        mockS3.getObject.mockReturnValueOnce({ createReadStream } as any);
        const res200: any = {
            setHeader: jest.fn(),
            writeHead: jest.fn(),
            on: jest.fn(),
            headersSent: false,
            write: jest.fn(),
            end: jest.fn(),
            status: jest.fn().mockReturnThis(),
            send: jest.fn(),
        };
        const req200: any = { headers: {} };
        await awsS3Module.getItemObjectPart(req200, res200, 'b', 'us-east-1', 'k');
        expect(res200.writeHead).toHaveBeenCalledWith(200, expect.objectContaining({ 'Content-Length': 100 }));
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockResolvedValue({ ContentLength: 50 }) });
        const res416: any = { setHeader: jest.fn(), status: jest.fn().mockReturnThis(), send: jest.fn() };
        const req416: any = { headers: { range: 'bytes=60-10' } };
        await awsS3Module.getItemObjectPart(req416, res416, 'b', 'us-east-1', 'k');
        expect(res416.status).toHaveBeenCalledWith(416);
        expect(res416.send).toHaveBeenCalledWith('Range Not Satisfiable');
    });

    it('getItemObjectPart serves partial content (206) and handles stream error', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockResolvedValue({ ContentLength: 100, ETag: 'etag' }) });
        const partialStream = Readable.from(Buffer.alloc(20));
        mockS3.getObject.mockReturnValueOnce({ createReadStream: () => partialStream } as any);
        const res206: any = {
            setHeader: jest.fn(),
            writeHead: jest.fn(),
            on: jest.fn(),
            headersSent: false,
            write: jest.fn(),
            end: jest.fn(),
            status: jest.fn().mockReturnThis(),
            send: jest.fn(),
        };
        const req206: any = { headers: { range: 'bytes=10-19' } };
        await awsS3Module.getItemObjectPart(req206, res206, 'b', 'us-east-1', 'k');
        expect(res206.writeHead).toHaveBeenCalledWith(206, expect.objectContaining({ 'Content-Range': 'bytes 10-19/100', 'Content-Length': 10 }));
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockResolvedValue({ ContentLength: 100 }) });
        const errorStream = new Readable({ read() { } });
        setTimeout(() => {
            errorStream.emit('error', { code: 'NoSuchKey' });
        }, 0);
        mockS3.getObject.mockReturnValueOnce({ createReadStream: () => errorStream } as any);
        const resErr: any = { setHeader: jest.fn(), headersSent: false, status: jest.fn().mockReturnThis(), send: jest.fn(), writeHead: jest.fn(), on: jest.fn(), write: jest.fn(), end: jest.fn() };
        await awsS3Module.getItemObjectPart({ headers: {} } as any, resErr, 'b', 'us-east-1', 'k');
        await new Promise(r => setTimeout(r, 0));
        expect(resErr.status).toHaveBeenCalledWith(404);
        expect(resErr.send).toHaveBeenCalledWith('File not found in S3');
    });

    it('getItemObjectPart handles headObject AccessDenied (403)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockRejectedValue({ code: 'AccessDenied' }) });
        const res: any = { setHeader: jest.fn(), headersSent: false, status: jest.fn().mockReturnThis(), send: jest.fn() };
        await awsS3Module.getItemObjectPart({ headers: {} } as any, res, 'b', 'us-east-1', 'k');
        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.send).toHaveBeenCalledWith('Access denied to S3 resource');
    });

    it('getItemObjectPart handles headObject NoSuchBucket (500)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockRejectedValue({ code: 'NoSuchBucket' }) });
        const res: any = { setHeader: jest.fn(), headersSent: false, status: jest.fn().mockReturnThis(), send: jest.fn() };
        await awsS3Module.getItemObjectPart({ headers: {} } as any, res, 'b', 'us-east-1', 'k');
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Server configuration error (Bucket not found)');
    });

    it('getItemObjectPart handles stream error after headers sent (ends response)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockResolvedValue({ ContentLength: 100 }) });
        const stream = new Readable({ read() { } });
        mockS3.getObject.mockReturnValueOnce({ createReadStream: () => stream } as any);
        const res: any = { setHeader: jest.fn(), headersSent: true, end: jest.fn(), writeHead: jest.fn(), on: jest.fn() };
        awsS3Module.getItemObjectPart({ headers: {} } as any, res, 'b', 'us-east-1', 'k');

        setImmediate(() => {
            stream.emit('error', { code: 'AccessDenied' });
        });

        await new Promise(resolve => setImmediate(resolve));
        expect(res.end).toHaveBeenCalled();
    });

    it('processBatchItem should fall back to S3 when CloudFront returns null', () => {
        const awsS3Module = require('../../modules/awsS3');
        const res = awsS3Module.processBatchItem({ bucketName: 'other-bucket', key: 'k.jpg', region: 'us-east-1' });
        expect(res.signedUrl).toBeDefined();
        expect(res.signedUrl).toContain('https://');
    });

    it('processBatchItem catch path returns fallback when getCloudfrontSignedUrl throws', () => {
        const awsS3Module = require('../../modules/awsS3');
        const spy = jest.spyOn(awsS3Module, 'getCloudfrontSignedUrl').mockImplementation(() => { throw new Error('boom'); });
        const res = awsS3Module.processBatchItem({ bucketName: 'b', key: 'k.jpg', region: 'us-east-1' });
        expect(res.signedUrl).toContain('https://');
        spy.mockRestore();
    });

    it('module bootstrap catch path logs when refreshDistributionCache fails', async () => {
        jest.resetModules();
        await jest.isolateModulesAsync(async () => {
            mockCloudFrontClient.send.mockRejectedValueOnce(new Error('seed-failure'));
            const logSpy = jest.spyOn(console, 'error').mockImplementation(() => { });
            require('../../modules/awsS3');
            await new Promise(r => setTimeout(r, 0));
            expect(logSpy).toHaveBeenCalled();
            logSpy.mockRestore();
        });
    });

    it('starts monitoring interval when not in test env', async () => {
        jest.resetModules();
        await jest.isolateModulesAsync(async () => {
            const prevEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'production';
            const setIntervalSpy = jest.spyOn(global as any, 'setInterval').mockImplementation((..._args: any[]) => 0 as any);
            require('../../modules/awsS3');
            expect(setIntervalSpy).toHaveBeenCalled();
            setIntervalSpy.mockRestore();
            process.env.NODE_ENV = prevEnv;
        });
    });

    it('buildThumbnailImage updates DB when ETag present', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Body: Buffer.from('orig') }) });
        mockS3.putObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ ETag: 'etag' }) });
        const updateMany = (jest.fn() as any).mockResolvedValue({});
        (mockDb.qmai.collection as any).mockReturnValue({ updateMany });
        const out = await awsS3Module.buildThumbnailImage('bucket', 'us-east-1', 'dir/file.jpg', 'unit');
        expect(updateMany).toHaveBeenCalled();
        expect(out?.ContentType).toBe('image/jpeg');
    });

    it('deleteFileFromS3 logs and does not throw when delete fails', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const err = new Error('del-failed');
        mockS3.deleteObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockRejectedValue(err) });
        const warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => { });
        await awsS3Module.deleteFileFromS3({ name: 'b', region: 'us-east-1' }, 'k');
        expect(warnSpy).toHaveBeenCalled();
        warnSpy.mockRestore();
    });

    it('checkKeyExists rejects on non-404 error', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValueOnce({ promise: (jest.fn() as any).mockRejectedValue({ statusCode: 500 }) });
        await expect(awsS3Module.checkKeyExists('b', 'k2')).rejects.toBeDefined();
    });

    it('uploadFileToS3 handles file without originalname (uses dat extension)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const fileWithoutOriginalname = { buffer: Buffer.from('test') };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path';

        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Key: 'test/path/123-abc.dat' }) });

        const result = await awsS3Module.uploadFileToS3(fileWithoutOriginalname, bucketType, filePath);
        expect(result).toBe('test/path/123-abc.dat');
        expect(mockS3.upload).toHaveBeenCalledWith(expect.objectContaining({
            Key: expect.stringMatching(/test\/path\/\d+-[a-z0-9]+\.dat$/)
        }));
    });

    it('uploadFileToS3 handles file with existing extension in path', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const file = { buffer: Buffer.from('test'), originalname: 'test.jpg' };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path.jpg';

        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Key: 'test/path.jpg' }) });

        const result = await awsS3Module.uploadFileToS3(file, bucketType, filePath);
        expect(result).toBe('test/path.jpg');
        expect(mockS3.upload).toHaveBeenCalledWith(expect.objectContaining({
            Key: 'test/path.jpg'
        }));
    });

    it('getObjectStream handles error in getS3Object and re-throws', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const originalGetS3Object = awsS3Module.getS3Object;
        awsS3Module.getS3Object = (jest.fn() as any).mockResolvedValue({});
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

        await expect(awsS3Module.getObjectStream('bucket', 'region', 'key')).rejects.toThrow('Expected AWS Request object with createReadStream method');
        expect(consoleSpy).toHaveBeenCalledWith('Error getting object from S3:', expect.any(Error));

        awsS3Module.getS3Object = originalGetS3Object;
        consoleSpy.mockRestore();
    });

    it('checkKeyExists handles bucket not in cache and adds key after successful headObject', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({}) });
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => { });

        const result = await awsS3Module.checkKeyExists('new-bucket', 'new-key');
        expect(result).toBe(true);
        expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: 'new-bucket', Key: 'new-key' });
        expect(consoleSpy).toHaveBeenCalledWith(expect.stringMatching(/added key new-key to bucket new-bucket/));

        consoleSpy.mockRestore();
    });

    it('checkKeyExists initializes empty bucket array when bucket not in cache', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.headObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({}) });

        const result = await awsS3Module.checkKeyExists('brand-new-bucket', 'key');
        expect(result).toBe(true);
        expect(mockS3.headObject).toHaveBeenCalledWith({ Bucket: 'brand-new-bucket', Key: 'key' });
    });

    it('buildThumbnailImage handles successful thumbnail creation with database update', async () => {
        const awsS3Module = require('../../modules/awsS3');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Body: Buffer.from('original image') }) });
        mockS3.putObject.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ ETag: '"valid-etag"' }) });

        const result = await awsS3Module.buildThumbnailImage('bucket', 'us-east-1', 'dir/file.jpg', 'unit');
        expect(result).toBeDefined();
        expect(result.createReadStream).toBeDefined();
        expect(result.ContentLength).toBeDefined();
        expect(result.ContentType).toBe('image/jpeg');
        expect(mockDb.qmai.collection().updateMany).toHaveBeenCalledWith(
            { image_path: 'dir/file.jpg' },
            { $set: { thumbnail_image_path: 'images/unit/file.jpg' } }
        );
    });

    it('uploadFileToS3 handles file with mimetype', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const file = { buffer: Buffer.from('test'), mimetype: 'image/jpeg' };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path.jpg';

        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Key: 'test/path.jpg' }) });

        const result = await awsS3Module.uploadFileToS3(file, bucketType, filePath);
        expect(result).toBe('test/path.jpg');
        expect(mockS3.upload).toHaveBeenCalledWith(expect.objectContaining({
            ContentType: 'image/jpeg'
        }));
    });

    it('uploadFileToS3 handles file without mimetype (uses default)', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const file = { buffer: Buffer.from('test') };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path.jpg';

        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Key: 'test/path.jpg' }) });

        const result = await awsS3Module.uploadFileToS3(file, bucketType, filePath);
        expect(result).toBe('test/path.jpg');
        expect(mockS3.upload).toHaveBeenCalledWith(expect.objectContaining({
            ContentType: 'application/octet-stream'
        }));
    });

    it('uploadFileToS3 handles additional options parameter', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const file = { buffer: Buffer.from('test') };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path.jpg';
        const options = { ServerSideEncryption: 'AES256' };

        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockResolvedValue({ Key: 'test/path.jpg' }) });

        const result = await awsS3Module.uploadFileToS3(file, bucketType, filePath, options);
        expect(result).toBe('test/path.jpg');
        expect(mockS3.upload).toHaveBeenCalledWith(expect.objectContaining({
            ServerSideEncryption: 'AES256'
        }));
    });

    it('uploadFileToS3 handles upload error with proper error message', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const file = { buffer: Buffer.from('test') };
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const filePath = 'test/path.jpg';

        const uploadError = new Error('Upload failed');
        mockS3.upload.mockReturnValue({ promise: (jest.fn() as any).mockRejectedValue(uploadError) });

        await expect(awsS3Module.uploadFileToS3(file, bucketType, filePath)).rejects.toThrow('Failed to upload file to S3: Upload failed');
    });

    it('deleteFileFromS3 handles delete error and logs it but does not throw', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const bucketType = { name: 'test-bucket', region: 'us-east-1' };
        const key = 'test-key';

        const deleteError = new Error('Delete failed');
        mockS3.deleteObject.mockReturnValue({ promise: (jest.fn() as any).mockRejectedValue(deleteError) });
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });
        const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => { });

        await awsS3Module.deleteFileFromS3(bucketType, key);

        expect(consoleErrorSpy).toHaveBeenCalledWith('[awsS3.deleteFileFromS3] Error:', deleteError);
        expect(consoleWarnSpy).toHaveBeenCalledWith(`[awsS3.deleteFileFromS3] Failed to delete file ${key}, but continuing with database update`);

        consoleErrorSpy.mockRestore();
        consoleWarnSpy.mockRestore();
    });

    it('getS3Object handles getObject error with proper error message', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const bucket = 'test-bucket';
        const region = 'us-east-1';
        const key = 'test-key';

        const getError = new Error('Get failed');
        mockS3.getObject.mockReturnValue({ promise: (jest.fn() as any).mockRejectedValue(getError) });

        await expect(awsS3Module.getS3Object(bucket, region, key, true)).rejects.toThrow('Get failed');
    });

    it('generateS3FallbackUrl handles missing file extension', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const bucketName = 'test-bucket';
        const key = 'test-file';
        const region = 'us-east-1';

        const result = awsS3Module.generateS3FallbackUrl(bucketName, key, region);
        expect(result).toBe('https://signed-url');
    });

    it('generateS3FallbackUrl handles file with extension', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const bucketName = 'test-bucket';
        const key = 'test-file.jpg';
        const region = 'us-east-1';

        const result = awsS3Module.generateS3FallbackUrl(bucketName, key, region);
        expect(result).toBe('https://signed-url');
    });

    it('getCloudfrontSignedUrl handles missing fileName parameter', async () => {
        const awsS3Module = require('../../modules/awsS3');

        expect(() => awsS3Module.getCloudfrontSignedUrl({})).toThrow('fileName is required');
    });

    it('getCloudfrontSignedUrl handles missing bucketName parameter', async () => {
        const awsS3Module = require('../../modules/awsS3');

        expect(() => awsS3Module.getCloudfrontSignedUrl({ fileName: 'test.jpg' })).toThrow('bucketName is required');
    });

    it('getCloudfrontSignedUrl handles missing distribution in cache', async () => {
        const awsS3Module = require('../../modules/awsS3');

        const result = awsS3Module.getCloudfrontSignedUrl({ fileName: 'test.jpg', bucketName: 'non-existent-bucket' });
        expect(result).toBeNull();
    });

    it('processBatchItem handles error in getCloudfrontSignedUrl and returns fallback', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const originalGetCloudfrontSignedUrl = awsS3Module.getCloudfrontSignedUrl;
        awsS3Module.getCloudfrontSignedUrl = jest.fn().mockImplementation(() => {
            throw new Error('CloudFront error');
        });

        const result = awsS3Module.processBatchItem({ bucketName: 'test-bucket', key: 'test.jpg', region: 'us-east-1' });
        expect(result.signedUrl).toBeDefined();
        expect(result.signedUrl).toBe('https://signed-url');

        awsS3Module.getCloudfrontSignedUrl = originalGetCloudfrontSignedUrl;
    });

    it('processBatchItem handles CloudFront returning null and uses fallback', async () => {
        const awsS3Module = require('../../modules/awsS3');
        const originalGetCloudfrontSignedUrl = awsS3Module.getCloudfrontSignedUrl;
        awsS3Module.getCloudfrontSignedUrl = jest.fn().mockReturnValue(null);

        const result = awsS3Module.processBatchItem({ bucketName: 'test-bucket', key: 'test.jpg', region: 'us-east-1' });
        expect(result.signedUrl).toBeDefined();
        expect(result.signedUrl).toBe('https://signed-url');

        awsS3Module.getCloudfrontSignedUrl = originalGetCloudfrontSignedUrl;
    });


});