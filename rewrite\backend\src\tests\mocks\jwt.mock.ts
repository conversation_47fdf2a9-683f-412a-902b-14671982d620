import { jest } from '@jest/globals';

const sign = jest.fn().mockImplementation((payload: any) => {
    const tokenData = JSON.stringify(payload);
    return `mock-token-${Buffer.from(tokenData).toString('base64')}`;
});

const verify = jest.fn();

class JsonWebTokenError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'JsonWebTokenError';
    }
}

const jwtMock = { sign, verify, JsonWebTokenError };

export { sign, verify, JsonWebTokenError };
export default jwtMock;
