import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('NotificationSummary Model', () => {
    let mockIoEmitter: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockIoEmitter = testSetup.mockIoEmitter;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create model with vessel_ids validator and timestamp defaults', async () => {
        jest.doMock('../../modules/db', () => mockDb);
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);

        delete require.cache[require.resolve('../../models/NotificationSummary')];

        const NotificationSummaryModule = await import('../../models/NotificationSummary');
        const _NotificationSummary = NotificationSummaryModule.default;

        const isValidObjectId = (id: string) => /^[0-9a-fA-F]{24}$/.test(id);
        const validator = (arr: (string | any)[]) => {
            return arr.every((item: string | any) => item === "all" || isValidObjectId(item));
        };

        expect(validator(['all'])).toBe(true);
        expect(validator(['507f1f77bcf86cd799439011'])).toBe(true);
        expect(validator(['all', '507f1f77bcf86cd799439011'])).toBe(true);
        expect(validator(['invalid-id'])).toBe(false);

        const defaultTimestampFunction = () => new Date().toISOString();
        const createdAtTimestamp = defaultTimestampFunction();
        expect(typeof createdAtTimestamp).toBe('string');
        expect(createdAtTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const updatedAtTimestamp = defaultTimestampFunction();
        expect(typeof updatedAtTimestamp).toBe('string');
        expect(updatedAtTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});