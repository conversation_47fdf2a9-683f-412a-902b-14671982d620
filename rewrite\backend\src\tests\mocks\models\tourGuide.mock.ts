import { jest } from '@jest/globals';

const tourGuideMock = {
    find: jest.fn().mockReturnThis(),
    findOne: jest.fn().mockReturnThis(),
    findOneAndUpdate: jest.fn().mockReturnThis(),
    create: jest.fn().mockReturnThis(),
    exec: jest.fn().mockResolvedValue([] as never),
    lean: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    populate: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    aggregate: jest.fn().mockResolvedValue([] as never),
    countDocuments: jest.fn().mockResolvedValue(0 as never),
    deleteOne: jest.fn().mockResolvedValue({ deletedCount: 0 } as never),
    deleteMany: jest.fn().mockResolvedValue({ deletedCount: 0 } as never),
    findById: jest.fn().mockReturnThis(),
    findByIdAndUpdate: jest.fn().mockReturnThis(),
    findByIdAndDelete: jest.fn().mockReturnThis(),
    updateOne: jest.fn().mockResolvedValue({ modifiedCount: 0 } as never),
    updateMany: jest.fn().mockResolvedValue({ modifiedCount: 0 } as never),
    save: jest.fn().mockResolvedValue({} as never),
    toObject: jest.fn().mockReturnValue({}),
    toJSON: jest.fn().mockReturnValue({}),
};

export default tourGuideMock;
