import { jest } from '@jest/globals';

const vesselMock = {
    find: jest.fn().mockReturnThis(),
    findOne: jest.fn().mockReturnThis(),
    findById: jest.fn().mockReturnThis(),
    findByIdAndUpdate: jest.fn().mockReturnThis(),
    findByIdAndDelete: jest.fn().mockReturnThis(),
    create: jest.fn().mockReturnThis(),
    aggregate: jest.fn().mockResolvedValue([] as never),
    countDocuments: jest.fn().mockResolvedValue(0 as never),
    deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 } as never),
    deleteMany: jest.fn().mockResolvedValue({ deletedCount: 1 } as never),
    updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 } as never),
    updateMany: jest.fn().mockResolvedValue({ modifiedCount: 1 } as never),
    save: jest.fn().mockResolvedValue({} as never),
    toObject: jest.fn().mockReturnValue({}),
    toJSON: jest.fn().mockReturnValue({}),
};

export default vesselMock;