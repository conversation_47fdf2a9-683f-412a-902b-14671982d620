import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { vesselsList, locationsList, lastKnownLocation, closestLocation } from '../data/VesselLocation';
import { canAccessVessel, splitByMonthsUTC, validateError, getLocationsCollections } from '../mocks/utils/functions.mock';
import vesselService from '../../services/Vessel.service';
import vesselLocationService from '../../services/VesselLocation.service';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../services/VesselLocation.service', () => require('../mocks/services/vesselLocation.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Vessel Locations API', () => {
    describe('POST /api/vesselLocations/:vesselName', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });

                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/vesselLocations/prototype-37');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if endTimestamp provided without startTimestamp', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: 1727222400000 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('startTimestamp is required when endTimestamp is provided');
                });

                it('should return 404 if vessel does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(null);

                    const res = await request(app)
                        .post('/api/vesselLocations/nonexistent-vessel')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Vessel does not exist');
                });

                it('should return 403 if user cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe("Cannot access coordinates for 'prototype-37'");
                });

                it('should return last known location when lastKnown is 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce(lastKnownLocation);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 1, excludeIds: ['507f1f77bcf86cd799439011'] });

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(lastKnownLocation);
                });

                it('should return last known location when lastKnown is 1 and no location found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce(null);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 1, excludeIds: ['507f1f77bcf86cd799439011'] });

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(null);
                });

                it('should return 500 if error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockRejectedValueOnce(new Error('Error'));
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce(null);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 1 });

                    expect(res.status).toBe(500);
                    // expect(res.body).toEqual(null);
                });

                it('should return 400 if no location collections found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (getLocationsCollections as jest.Mock).mockReturnValueOnce([]);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: 1727136000000, endTimestamp: 1727222400000 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('No location collections found for the specified time range');
                });

                it('should return locations with timestamp range', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (getLocationsCollections as jest.Mock).mockReturnValueOnce([{ aggregate: jest.fn().mockReturnValue({ toArray: jest.fn().mockResolvedValue([] as never) }) }]);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: 1727136000000, excludeIds: ['507f1f77bcf86cd799439011'] });

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should return 400 for invalid lastKnown value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 2, excludeIds: 'incorrect-id' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid startTimestamp', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid endTimestamp', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should handle swagger request with limit in callback', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findByAssignedUnitId.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const mockCollection = {
                        aggregate: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnThis(),
                            toArray: jest.fn().mockResolvedValue([] as never)
                        })
                    };
                    (getLocationsCollections as jest.Mock).mockReturnValueOnce([mockCollection]);

                    const res = await request(app)
                        .post('/api/vesselLocations/prototype-37')
                        .set('Authorization', authToken)
                        .set('Referer', 'http://localhost:3000/docs')
                        .send({ startTimestamp: 1727136000000 });

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselLocations/bulk', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (splitByMonthsUTC as jest.Mock).mockReturnValueOnce([{ start: new Date('2024-09-01'), end: new Date('2024-09-30') }]);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/vesselLocations/bulk');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if vesselIds is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds contains invalid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 if endTimestampISO is not provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce(vesselsList);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (vesselLocationService as any).findByDateRange.mockResolvedValueOnce(locationsList);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&startTimestampISO=2024-09-15T22:30:20.555Z')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should return 400 if endTimestampISO provided without startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce(vesselsList);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&endTimestampISO=2024-09-15T22:30:20.555Z')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('startTimestampISO is required when endTimestamp is provided');
                });

                it('should return 400 for invalid startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&startTimestampISO=invalid')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid endTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&endTimestampISO=invalid')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return bulk locations for valid vesselIds', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce(vesselsList);
                    (vesselLocationService as any).findByDateRange.mockResolvedValueOnce(locationsList);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011,507f1f77bcf86cd799439013')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should return bulk locations with timestamp range', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce(vesselsList);
                    (vesselLocationService as any).findByDateRange.mockResolvedValueOnce(locationsList);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&startTimestampISO=2024-09-15T22:29:20.555Z&endTimestampISO=2024-09-15T22:30:20.555Z')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should return 500 if error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockRejectedValueOnce(new Error('Error'));
                    (vesselLocationService as any).findByDateRange.mockResolvedValueOnce(locationsList);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/bulk?vesselIds=507f1f77bcf86cd799439011&startTimestampISO=2024-09-15T22:29:20.555Z&endTimestampISO=2024-09-15T22:30:20.555Z')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselLocations/latest', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/vesselLocations/latest');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if vesselIds is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/latest')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/latest?vesselIds=')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds contains invalid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselLocations/latest?vesselIds=invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return latest locations for valid vesselIds', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce(vesselsList);
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce([lastKnownLocation]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/latest?vesselIds=507f1f77bcf86cd799439011,507f1f77bcf86cd799439013')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should handle latest request with no vessels', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockResolvedValueOnce([]);
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce([]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselLocations/latest?vesselIds=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeDefined();
                });

                it('should return 500 if error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).find.mockRejectedValueOnce(new Error('Error'));
                    (vesselLocationService as any).findLastKnownLocation.mockResolvedValueOnce([]);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });

                    const res = await request(app)
                        .get('/api/vesselLocations/latest?vesselIds=507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/vesselLocations/:vesselId/closest', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/vesselLocations/507f1f77bcf86cd799439011/closest');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if timestampISO is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid timestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 404 if vessel does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findById.mockResolvedValueOnce(null);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439999/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: '2024-09-15T22:29:20.555Z' });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Vessel does not exist');
                });

                it('should return 403 if user cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findById.mockResolvedValueOnce(vesselsList[0]);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: '2024-09-15T22:29:20.555Z' });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Cannot access coordinates for this vessel');
                });

                it('should return 404 if no closest location found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findById.mockResolvedValueOnce(vesselsList[0]);
                    (vesselLocationService as any).findClosestLocation.mockResolvedValueOnce(null);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: '2024-09-15T22:29:20.555Z' });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('No coordinate found');
                });

                it('should return closest location for valid request', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findById.mockResolvedValueOnce(vesselsList[0]);
                    (vesselLocationService as any).findClosestLocation.mockResolvedValueOnce(closestLocation);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: '2024-09-15T22:29:20.555Z' });

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(closestLocation);
                });

                it('should return 500 if error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService as any).findById.mockRejectedValueOnce(new Error('Error'));
                    (vesselLocationService as any).findClosestLocation.mockResolvedValueOnce(null);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });

                    const res = await request(app)
                        .post('/api/vesselLocations/507f1f77bcf86cd799439011/closest')
                        .set('Authorization', authToken)
                        .send({ timestampISO: '2024-09-15T22:29:20.555Z' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});