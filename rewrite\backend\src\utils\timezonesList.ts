const defaultDateTimeFormat = "MM/DD/YYYY h:mm:ss A";

const timezonesList = [
    { name: "GMT-12:00", offset: "-12:00", representative: "Etc/GMT-12" },
    { name: "GMT-11:00", offset: "-11:00", representative: "Pacific/Pago_Pago" },
    { name: "GMT-10:00", offset: "-10:00", representative: "Pacific/Honolulu" },
    { name: "GMT-09:00", offset: "-09:00", representative: "America/Anchorage" },
    { name: "GMT-08:00", offset: "-08:00", representative: "America/Los_Angeles" },
    { name: "GMT-07:00", offset: "-07:00", representative: "America/Denver" },
    { name: "GMT-06:00", offset: "-06:00", representative: "America/Costa_Rica" },
    { name: "GMT-05:00", offset: "-05:00", representative: "America/New_York" },
    { name: "GMT-04:00", offset: "-04:00", representative: "America/Santiago" },
    { name: "GMT-03:00", offset: "-03:00", representative: "America/Sao_Paulo" },
    { name: "GMT-02:00", offset: "-02:00", representative: "Atlantic/South_Georgia" },
    { name: "GMT-01:00", offset: "-01:00", representative: "Atlantic/Azores" },
    { name: "GMT+00:00", offset: "+00:00", representative: "UTC" },
    { name: "GMT+01:00", offset: "+01:00", representative: "Europe/Berlin" },
    { name: "GMT+02:00", offset: "+02:00", representative: "Africa/Cairo" },
    { name: "GMT+03:00", offset: "+03:00", representative: "Europe/Moscow" },
    { name: "GMT+04:00", offset: "+04:00", representative: "Asia/Dubai" },
    { name: "GMT+05:00", offset: "+05:00", representative: "Asia/Karachi" },
    { name: "GMT+05:30", offset: "+05:30", representative: "Asia/Kolkata" },
    { name: "GMT+06:00", offset: "+06:00", representative: "Asia/Dhaka" },
    { name: "GMT+07:00", offset: "+07:00", representative: "Asia/Bangkok" },
    { name: "GMT+08:00", offset: "+08:00", representative: "Asia/Shanghai" },
    { name: "GMT+09:00", offset: "+09:00", representative: "Asia/Tokyo" },
    { name: "GMT+10:00", offset: "+10:00", representative: "Australia/Sydney" },
    { name: "GMT+11:00", offset: "+11:00", representative: "Pacific/Noumea" },
    { name: "GMT+12:00", offset: "+12:00", representative: "Pacific/Auckland" },
];

const isValidTimezoneOffset = (timezone: string) => {
    return timezonesList.some((tz) => tz.offset === timezone);
};

export { timezonesList, isValidTimezoneOffset, defaultDateTimeFormat };
