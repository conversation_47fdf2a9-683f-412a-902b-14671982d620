import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { s3TestData, batchTestData, cloudfrontTestData, cloudfrontBatchTestData } from '../data/S3';
import { validateError } from '../mocks/utils/functions.mock';
import { s3, getCloudfrontSignedUrl, generateS3FallbackUrl, processBatchItem, s3Config } from '../mocks/modules/awsS3.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';
process.env.CLOUDFRONT_KEY_PAIR_ID = 'test-key-pair-id';
process.env.CLOUDFRONT_PRIVATE_KEY = 'test-private-key';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('S3 API', () => {
    describe('GET /api/s3/signedUrl', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/s3/signedUrl');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if bucket_name is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/signedUrl?key=test&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if key is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/signedUrl?bucket_name=test&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/signedUrl?bucket_name=test&key=test')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and generate signed URL', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl.mockReturnValueOnce(s3TestData.signedUrl);

                    const res = await request(app)
                        .get(`/api/s3/signedUrl?bucket_name=${s3TestData.bucketName}&key=${s3TestData.key}&region=${s3TestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(s3TestData.signedUrl);
                });

                it('should return 500 if S3 error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl.mockImplementationOnce(() => { throw new Error('S3 error'); });

                    const res = await request(app)
                        .get(`/api/s3/signedUrl?bucket_name=${s3TestData.bucketName}&key=${s3TestData.key}&region=${s3TestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle file without extension', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl.mockReturnValueOnce(s3TestData.signedUrl);

                    const res = await request(app)
                        .get('/api/s3/signedUrl?bucket_name=test&key=fileWithoutExtension&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(s3TestData.signedUrl);
                });

                it('should handle file with empty extension', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl.mockReturnValueOnce(s3TestData.signedUrl);

                    const res = await request(app)
                        .get('/api/s3/signedUrl?bucket_name=test&key=file.&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(s3TestData.signedUrl);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/s3/signedUrl/batch', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/s3/signedUrl/batch');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if batch is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch is not an array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: 'not-an-array' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch item missing bucket_name', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({
                            batch: [{ key: 'test', region: 'us-east-1' }]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch item missing key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({
                            batch: [{ bucket_name: 'test', region: 'us-east-1' }]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch item missing region', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({
                            batch: [{ bucket_name: 'test', key: 'test' }]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and generate batch signed URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-1/test-file-1.jpg')
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-2/test-file-2.png');

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: batchTestData });

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrls).toHaveLength(2);
                    expect(res.body.signedUrls[0]).toHaveProperty('Bucket');
                    expect(res.body.signedUrls[0]).toHaveProperty('Key');
                    expect(res.body.signedUrls[0]).toHaveProperty('signedUrl');
                });

                it('should return 500 if S3 error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl.mockImplementationOnce(() => { throw new Error('S3 error'); });

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: batchTestData });

                    expect(res.status).toBe(500);
                });

                it('should handle batch with files without extensions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-1/file1')
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-2/file2');

                    const batchWithoutExtensions = [
                        { bucket_name: 'test-bucket-1', key: 'file1', region: 'us-east-1' },
                        { bucket_name: 'test-bucket-2', key: 'file2', region: 'us-west-2' }
                    ];

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: batchWithoutExtensions });

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrls).toHaveLength(2);
                });

                it('should handle batch with files with empty extensions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    s3.getSignedUrl
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-1/file1')
                        .mockReturnValueOnce('https://s3.amazonaws.com/test-bucket-2/file2');

                    const batchWithEmptyExtensions = [
                        { bucket_name: 'test-bucket-1', key: 'file1.', region: 'us-east-1' },
                        { bucket_name: 'test-bucket-2', key: 'file2.', region: 'us-west-2' }
                    ];

                    const res = await request(app)
                        .post('/api/s3/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: batchWithEmptyExtensions });

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrls).toHaveLength(2);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/s3/cloudfront/signedUrl', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/s3/cloudfront/signedUrl');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if key is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?bucketName=test&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if bucketName is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test&bucketName=test')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 500 if CloudFront configuration is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const originalKeyPairId = process.env.CLOUDFRONT_KEY_PAIR_ID;
                    const originalPrivateKey = process.env.CLOUDFRONT_PRIVATE_KEY;
                    
                    delete process.env.CLOUDFRONT_KEY_PAIR_ID;
                    delete process.env.CLOUDFRONT_PRIVATE_KEY;

                    const res = await request(app)
                        .get(`/api/s3/cloudfront/signedUrl?key=${cloudfrontTestData.key}&bucketName=${cloudfrontTestData.bucketName}&region=${cloudfrontTestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('CloudFront configuration missing');

                    process.env.CLOUDFRONT_KEY_PAIR_ID = originalKeyPairId;
                    process.env.CLOUDFRONT_PRIVATE_KEY = originalPrivateKey;
                });

                it('should return 200 and generate CloudFront signed URL', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const res = await request(app)
                        .get(`/api/s3/cloudfront/signedUrl?key=${cloudfrontTestData.key}&bucketName=${cloudfrontTestData.bucketName}&region=${cloudfrontTestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);
                });

                it('should return 200 with fallback URL when CloudFront returns null', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(null);
                    generateS3FallbackUrl.mockReturnValueOnce('https://s3.amazonaws.com/fallback-url');

                    const res = await request(app)
                        .get(`/api/s3/cloudfront/signedUrl?key=${cloudfrontTestData.key}&bucketName=${cloudfrontTestData.bucketName}&region=${cloudfrontTestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe('https://s3.amazonaws.com/fallback-url');
                });

                it('should return 500 if CloudFront error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockImplementationOnce(() => { throw new Error('CloudFront error'); });

                    const res = await request(app)
                        .get(`/api/s3/cloudfront/signedUrl?key=${cloudfrontTestData.key}&bucketName=${cloudfrontTestData.bucketName}&region=${cloudfrontTestData.region}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle assets bucket name', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test-file.jpg&bucketName=test-assets-bucket&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);
                });

                it('should handle compressed items bucket name', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test-file.jpg&bucketName=test-compressed-bucket&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);
                });

                it('should handle other bucket names', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test-file.jpg&bucketName=other-bucket&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);
                });

                it('should handle assets bucket name with null s3Config values', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const originalAssetsName = s3Config.buckets.assets.name;
                    
                    s3Config.buckets.assets.name = null as any;
                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test-file.jpg&bucketName=null-bucket&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);

                    s3Config.buckets.assets.name = originalAssetsName;
                });

                it('should handle compressed bucket name with null s3Config values', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    getCloudfrontSignedUrl.mockReturnValueOnce(cloudfrontTestData.cloudfrontUrl);

                    const originalCompressedName = s3Config.buckets.compressedItems.name;
                    
                    s3Config.buckets.compressedItems.name = null as any;

                    const res = await request(app)
                        .get('/api/s3/cloudfront/signedUrl?key=test-file.jpg&bucketName=null-bucket&region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrl).toBe(cloudfrontTestData.cloudfrontUrl);

                    s3Config.buckets.compressedItems.name = originalCompressedName;
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/s3/cloudfront/signedUrl/batch', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/s3/cloudfront/signedUrl/batch');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if batch is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch is not an array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: 'not-an-array' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch item missing bucketName', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({
                            batch: [{ key: 'test', region: 'us-east-1' }]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if batch item missing key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({
                            batch: [{ bucketName: 'test', region: 'us-east-1' }]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 500 if CloudFront configuration is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const originalKeyPairId = process.env.CLOUDFRONT_KEY_PAIR_ID;
                    const originalPrivateKey = process.env.CLOUDFRONT_PRIVATE_KEY;
                    
                    delete process.env.CLOUDFRONT_KEY_PAIR_ID;
                    delete process.env.CLOUDFRONT_PRIVATE_KEY;

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: cloudfrontBatchTestData });

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('CloudFront configuration missing');

                    process.env.CLOUDFRONT_KEY_PAIR_ID = originalKeyPairId;
                    process.env.CLOUDFRONT_PRIVATE_KEY = originalPrivateKey;
                });

                it('should return 200 and generate batch CloudFront signed URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    processBatchItem
                        .mockReturnValueOnce('https://cloudfront.net/test-file-1.jpg')
                        .mockReturnValueOnce('https://cloudfront.net/test-file-2.png');

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: cloudfrontBatchTestData });

                    expect(res.status).toBe(200);
                    expect(res.body.signedUrls).toHaveLength(2);
                    expect(res.body.signedUrls[0]).toBe('https://cloudfront.net/test-file-1.jpg');
                    expect(res.body.signedUrls[1]).toBe('https://cloudfront.net/test-file-2.png');
                });

                it('should return 500 if CloudFront batch error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    processBatchItem.mockImplementationOnce(() => { throw new Error('CloudFront batch error'); });

                    const res = await request(app)
                        .post('/api/s3/cloudfront/signedUrl/batch')
                        .set('Authorization', authToken)
                        .send({ batch: cloudfrontBatchTestData });

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});