import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import EmailDomains from '../../models/EmailDomains';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/EmailDomains', () => require('../mocks/models/emailDomains.mock'));

describe('Email Domains API', () => {
    describe('GET /api/emailDomains', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/emailsDomain');
                    expect(res.status).toBe(401);
                });


                it('should return 200 with email domains for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as any).mockResolvedValueOnce([
                        { _id: '507f1f77bcf86cd799439011', domain: 'example.com' },
                        { _id: '507f1f77bcf86cd799439012', domain: 'test.com' }
                    ]);

                    const res = await request(app)
                        .get('/api/emailsDomain')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                    expect(res.body).toHaveLength(2);
                    expect(res.body[0]).toHaveProperty('domain', 'example.com');
                });

                it('should return 200 with empty array when no email domains found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as any).mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/emailsDomain')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                    expect(res.body).toHaveLength(0);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as any).mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/emailsDomain')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
