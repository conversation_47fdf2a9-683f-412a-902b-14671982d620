import mongoose from "mongoose";
import ioEmitter from "../modules/ioEmitter";
import db from "../modules/db";
import User from "./User";
import { IUnitsHistory, IVessel } from "src/interfaces/Vessel";

const unitsHistorySchema = new mongoose.Schema<IUnitsHistory>(
    {
        unit_id: { type: String, required: true },
        mount_timestamp: { type: Date, required: true },
        unmount_timestamp: { type: Date, required: false },
    },
    { _id: false },
);

const vesselSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    thumbnail_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    thumbnail_compressed_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    unit_id: {
        type: String,
        required: false,
        unique: true,
        sparse: true,
    },
    region_group_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    // home_port_location: {
    //     type: {
    //         type: String,
    //         enum: ['Point'],
    //         required: false,
    //     },
    //     coordinates: {
    //         type: [Number],
    //         required: false,
    //     },
    //     // default: null,
    // },
    home_port_location: {
        type: Object,
        required: true,
    },
    country_iso_code: {
        type: String,
        required: true,
        uppercase: true,
    },
    is_active: {
        type: Boolean,
        required: true,
        default: true,
    },
    units_history: {
        type: [unitsHistorySchema],
        required: true,
        default: [],
    },
    creation_timestamp: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: User,
        required: true,
    },
});

vesselSchema.index({ unit_id: 1 });
vesselSchema.index({ home_port_location: "2dsphere" });

vesselSchema.pre("save", function (next) {
    if (this.isNew) {
        // For new vessels, only add to history if unit_id is provided
        if (this.unit_id) {
            this.units_history.splice(0, this.units_history.length); // clear it
            this.units_history.push({
                unit_id: this.unit_id,
                mount_timestamp: this.creation_timestamp || new Date(),
                unmount_timestamp: null,
            });
        } else {
            this.units_history.splice(0, this.units_history.length);
        }
    } else if (this.isModified("unit_id")) {
        // If unit_id is being changed, update the history
        const now = new Date();

        // Close the previous unit's history if it exists
        if (this.units_history && this.units_history.length > 0) {
            const lastEntry = this.units_history[this.units_history.length - 1];
            if (!lastEntry) {
                throw new Error("Unexpected error: Last entry in units_history is undefined");
            }
            if (!lastEntry.unmount_timestamp) {
                lastEntry.unmount_timestamp = now;
            }
        }

        // Only add new unit to history if unit_id is not null/undefined
        if (this.unit_id) {
            this.units_history.push({
                unit_id: this.unit_id,
                mount_timestamp: now,
                unmount_timestamp: null,
            });
        }
    }
    next();
});

vesselSchema.post("save", (vessel) => {
    ioEmitter.emit("notifyAll", { name: `vessel/changed`, data: vessel.toObject() });
});
vesselSchema.post("findOneAndUpdate", (vessel) => {
    ioEmitter.emit("notifyAll", { name: `vessel/changed`, data: vessel.toObject() });
});
vesselSchema.post("findOneAndDelete", (vessel) => {
    ioEmitter.emit("notifyAll", { name: `vessel/changed`, data: vessel.toObject() });
});

// function emitChangedEvent(vessel) {
//     ioEmitter.emit("notifyAll", { name: `vessel/changed`, data: vessel.toObject() });
// }

const Vessel = db.qmShared.model<IVessel>("Vessel", vesselSchema, "vessels");

export default Vessel;
// module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
