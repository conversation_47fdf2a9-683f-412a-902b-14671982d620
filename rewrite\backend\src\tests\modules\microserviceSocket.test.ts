import { jest, describe, it, expect, beforeEach } from '@jest/globals';

jest.mock('socket.io-client', () => ({
    io: jest.fn(() => ({
        connect: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        once: jest.fn(),
        removeAllListeners: jest.fn(),
        connected: true,
        disconnected: false,
        id: 'mock-socket-id',
        io: {
            uri: 'http://localhost:3001',
            opts: {
                autoConnect: true,
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionAttempts: 5,
                timeout: 20000
            }
        }
    }))
}));

describe('Microservice Socket Module', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should export a socket.io instance with required methods', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;
        
        expect(microservice_socket).toBeDefined();
        expect(typeof microservice_socket.connect).toBe('function');
        expect(typeof microservice_socket.disconnect).toBe('function');
        expect(typeof microservice_socket.emit).toBe('function');
        expect(typeof microservice_socket.on).toBe('function');
        expect(typeof microservice_socket.off).toBe('function');
        expect(typeof microservice_socket.once).toBe('function');
        expect(typeof microservice_socket.removeAllListeners).toBe('function');
    });

    it('should have correct socket configuration', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;
        
        expect(microservice_socket.io).toBeDefined();
        expect(microservice_socket.io.opts.autoConnect).toBe(true);
        expect(microservice_socket.io.opts.reconnection).toBe(true);
        expect(microservice_socket.io.opts.reconnectionDelay).toBe(1000);
        expect(microservice_socket.io.opts.reconnectionAttempts).toBe(5);
        expect(microservice_socket.io.opts.timeout).toBe(20000);
    });

    it('should have socket connection state properties', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;
        
        expect(microservice_socket.connected).toBeDefined();
        expect(microservice_socket.disconnected).toBeDefined();
        expect(microservice_socket.id).toBeDefined();
    });

    it('should allow calling socket methods', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;
        
        expect(() => {
            microservice_socket.connect();
            microservice_socket.disconnect();
            microservice_socket.emit('test', 'data');
            microservice_socket.on('test', () => {});
            microservice_socket.off('test', () => {});
            microservice_socket.once('test', () => {});
            microservice_socket.removeAllListeners();
        }).not.toThrow();
    });
});