import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('Permission Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create Permission model with proper schema', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Permission')];

        const PermissionModule = await import('../../models/Permission');
        const Permission = PermissionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Permission', expect.any(Object));
        expect(Permission).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.permission_id).toBeDefined();
        expect(schemaArg.paths.permission_id.type).toBe(Number);
        expect(schemaArg.paths.permission_id.required).toBe(true);
        expect(schemaArg.paths.permission_id.unique).toBe(true);

        expect(schemaArg.paths.permission_name).toBeDefined();
        expect(schemaArg.paths.permission_name.type).toBe(String);
        expect(schemaArg.paths.permission_name.required).toBe(true);
        expect(schemaArg.paths.permission_name.unique).toBe(true);

        expect(schemaArg.paths.permission_description).toBeDefined();
        expect(schemaArg.paths.permission_description.type).toBe(String);
        expect(schemaArg.paths.permission_description.required).toBe(true);

        expect(schemaArg.paths.assignable).toBeDefined();
        expect(schemaArg.paths.assignable.type).toBe(Boolean);
        expect(schemaArg.paths.assignable.required).toBe(true);
        expect(schemaArg.paths.assignable.default).toBe(true);
    });
});
