import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('TourGuide Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create TourGuide model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/TourGuide')];

        const TourGuideModule = await import('../../models/TourGuide');
        const TourGuide = TourGuideModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('TourGuide', expect.any(Object));
        expect(TourGuide).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.user_id).toBeDefined();
        expect(schemaArg.paths.user_id.type).toBeDefined();
        expect(schemaArg.paths.user_id.required).toBe(true);

        expect(schemaArg.paths.maps).toBeDefined();
        expect(schemaArg.paths.maps.type).toBeDefined();
        expect(schemaArg.paths.maps.default).toBe(false);

        expect(schemaArg.paths.streams).toBeDefined();
        expect(schemaArg.paths.streams.type).toBeDefined();
        expect(schemaArg.paths.streams.default).toBe(false);

        expect(schemaArg.paths.events).toBeDefined();
        expect(schemaArg.paths.events.type).toBeDefined();
        expect(schemaArg.paths.events.default).toBe(false);

        expect(schemaArg.paths.notifications).toBeDefined();
        expect(schemaArg.paths.notifications.type).toBeDefined();
        expect(schemaArg.paths.notifications.default).toBe(false);
    });
});
