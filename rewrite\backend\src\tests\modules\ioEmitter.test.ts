import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import ioEmitter from '../../modules/ioEmitter';

describe('IO Emitter Module', () => {
    beforeEach(() => {
        jest.resetModules();
    });

    it('should register and emit events with payload', (done) => {
        const payload = { a: 1 };
        ioEmitter.once('test-event', (data: any) => {
            expect(data).toEqual(payload);
            done();
        });
        ioEmitter.emit('test-event', payload);
    });

    it('should support multiple listeners and listener removal', () => {
        const handlerA = jest.fn();
        const handlerB = jest.fn();
        ioEmitter.on('multi', handlerA);
        ioEmitter.on('multi', handlerB);

        ioEmitter.emit('multi', 42);
        expect(handlerA).toHaveBeenCalledWith(42);
        expect(handlerB).toHaveBeenCalledWith(42);

        ioEmitter.off('multi', handlerA);
        ioEmitter.emit('multi', 7);
        expect(handlerA).toHaveBeenCalledTimes(1);
        expect(handlerB).toHaveBeenCalledTimes(2);
    });
});