import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import mongoose from 'mongoose';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import ArtifactFavourite from '../../models/ArtifactFavourites';
import Vessel from '../../models/Vessel';
import { AuthRunTestsFunction } from '../type';
import { qmai } from '../mocks/modules/db.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/ArtifactFavourites', () => require('../mocks/models/artifactFavourites.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));

describe('Artifact Favourites API', () => {

	describe('POST /api/artifactFavourites', () => {

		const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
			const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
			const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
			const validArtifactId = new mongoose.Types.ObjectId().toString();

			describe(`${authMethod} authentication`, () => {

				beforeEach(() => {
					jest.resetAllMocks();
					qmai.collection.mockReturnValue({ findOne: (jest.fn() as any).mockResolvedValueOnce(null as any) });
				});

				it('should return 401 if no token is provided', async () => {
					const res = await request(app).post('/api/artifactFavourites').send({ artifact_id: validArtifactId });
					expect(res.status).toBe(401);
				});

				it('should return 400 if artifact_id is invalid or missing', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

					const res1 = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({});
					expect(res1.status).toBe(400);

					const res2 = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: '' });
					expect(res2.status).toBe(400);

					const res3 = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: 'not-an-objectid' });
					expect(res3.status).toBe(400);
				});

				it('should return 404 if the artifact is not found', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					qmai.collection.mockReturnValue({ findOne: (jest.fn() as any).mockResolvedValueOnce(null as any) });

					const res = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					expect(res.status).toBe(404);
				});


				it('should return 400 if the artifact is already in favourites', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					qmai.collection.mockReturnValue({ findOne: (jest.fn() as any).mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId(), onboard_vessel_id: new mongoose.Types.ObjectId() } as any) });
					(Vessel as any).findOne.mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId(), is_active: true });
					(ArtifactFavourite as any).findOne.mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId() });

					const res = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					expect(res.status).toBe(400);
				});

				it('should return 201 and add to favourites', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					qmai.collection.mockReturnValue({ findOne: (jest.fn() as any).mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId(), onboard_vessel_id: new mongoose.Types.ObjectId() } as any) });
					(Vessel as any).findOne.mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId(), is_active: true });
					(ArtifactFavourite as any).findOne.mockResolvedValueOnce(null);

					const res = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					if (authMethod === 'user') {
						expect(res.status).toBe(201);
					} else if (authMethod === 'api-key') {
						expect(res.status).toBe(403);
					}
				});

				it('should return 500 on unexpected error', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					qmai.collection.mockReturnValue({ findOne: (jest.fn() as any).mockRejectedValueOnce(new Error('DB error') as any) });

					const res = await request(app)
						.post('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					if (authMethod === 'user') {
						expect(res.status).toBe(500);
					} else if (authMethod === 'api-key') {
						expect(res.status).toBe(403);
					}
				});
			});
		};

		runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
		// runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
	});

	describe('GET /api/artifactFavourites', () => {

		const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
			const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
			const _authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

			describe(`${authMethod} authentication`, () => {

				beforeEach(() => {
					jest.resetAllMocks();
				});

				it('should return 401 if no token is provided', async () => {
					const res = await request(app).get('/api/artifactFavourites');
					expect(res.status).toBe(401);
				});
			});
		};

		runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
		// runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
	});

	describe('DELETE /api/artifactFavourites', () => {

		const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
			const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
			const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
			const validArtifactId = new mongoose.Types.ObjectId().toString();

			describe(`${authMethod} authentication`, () => {

				beforeEach(() => {
					jest.resetAllMocks();
				});

				it('should return 401 if no token is provided', async () => {
					const res = await request(app).delete('/api/artifactFavourites').send({ artifact_id: validArtifactId });
					expect(res.status).toBe(401);
				});

				it('should return 400 if artifact_id is invalid or missing', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

					const res1 = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({});
					expect(res1.status).toBe(400);

					const res2 = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: '' });
					expect(res2.status).toBe(400);

					const res3 = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: 'not-an-objectid' });
					expect(res3.status).toBe(400);
				});

				it('should return 404 if the artifact is not found in favourites', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					(ArtifactFavourite as any).findOneAndDelete.mockResolvedValueOnce(null);

					const res = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					expect(res.status).toBe(404);
				});

				it('should return 200 and remove from favourites', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					(ArtifactFavourite as any).findOneAndDelete.mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId() });

					const res = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					if (authMethod === 'user') {
						expect(res.status).toBe(200);
					} else if (authMethod === 'api-key') {
						expect(res.status).toBe(403);
					}
				});

				it('should return 500 on unexpected error', async () => {
					setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
					(ArtifactFavourite as any).findOneAndDelete.mockRejectedValueOnce(new Error('Database error'));

					const res = await request(app)
						.delete('/api/artifactFavourites')
						.set('Authorization', authToken)
						.send({ artifact_id: validArtifactId });

					if (authMethod === 'user') {
						expect(res.status).toBe(500);
					} else if (authMethod === 'api-key') {
						expect(res.status).toBe(403);
					}
				});
			});
		};

		runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
		// runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
	});
});
