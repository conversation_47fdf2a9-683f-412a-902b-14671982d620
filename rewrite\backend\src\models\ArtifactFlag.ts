import mongoose from "mongoose";
import db from "../modules/db";
import ioEmitter from "../modules/ioEmitter";
import { IArtifactFlag } from "src/interfaces/ArtifactFlag";

const artifactFlaggedSchema = new mongoose.Schema({
    artifactId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        index: true,
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});

artifactFlaggedSchema.index({ artifactId: 1, flaggedBy: 1 });
artifactFlaggedSchema.index({ flaggedAt: -1 });

artifactFlaggedSchema.post("save", (flag) => {
    ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});
artifactFlaggedSchema.post("findOneAndDelete", (flag) => {
    if (flag?.deletedCount > 0) return ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed` });
    ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});
artifactFlaggedSchema.post("deleteMany", (flag) => {
    if (flag?.deletedCount > 0) return ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed` });
    ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});

const ArtifactFlagged = db.qm.model<IArtifactFlag>("ArtifactFlagged", artifactFlaggedSchema, "artifacts_flagged");

export default ArtifactFlagged;
