import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import vesselService from '../../services/Vessel.service';
import db from '../../modules/db';
import { canAccessVessel, validateError } from '../../utils/functions';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/Vessel.service', () => ({
    find: jest.fn()
}));
jest.mock('../../utils/functions', () => ({
    canAccessVessel: jest.fn(),
    validateError: jest.fn()
}));

describe('Audio API', () => {
    describe('GET /api/audios/bulk', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (db.audio.collection as any).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnThis(),
                            toArray: jest.fn().mockResolvedValue([] as never)
                        })
                    });
                    (canAccessVessel as any).mockReturnValue(true);
                    (validateError as any).mockImplementation((_err, res) => {
                        res.status(500).json({ message: "Internal server error" });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/audios/bulk');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if vesselIds is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselIds contains invalid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: 'invalid-id' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if startTimestampISO is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({
                            vesselIds: '507f1f77bcf86cd799439011',
                            startTimestampISO: 'invalid-date'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if endTimestampISO is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({
                            vesselIds: '507f1f77bcf86cd799439011',
                            endTimestampISO: 'invalid-date'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if endTimestampISO is provided without startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({
                            vesselIds: '507f1f77bcf86cd799439011',
                            endTimestampISO: '2023-01-01T00:00:00.000Z'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 if startTimestampISO is provided without endTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({
                            vesselIds: '507f1f77bcf86cd799439011',
                            startTimestampISO: '2023-01-01T00:00:00.000Z'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 with audio data for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const mockAudioData = [{
                        _id: '507f1f77bcf86cd799439012',
                        onboard_vessel_id: '507f1f77bcf86cd799439011',
                        frequency: 1000,
                        timestamp: new Date(),
                        host_location: { lat: 0, lng: 0 }
                    }];

                    (db.audio.collection as any).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnThis(),
                            toArray: jest.fn().mockResolvedValue(mockAudioData as never)
                        })
                    });

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439011');
                });

                it('should return 200 with empty audio data when no audios found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439011');
                    expect(res.body['507f1f77bcf86cd799439011']).toEqual([]);
                });

                it('should handle multiple vessel IDs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439011' },
                        { _id: '507f1f77bcf86cd799439012' }
                    ]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011,507f1f77bcf86cd799439012' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439011');
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439012');
                });

                it('should filter vessels based on user access', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439011' },
                        { _id: '507f1f77bcf86cd799439012' }
                    ]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011,507f1f77bcf86cd799439012' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle timestamp filtering', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({
                            vesselIds: '507f1f77bcf86cd799439011',
                            startTimestampISO: '2023-01-01T00:00:00.000Z',
                            endTimestampISO: '2023-12-31T23:59:59.999Z'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle Swagger requests with limit', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockResolvedValue([{ _id: '507f1f77bcf86cd799439011' }]);

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011' })
                        .set('Authorization', authToken)
                        .set('Referer', 'http://localhost:3000/docs');

                    expect(res.status).toBe(200);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as any).mockRejectedValue(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/audios/bulk')
                        .query({ vesselIds: '507f1f77bcf86cd799439011' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
