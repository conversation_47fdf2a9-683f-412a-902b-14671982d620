import { jest } from '@jest/globals';

type LookupResult = { synonyms: string[] }[];

const defaultLookup = (word: string): LookupResult => {
    if (word.toLowerCase() === 'automobile') {
        return [{ synonyms: ['car', 'vehicle', 'auto'] }];
    }
    return [{ synonyms: [] }];
};

class WordNetMock {
    lookup = jest.fn((word: string, callback: (results: LookupResult) => void) => {
        const results = defaultLookup(word);
        callback(results);
    });
}

const naturalMock = {
    WordNet: jest.fn(() => new WordNetMock())
};

export default naturalMock;


