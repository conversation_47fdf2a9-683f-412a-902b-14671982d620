import SpellingCorrector from '../../modules/spellingCorrector';
import { jest, beforeEach, describe, it, expect } from '@jest/globals';
import fs from 'fs';

const mockFs = fs as jest.Mocked<typeof fs>;

jest.mock('fs', () => ({
    readFileSync: jest.fn()
}));
jest.mock('nspell', () => {
    return jest.fn(() => ({
        correct: jest.fn(),
        suggest: jest.fn()
    }));
});

describe('SpellingCorrector Module', () => {
    let spellingCorrector: SpellingCorrector;

    beforeEach(() => {
        spellingCorrector = new SpellingCorrector();
        jest.clearAllMocks();
    });

    describe('Dictionary Loading Functionality', () => {
        it('should successfully load dictionary files using default file paths', () => {
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

            expect(() => spellingCorrector.loadDictionary()).not.toThrow();

            expect(mockFs.readFileSync).toHaveBeenCalledTimes(2);
            expect(consoleSpy).toHaveBeenCalledWith('SpellingCorrector dictionary loaded successfully');

            consoleSpy.mockRestore();
        });

        it('should successfully load dictionary files using custom file paths', () => {
            const customAffPath = './custom/path/en_US.aff';
            const customDicPath = './custom/path/en_US.dic';
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

            expect(() => spellingCorrector.loadDictionary(customAffPath, customDicPath)).not.toThrow();

            expect(mockFs.readFileSync).toHaveBeenCalledWith(customAffPath, 'utf-8');
            expect(mockFs.readFileSync).toHaveBeenCalledWith(customDicPath, 'utf-8');
            expect(consoleSpy).toHaveBeenCalledWith('SpellingCorrector dictionary loaded successfully');

            consoleSpy.mockRestore();
        });

        it('should throw error when dictionary files cannot be read from filesystem', () => {
            const error = new Error('File not found');
            mockFs.readFileSync.mockImplementation(() => {
                throw error;
            });

            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

            expect(() => spellingCorrector.loadDictionary()).toThrow(error);
            expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to load SpellingCorrector dictionary:', error);

            consoleErrorSpy.mockRestore();
        });
    });

    describe('Spelling Correction Functionality', () => {
        it('should return original word when dictionary has not been loaded', () => {
            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            
            const result = spellingCorrector.correct('hello');
            
            expect(result).toBe('hello');
            expect(consoleWarnSpy).toHaveBeenCalledWith('Dictionary not loaded, returning original word');
            
            consoleWarnSpy.mockRestore();
        });

        it('should return original word when word is already spelled correctly', () => {
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            spellingCorrector.loadDictionary();
            consoleSpy.mockRestore();

            const mockSpell = {
                correct: jest.fn().mockReturnValue(true),
                suggest: jest.fn()
            };
            (spellingCorrector as any).spell = mockSpell;

            const result = spellingCorrector.correct('hello');
            
            expect(result).toBe('hello');
            expect(mockSpell.correct).toHaveBeenCalledWith('hello');
            expect(mockSpell.suggest).not.toHaveBeenCalled();
        });

        it('should return first suggestion when word is misspelled and suggestions are available', () => {
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            spellingCorrector.loadDictionary();
            consoleSpy.mockRestore();

            const mockSpell = {
                correct: jest.fn().mockReturnValue(false),
                suggest: jest.fn().mockReturnValue(['hello', 'hallo'])
            };
            (spellingCorrector as any).spell = mockSpell;

            const result = spellingCorrector.correct('helo');
            
            expect(result).toBe('hello');
            expect(mockSpell.correct).toHaveBeenCalledWith('helo');
            expect(mockSpell.suggest).toHaveBeenCalledWith('helo');
        });

        it('should return original word when no spelling suggestions are available', () => {
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            spellingCorrector.loadDictionary();
            consoleSpy.mockRestore();

            const mockSpell = {
                correct: jest.fn().mockReturnValue(false),
                suggest: jest.fn().mockReturnValue([])
            };
            (spellingCorrector as any).spell = mockSpell;

            const result = spellingCorrector.correct('xyz123');
            
            expect(result).toBe('xyz123');
            expect(mockSpell.correct).toHaveBeenCalledWith('xyz123');
            expect(mockSpell.suggest).toHaveBeenCalledWith('xyz123');
        });

        it('should handle errors during spelling correction process gracefully', () => {
            const mockAffContent = 'SET UTF-8\nTRY esianrtolcdugmphbyfvkwz';
            const mockDicContent = '1000\nhello\nworld\ntest';

            mockFs.readFileSync
                .mockReturnValueOnce(mockAffContent)
                .mockReturnValueOnce(mockDicContent);

            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            spellingCorrector.loadDictionary();
            consoleSpy.mockRestore();

            const error = new Error('Spelling check failed');
            const mockSpell = {
                correct: jest.fn().mockImplementation(() => {
                    throw error;
                }),
                suggest: jest.fn()
            };
            (spellingCorrector as any).spell = mockSpell;

            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
            
            const result = spellingCorrector.correct('hello');
            
            expect(result).toBe('hello');
            expect(consoleErrorSpy).toHaveBeenCalledWith('Error in spelling correction:', error);
            
            consoleErrorSpy.mockRestore();
        });
    });
});