import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import streamService from '../../services/Stream.service';
import Region from '../../models/Region';
import RegionGroup from '../../models/RegionGroup';
import vesselService from '../../services/Vessel.service';
import awsKinesis from '../../modules/awsKinesis';
import { consoleLogObjectSize } from '../../utils/functions';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/Region', () => require('../mocks/models/Region.mock'));
jest.mock('../../models/RegionGroup', () => require('../mocks/models/regionGroup.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vesselService.mock'));
jest.mock('../../modules/awsKinesis', () => require('../mocks/services/streamService.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('StreamService', () => {
    beforeEach(async () => {
        jest.resetAllMocks();
        await streamService.resetCache();
    });

    it('returns cached data if checked within 5 minutes and filters by region', async () => {
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }] as never);
        (awsKinesis.listStreams as jest.Mock).mockResolvedValue([{ StreamName: 'U1', Region: 'r1', IsLive: true }] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([{ unit_id: 'U1', name: 'V', region_group_id: 'RG' }] as never);

        const first = await streamService.fetchAll();
        expect(first.length).toBe(1);

        const second = await streamService.fetchAll({ regions: ['r2'] });
        expect(second.length).toBe(0);

        const third = await streamService.fetchAll({ regions: ['r1'] });
        expect(third.length).toBe(1);

        const fourth = await streamService.fetchAll();
        expect(fourth.length).toBe(1);
    });

    it('fetches and maps streams with vessel and regionGroup fields', async () => {
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }] as never);
        (awsKinesis.listStreams as jest.Mock).mockResolvedValue([{ StreamName: 'U1', Region: 'r1', IsLive: true }] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([{ _id: 'RG', timezone: 'UTC' }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([{ _id: 'VID', unit_id: 'U1', name: 'V', thumbnail_compressed_s3_key: 'T', region_group_id: 'RG' }] as never);

        const updated = await streamService.fetchAll();
        expect(updated[0]).toMatchObject({ unit_id: 'U1', name: 'V', thumbnail: 'T', region: 'r1', is_live: true, timezone: 'UTC', region_group_id: 'RG' });
    });

    it('applies region filter after refresh (include and exclude)', async () => {
        await streamService.resetCache();
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }] as never);
        (awsKinesis.listStreams as jest.Mock).mockResolvedValue([{ StreamName: 'U1', Region: 'r1', IsLive: true }] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([{ unit_id: 'U1', name: 'V', region_group_id: 'RG' }] as never);

        const include = await streamService.fetchAll({ regions: ['r1'] });
        expect(include.length).toBe(1);

        const exclude = await streamService.fetchAll({ regions: ['zzz'] });
        expect(exclude.length).toBe(0);
    });

    it('handles kinesis list error per region and continues', async () => {
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }, { is_live: true, value: 'r2' }] as never);
        (awsKinesis.listStreams as jest.Mock)
            .mockRejectedValueOnce(new Error('x') as never)
            .mockResolvedValueOnce([] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);

        const updated = await streamService.fetchAll();
        expect(Array.isArray(updated)).toBe(true);
    });

    it('maps defaults when stream fields are missing', async () => {
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }] as never);
        (awsKinesis.listStreams as jest.Mock).mockResolvedValue([{ StreamName: 'NA' }] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);

        const result = await streamService.fetchAll();
        expect(result[0]).toEqual({
            unit_id: 'NA',
            name: null,
            thumbnail: null,
            region: '',
            is_live: false,
            timezone: null,
            region_group_id: null,
        });
    });

    it('fetchSingle returns from cache or triggers fetchAll and returns null when not found', async () => {
        await streamService.resetCache();
        (Region.find as jest.Mock).mockResolvedValue([{ is_live: true, value: 'r1' }] as never);
        (awsKinesis.listStreams as jest.Mock).mockResolvedValue([{ StreamName: 'U1', Region: 'r1', IsLive: true }] as never);
        (RegionGroup.find as jest.Mock).mockResolvedValue([] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);

        const res1 = await streamService.fetchSingle({ unitId: 'NA' });
        expect(res1).toBeNull();

        const res2 = await streamService.fetchSingle({ unitId: 'U1' });
        expect(res2?.unit_id).toBe('U1');
    });

    it('constructor sets interval in non-test env', async () => {
        const prev = process.env.NODE_ENV;
        process.env.NODE_ENV = 'development';
        jest.useFakeTimers();
        await jest.isolateModulesAsync(async () => {
            const mod = await import('../../services/Stream.service');
            expect(mod.default).toBeDefined();
        });
        jest.advanceTimersByTime(60000);
        expect(consoleLogObjectSize).toHaveBeenCalledWith([], 'streamService.streamsInfo');
        jest.clearAllTimers();
        jest.useRealTimers();
        process.env.NODE_ENV = prev;
    });
});


