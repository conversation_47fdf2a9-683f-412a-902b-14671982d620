import { jest } from '@jest/globals';

const mockUpload = {
    single: jest.fn().mockImplementation((_fieldName: any) => {
        return (req: any, _res: any, next: any) => {
            req.file = undefined;
            next();
        };
    }),
    array: jest.fn(),
    fields: jest.fn(),
    none: jest.fn(),
    any: jest.fn(),
};

const mockHandleMulterError = jest.fn().mockImplementation((_error: any, _req: any, _res: any, next: any) => {
    next();
});

export { mockUpload as upload, mockHandleMulterError as handleMulterError };
