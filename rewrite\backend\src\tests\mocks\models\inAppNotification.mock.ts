import { jest } from "@jest/globals";

const InAppNotification = {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    deleteOne: jest.fn(),
    deleteMany: jest.fn(),
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
    distinct: jest.fn(),
    save: jest.fn(),
    toObject: jest.fn(),
    markModified: jest.fn(),
};

module.exports = InAppNotification;
