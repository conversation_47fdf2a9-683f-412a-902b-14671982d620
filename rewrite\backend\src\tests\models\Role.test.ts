import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";
import { setupPermissionTest, cleanupPermissionTest } from "../mocks/models/permission.mock";

jest.resetModules();

describe('Role Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;
    let mockPermission: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        
        const permissionSetup = setupPermissionTest();
        mockPermission = permissionSetup.mockPermission;
        
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
        cleanupPermissionTest();
    });

    it('should create Role model with proper schema and hooks', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);
        jest.doMock('../../models/Permission', () => ({ default: mockPermission }));

        delete require.cache[require.resolve('../../models/Role')];

        const RoleModule = await import('../../models/Role');
        const Role = RoleModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Role', expect.any(Object));
        expect(Role).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const preHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preHookCall) {
            const preHookFn = preHookCall[1];
            
            const mockRole1 = {
                isNew: true,
                constructor: jest.fn().mockReturnValue({
                    findOne: jest.fn().mockReturnValue({
                        sort: (jest.fn() as any).mockResolvedValue({ role_id: 5 })
                    })
                }),
                role_id: null,
                hierarchy_number: null,
                denied_permissions: null
            };
            const next1 = jest.fn();
            await preHookFn.call(mockRole1, next1);

            const mockRole2 = {
                isNew: false
            };
            const next2 = jest.fn();
            await preHookFn.call(mockRole2, next2);
        }

        expect(schemaArg.pre).toHaveBeenCalledWith("save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockRole = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', role_name: 'Test Role' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockRole);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockRole);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});