import { jest, describe, it, expect } from '@jest/globals';

jest.mock('p-limit', () => {
    return jest.fn(() => jest.fn());
});

describe('pLimit module', () => {
    it('should successfully export a function from p-limit', () => {
        const limitPromise = require('../../modules/pLimit').default;
        expect(limitPromise).toBeDefined();
        expect(typeof limitPromise).toBe('function');
    });
});
