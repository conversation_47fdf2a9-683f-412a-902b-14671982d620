import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext } from '../mocks/middlewares/auth.mock';
import { NextFunction } from 'express';

let capturedMulterOptions: any;

jest.mock('multer', () => {
    class MockMulterError extends Error {
        code: any;
        constructor(code?: any, message?: string) {
            super(message ?? "");
            this.name = "MulterError";
            this.code = code;
        }
    }

    const multerFn: any = jest.fn((options?: any) => {
        capturedMulterOptions = options;
        return ({
            single: jest.fn(),
            array: jest.fn(),
            fields: jest.fn(),
            none: jest.fn(),
            any: jest.fn()
        });
    });
    multerFn.memoryStorage = jest.fn(() => ({}));
    multerFn.MulterError = MockMulterError;

    return {
        __esModule: true,
        default: multerFn,
        MulterError: MockMulterError
    };
});

describe('multerConfig middleware', () => {
    let req: any;
    let res: any;
    let next: NextFunction;

    beforeEach(() => {
        req = createMockRequest();
        res = createMockResponse();
        next = createMockNext();
    });

    describe('handleMulterError', () => {
        it('should handle LIMIT_FILE_SIZE error', () => {
            const { handleMulterError } = require('../../middlewares/multerConfig');
            const { MulterError } = require('multer');
            const error = new MulterError('LIMIT_FILE_SIZE', 'File too large');
            handleMulterError(error as any, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'File too large. Maximum size is 5MB.'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle LIMIT_UNEXPECTED_FILE error', () => {
            const { handleMulterError } = require('../../middlewares/multerConfig');
            const { MulterError } = require('multer');
            const error = new MulterError('LIMIT_UNEXPECTED_FILE', 'Unexpected field');
            handleMulterError(error as any, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'Unexpected field name for file upload.'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle other MulterError types', () => {
            const { handleMulterError } = require('../../middlewares/multerConfig');
            const { MulterError } = require('multer');
            const error = new MulterError('LIMIT_FILE_COUNT', 'Too many files');
            handleMulterError(error as any, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'File upload error: Too many files'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle file filter error for non-image files', () => {
            const error = new Error('Only image files are allowed!');
            const { handleMulterError } = require('../../middlewares/multerConfig');
            handleMulterError(error, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'Only image files are allowed!'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should pass through other errors to next middleware', () => {
            const error = new Error('Some other error');
            const { handleMulterError } = require('../../middlewares/multerConfig');
            handleMulterError(error, req, res, next);
            expect(res.status).not.toHaveBeenCalled();
            expect(res.json).not.toHaveBeenCalled();
            expect(next).toHaveBeenCalledWith(error);
        });

        it('should handle MulterError without code', () => {
            const { handleMulterError } = require('../../middlewares/multerConfig');
            const { MulterError } = require('multer');
            const error = new MulterError(undefined, 'Multer error without code');
            handleMulterError(error as any, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'File upload error: Multer error without code'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle MulterError without message', () => {
            const { handleMulterError } = require('../../middlewares/multerConfig');
            const { MulterError } = require('multer');
            const error = new MulterError('LIMIT_FILE_SIZE');
            handleMulterError(error as any, req, res, next);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                message: 'File too large. Maximum size is 5MB.'
            });
            expect(next).not.toHaveBeenCalled();
        });

        it('should handle non-Error objects', () => {
            const error = 'String error';
            const { handleMulterError } = require('../../middlewares/multerConfig');
            handleMulterError(error as any, req, res, next);
            expect(res.status).not.toHaveBeenCalled();
            expect(res.json).not.toHaveBeenCalled();
            expect(next).toHaveBeenCalledWith(error);
        });

        it('should throw on null error', () => {
            const error = null;
            const { handleMulterError } = require('../../middlewares/multerConfig');
            expect(() => handleMulterError(error as any, req, res, next)).toThrow();
        });

        it('should throw on undefined error', () => {
            const error = undefined;
            const { handleMulterError } = require('../../middlewares/multerConfig');
            expect(() => handleMulterError(error as any, req, res, next)).toThrow();
        });

        it('should handle error with empty message', () => {
            const error = new Error('');
            const { handleMulterError } = require('../../middlewares/multerConfig');
            handleMulterError(error, req, res, next);
            expect(res.status).not.toHaveBeenCalled();
            expect(res.json).not.toHaveBeenCalled();
            expect(next).toHaveBeenCalledWith(error);
        });

        it('should handle error with special characters in message', () => {
            const error = new Error('Error with special chars: !@#$%^&*()');
            const { handleMulterError } = require('../../middlewares/multerConfig');
            handleMulterError(error, req, res, next);
            expect(res.status).not.toHaveBeenCalled();
            expect(res.json).not.toHaveBeenCalled();
            expect(next).toHaveBeenCalledWith(error);
        });
    });

    describe('fileFilter', () => {
        it('should accept image mimetypes and call cb with (null, true)', () => {
            jest.resetModules();
            capturedMulterOptions = undefined;
            const { upload } = require('../../middlewares/multerConfig');
            expect(upload).toBeDefined();
            expect(capturedMulterOptions).toBeDefined();
            const { fileFilter } = capturedMulterOptions as any;
            const req = {} as any;
            const cb = jest.fn();
            const mockFile = { mimetype: 'image/png' } as any;
            fileFilter(req, mockFile, cb);
            expect(cb).toHaveBeenCalledWith(null, true);
        });

        it('should reject non-image mimetypes and call cb with Error', () => {
            jest.resetModules();
            capturedMulterOptions = undefined;
            const { upload } = require('../../middlewares/multerConfig');
            expect(upload).toBeDefined();
            expect(capturedMulterOptions).toBeDefined();
            const { fileFilter } = capturedMulterOptions as any;
            const req = {} as any;
            const cb = jest.fn();
            const mockFile = { mimetype: 'application/pdf' } as any;
            fileFilter(req, mockFile, cb);
            expect(cb).toHaveBeenCalled();
            const firstArg = (cb as any).mock.calls[0][0];
            expect(firstArg).toBeInstanceOf(Error);
            expect(firstArg.message).toBe('Only image files are allowed!');
        });
    });
});