import { jest, expect } from '@jest/globals';
import { NextFunction } from 'express';

export const createJWTMock = () => ({
    verify: jest.fn(),
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    decode: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    JsonWebTokenError: class JsonWebTokenError extends Error {
        constructor(message: string) {
            super(message);
            this.name = 'JsonWebTokenError';
        }
    }
});

export interface MockRequest {
    header: jest.Mock;
    user?: any;
    api_key?: any;
    _endpoint_id?: string;
    method?: string;
    originalUrl?: string;
    url?: string;
    path?: string;
    body?: any;
    query?: any;
    params?: any;
    headers?: any;
    cookies?: any;
    ip?: string;
}

export interface MockResponse {
    status: jest.Mock;
    json: jest.Mock;
    send: jest.Mock;
    cookie?: jest.Mock;
    clearCookie?: jest.Mock;
    locals?: any;
}

export const createMockRequest = (overrides: Partial<MockRequest> = {}): MockRequest => ({
    header: jest.fn(),
    method: 'GET',
    originalUrl: '/test',
    url: '/test',
    path: '/test',
    body: {},
    query: {},
    params: {},
    headers: {},
    cookies: {},
    ip: '127.0.0.1',
    _endpoint_id: 'test-endpoint',
    ...overrides
});

export const createMockResponse = (overrides: Partial<MockResponse> = {}): MockResponse => ({
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis(),
    locals: {},
    ...overrides
});

export const createMockNext = (): NextFunction => jest.fn();

export const setupAuthTest = (overrides: {
    req?: Partial<MockRequest>;
    res?: Partial<MockResponse>;
} = {}) => {
    const req = createMockRequest(overrides.req);
    const res = createMockResponse(overrides.res);
    const next = createMockNext();

    return { req, res, next };
};

export const expectUnauthorizedResponse = (res: MockResponse, message = 'Unauthorized') => {
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ message });
};

export const expectForbiddenResponse = (res: MockResponse, message = 'Forbidden') => {
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({ message });
};

export const expectServerErrorResponse = (res: MockResponse, message = 'Internal server error') => {
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({ message });
};

export const expectNextCalled = (next: jest.Mock) => {
    expect(next).toHaveBeenCalled();
};

export const expectNextNotCalled = (next: jest.Mock) => {
    expect(next).not.toHaveBeenCalled();
};

export const silenceConsole = () => {
    const originalConsole = { ...console };

    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
    console.info = jest.fn();
    console.debug = jest.fn();

    return () => {
        Object.assign(console, originalConsole);
    };
};

export const cleanupAuthTest = () => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    if (global.gc) {
        global.gc();
    }
};
