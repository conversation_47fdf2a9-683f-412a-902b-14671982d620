import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import vesselService from '../../services/Vessel.service';
import { canAccessVessel, validateError } from '../../utils/functions';
import { lookups } from '../mocks/modules/db.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

const mockVessels = [
    {
        _id: '507f1f77bcf86cd799439012',
        name: 'Test Vessel 1',
        is_active: true,
        region_group_id: '507f1f77bcf86cd799439020'
    },
    {
        _id: '507f1f77bcf86cd799439013',
        name: 'Test Vessel 2',
        is_active: true,
        region_group_id: '507f1f77bcf86cd799439021'
    }
];

const mockAisMessages = [
    {
        _id: '507f1f77bcf86cd799439022',
        location: { type: 'Point', coordinates: [10.0, 20.0] },
        metadata: { onboard_vessel_id: '507f1f77bcf86cd799439012', mmsi: '123456789' },
        details: { message: { speed: 10 } },
        name: 'Test Vessel 1',
        timestamp: new Date('2023-01-01T00:00:00Z')
    },
    {
        _id: '507f1f77bcf86cd799439023',
        location: { type: 'Point', coordinates: [11.0, 21.0] },
        metadata: { onboard_vessel_id: null, mmsi: '987654321' },
        details: { message: { speed: 15 } },
        name: 'Test Vessel 2',
        timestamp: new Date('2023-01-01T01:00:00Z')
    }
];

describe('VesselAis API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('GET /api/vesselAis/latest', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (lookups.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            limit: jest.fn().mockResolvedValue(mockAisMessages as never),
                            toArray: jest.fn().mockResolvedValue(mockAisMessages as never),
                        }),
                    });
                });

                it('should return 200 and latest AIS data for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(vesselService, 'find').mockResolvedValueOnce(mockVessels as any);
                    jest.spyOn(require('../../utils/functions'), 'canAccessVessel').mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439012');
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439013');
                });

                it('should return 200 with startTimestampISO filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(vesselService, 'find').mockResolvedValueOnce(mockVessels as any);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ 
                            vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013',
                            startTimestampISO: '2023-01-01T00:00:00Z'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439012');
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439013');
                });

                it('should return 200 with Swagger limit when Referer includes /docs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(vesselService, 'find').mockResolvedValueOnce(mockVessels as any);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' })
                        .set('Authorization', authToken)
                        .set('Referer', '/docs');

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439012');
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439013');
                });

                it('should return 200 with empty arrays for vessels with no AIS data', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(vesselService, 'find').mockResolvedValueOnce(mockVessels as any);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    
                    (lookups.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            limit: jest.fn().mockResolvedValue([] as never),
                            toArray: jest.fn().mockResolvedValue([] as never),
                        }),
                    });

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439012');
                    expect(res.body).toHaveProperty('507f1f77bcf86cd799439013');
                    expect(res.body['507f1f77bcf86cd799439012']).toEqual([]);
                    expect(res.body['507f1f77bcf86cd799439013']).toEqual([]);
                });

                it('should return 400 for missing vesselIds', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid vesselIds', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: 'invalid-id' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ 
                            vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013',
                            startTimestampISO: 'invalid-date'
                        })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    jest.spyOn(vesselService, 'find').mockRejectedValueOnce(new Error('Database error'));
                    jest.spyOn(require('../../utils/functions'), 'validateError').mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 500 on database error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    jest.spyOn(vesselService, 'find').mockResolvedValueOnce(mockVessels as any);
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (lookups.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockRejectedValueOnce(new Error('Database connection error') as never),
                            }),
                        }),
                    });
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });

                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });


                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselAis/latest')
                        .query({ vesselIds: '507f1f77bcf86cd799439012,507f1f77bcf86cd799439013' });

                    expect(res.status).toBe(401);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
