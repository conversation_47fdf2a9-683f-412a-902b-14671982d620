import mongoose from "mongoose";
import db from "../modules/db";
import crypto from "crypto";
import { IApiKey } from "src/interfaces/ApiKey";
// import ioEmitter from '../modules/ioEmitter';

const apiKeySchema = new mongoose.Schema({
    api_key: { type: String, required: true, default: () => Buffer.from(crypto.randomBytes(16)).toString("hex"), unique: true },
    description: { type: String, required: true },
    email: { type: String, required: false },
    allowed_endpoints: { type: Array, required: true },
    is_deleted: { type: Boolean, required: true, default: false },
    is_revoked: { type: Boolean, required: true, default: false },
    requests: { type: Number, required: true, default: 0 },
    requests_endpoints: { type: Object, required: true, default: {} },
    jwt_token: { type: String, required: false },
    allowed_vessels: { type: Array, required: true, default: [] },
    last_used: { type: Date, required: false },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
    },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

/** TODO: temporarily disabled due to an api endpoint spam resulting in continuous listener invocation */
// apiKeySchema.post('save', emitChangedEvent)
// apiKeySchema.post('findOneAndDelete', emitChangedEvent)
// apiKeySchema.post("findOneAndUpdate", emitChangedEvent);

// function emitChangedEvent(apiKey) {
//     ioEmitter.emit('notifyAll', { name: `apiKeys/changed`, data: apiKey.toObject() });
// }

const ApiKey = db.qm.model<IApiKey>("ApiKey", apiKeySchema, "api_keys");

export default ApiKey;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
