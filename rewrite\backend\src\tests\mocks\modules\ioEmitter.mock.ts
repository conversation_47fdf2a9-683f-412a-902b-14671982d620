import { jest } from '@jest/globals';

export const emit = jest.fn();
export const on = jest.fn();
export const off = jest.fn();
export const once = jest.fn();
export const removeAllListeners = jest.fn();
export const listenerCount = jest.fn();
export const listeners = jest.fn();
export const eventNames = jest.fn();
export const setMaxListeners = jest.fn();
export const getMaxListeners = jest.fn();

export default {
    emit,
    on,
    off,
    once,
    removeAllListeners,
    listenerCount,
    listeners,
    eventNames,
    setMaxListeners,
    getMaxListeners,
};
