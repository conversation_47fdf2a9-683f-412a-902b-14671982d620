import dayjs from "dayjs";

// import s3Controller from "../../../controllers/S3.controller";
import { calculateDistance, defaultValues, displayCoordinates, simplifyTimezone, stringToColor, userValues } from "../../../utils";
// import favouriteArtifactsController from "../../../controllers/FavouriteArtifacts.controller";
import { MarkerClusterer, SuperClusterAlgorithm } from "@googlemaps/markerclusterer";
import idb from "../../../indexedDB";
// import axiosInstance from "../../../axios.js";
import WrappedInfoWindow from "./InfoWindow.jsx";
import { createRoot } from "react-dom/client";
import React from "react";
import WrappedClusterInfoWindow from "./ClusterInfoWindow.jsx";
import WrappedAudioInfoWindow from "./AudioInfoWindow.jsx";
import WrappedAudioClusterInfoWindow from "./AudioClusterInfoWindow.jsx";

////This function is no longer need since we migrated the info window
// const downloadBlockElems = function (handler) {
//     return `
//         <button id="download-btn" ${handler ? `onClick="${handler}"` : ""}>Download</button>
//         <span id="download-spinner" class="hidden">
//                           <svg
//                               style="width: 24px; height: 24px;"
//                               viewBox="22 22 44 44"
//                           >
//                             <style>
//                               @keyframes MuiCircularProgress-keyframes-circular-rotate {
//                                 100 % {
//                                     transform: rotate(360deg);
//                             }
//                             }
//                                 @keyframes MuiCircularProgress-keyframes-circular-dash {
//                                 0 % {
//                                     stroke-dasharray: 1px, 200px;
//                                 stroke-dashoffset: 0px;
//                             }
//                                 50% {
//                                 stroke-dasharray: 100px, 200px;
//                                 stroke-dashoffset: -15px;
//                             }
//                                 100% {
//                                 stroke-dasharray: 100px, 200px;
//                                 stroke-dashoffset: -125px;
//                             }
//                             }
//                             </style>
//                             <circle
//                                 style="stroke: white; animation: 1.4s ease-in-out infinite MuiCircularProgress-keyframes-circular-dash; stroke-dasharray: 80px, 200px; stroke-dashoffset: 0px;"
//                                 cx="44"
//                                 cy="44"
//                                 r="20.2"
//                                 fill="none"
//                                 stroke-width="3.6"
//                             />
//                           </svg>
//                         </span>
//     `;
// };

//This function is no longer need since we migrated the info window
// const downloadArtifact = async (artifactId) => {
//     const spinner = document.getElementById("download-spinner");
//     const downloadBtn = document.getElementById("download-btn");

//     downloadBtn.classList.add("hidden");
//     spinner.classList.remove("hidden");

//     await axiosInstance
//         .post(
//             `artifacts/${artifactId}/download`,
//             // `artifacts/66fa861c64ecc5217496b973/download`,
//             {},
//             {
//                 responseType: "blob",
//                 timeout: 120000,
//             },
//         )
//         .then(procDownloadResponse)
//         .catch((e) => {
//             console.error(e);
//         })
//         .finally(() => {
//             spinner.classList.add("hidden");
//             downloadBtn.classList.remove("hidden");
//         });
// };

// This is used for both single info window & cluster info window for generate the content

// This function is no longer need since we migrated the info window
// const generateContentWithImage = async (
//     artifact,
//     isGrouped = false,
//     currentIndex = 0,
//     groupLength = 0,
//     favouriteArtifactsRef,
//     cachedSrc,
//     timezone,
// ) => {
//     try {
//         const img = s3Controller.fetchPreviewUrl(artifact);
//         const isFavourite = favouriteArtifactsRef.current.some((fav) => fav.artifact_id === artifact._id);

//         return `
//             <div style="color: black; padding: 20px 20px 30px; display: flex; flex-direction: column; max-width: 330px; gap: 5px; background: #343B44;">
//                 <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
//                     <span style="color: white; font-size: 24px; font-weight: 500;">
//                         Artifact${isGrouped ? ` ${currentIndex + 1} / ${groupLength}` : ""}
//                     </span>
//                     <button id="custom-close-btn" style="width: fit-content; display: flex; align-self: flex-end; background: #343B44; color: white; border: 1px solid white; border-radius: 50%; cursor: pointer; padding: 5px; margin: 0; line-height: 12px; font-size: 10px; transition: all .3s;">
//                         <svg id="custom-close-icon" width="14px" height="14px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
//                             <path fill="#000000" d="M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496z" />
//                         </svg>
//                     </button>
//                 </div>

//                 <div class="artifact-container" style="position: relative; background: #343B44; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; width: auto;" class="img-box">
//                     <span style="background: #343B44; color: white; font-size: 16px; font-weight: bold; height: 200px; display: flex; align-items: center; justify-content: center;" class="loading-text">Loading...</span>
//                     <img src="${img}" alt="Artifact" class="artifact-image" style="display: none; width: 100%; height: 100%; object-fit: cover; border-radius: 5px;" onload="this.style.display='block'; this.previousElementSibling.style.display='none';" />
//                     ${
//                         artifact.video_path
//                             ? `<div id="play-button-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: rgba(0, 0, 0, 0.3); border-radius: 5px; cursor: pointer;">
//                                 <svg style="width: 50px; height: 50px; filter: drop-shadow(0px 2px 3px rgba(0,0,0,0.5));" viewBox="0 0 24 24" fill="white">
//                                     <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z"/>
//                                 </svg>
//                                </div>`
//                             : ""
//                     }
//                     ${`<button id="favourite-btn" style="position: absolute; top: 10px; right: 10px; background: none; border: none; cursor: pointer;">
//                             <svg width="18" height="18" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
//                                 <path fill="${isFavourite ? "gold" : "white"}" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
//                             </svg>
//                         </button>`}
//                     <button id="share-btn" style="position: absolute; top: 50px; right: 10px; background: none; border: none; cursor: pointer;">
//                         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18px" height="18px">
//                             <path d="M 18 2 A 3 3 0 0 0 15 5 A 3 3 0 0 0 15.054688 5.5605469 L 7.9394531 9.7109375 A 3 3 0 0 0 6 9 A 3 3 0 0 0 3 12 A 3 3 0 0 0 6 15 A 3 3 0 0 0 7.9355469 14.287109 L 15.054688 18.439453 A 3 3 0 0 0 15 19 A 3 3 0 0 0 18 22 A 3 3 0 0 0 21 19 A 3 3 0 0 0 18 16 A 3 3 0 0 0 16.0625 16.712891 L 8.9453125 12.560547 A 3 3 0 0 0 9 12 A 3 3 0 0 0 8.9453125 11.439453 L 16.060547 7.2890625 A 3 3 0 0 0 18 8 A 3 3 0 0 0 21 5 A 3 3 0 0 0 18 2 z"/>
//                         </svg>
//                     </button>
//                     ${
//                         artifact.video_path
//                             ? ""
//                             : `<button id="fullscreen-btn" style="position: absolute; top: 90px; right: 10px; background: none; border: none; cursor: pointer;">
//                             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18px" height="18px">
//                                 <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" fill="white"/>
//                             </svg>
//                         </button>`
//                     }
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         <strong>Super Category</strong>: ${(artifact.super_category !== undefined && artifact.super_category) || "Not available"}
//                     </p>
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         <strong>Category</strong>: ${(artifact.category !== undefined && artifact.category) || "Not available"}
//                     </p>
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         <strong>Color</strong>: ${(artifact.color !== undefined && artifact.color) || "Not available"}
//                     </p>
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         <strong>Size</strong>: ${(artifact.size !== undefined && artifact.size) || "Not available"}
//                     </p>
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         <strong>Timestamp</strong>: ${artifact.timestamp !== undefined && artifact.timestamp ? dayjs(artifact.timestamp).tz(timezone).format(defaultValues.dateTimeFormat()) : "Not available"}
//                     </p>
//                 </div>

//                 <div>
//                     <p style="font-size: 16px; font-family: Outfit, sans-serif;">
//                         ${(artifact.others !== undefined && artifact.others) || "Not available"}
//                     </p>
//                 </div>

//                 ${
//                     isGrouped
//                         ? `
//                     <div style="display: flex; justify-content: space-between; margin-top: 20px;">
//                         <button id="prev-btn" ${groupLength <= 1 ? "disabled" : ""}>&lsaquo;</button>
//                         ${downloadBlockElems()}
//                         <button id="next-btn" ${groupLength <= 1 ? "disabled" : ""}>&rsaquo;</button>
//                     </div>`
//                         : `
//                     <div style="display: flex; justify-content: center; margin-top: 20px;">
//                         ${downloadBlockElems()}
//                     </div>`
//                 }

//                 <style>
//                     .gm-style-iw-chr, .gm-style-iw-tc {
//                         display: none !important;
//                     }
//                     .gm-style .gm-style-iw-c {
//                         background-color: #343B44 !important;
//                         outline: none;
//                         padding: 0;
//                     }
//                     .gm-style .gm-style-iw-d {
//                         overflow: auto !important;
//                     }
//                     .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
//                         background-color: #fff !important;
//                     }
//                     .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
//                         background: #343B44 !important;
//                     }
//                     p {
//                         margin: 0;
//                         color: white;
//                     }
//                     strong {
//                         color: white;
//                     }
//                     #custom-close-icon path {
//                         border-color: white;
//                         fill: white;
//                     }
//                     #custom-close-btn:hover #custom-close-icon path {
//                         border-color: white;
//                         fill: #343B44;
//                     }
//                     #custom-close-btn:hover {
//                         background: white !important;
//                         color: #343B44 !important;
//                     }
//                     #prev-btn, #next-btn, #download-btn {
//                         padding: 10px;
//                         border-radius: 10px;
//                         background-color: transparent;
//                         color: #FFFFFF;
//                         cursor: pointer;
//                         min-width: 40px;
//                         transition: all .3s;
//                         border: 0;
//                     }

//                     #download-btn {
//                         background-color: #3873E4;
//                     }

//                     #prev-btn, #next-btn {
//                         font-size: 2rem;
//                         padding: 0;
//                     }

//                     #prev-btn:hover, #next-btn:hover {
//                         background-color: #FFFFFF;
//                         color: #4F5968;
//                     }

//                     .hidden {
//                         display: none;
//                     }

//                     /* Desktop view */
//                     @media (min-width: 768px) {
//                         .artifact-image, .artifact-video, .artifact-container {
//                             width: 300px;
//                             height: 200px;
//                             object-fit: cover;
//                         }
//                         .img-box {
//                             width: 300px;
//                             height: 200px;
//                         }
//                     }

//                     /* Mobile view */
//                     @media (max-width: 767px) {
//                         .artifact-image, .artifact-video {
//                             object-fit: cover;
//                             padding-right: 0px !important;
//                         }
//                         .img-box {
//                             height: 100px;
//                         }
//                     }
//                 </style>
//             </div>
//             `;
//     } catch (err) {
//         console.error("Error fetching signed URL:", err);
//         return `<p>Error loading image or video</p>`; // Fallback in case of an error
//     }
// };

//This function is no longer need since we migrated the info window
//This is used for giving the random location to those artifacts that are very close to each other on Map

// const generateRandomOffset = (minDistance, maxDistance) => {
//     const randomDistance = minDistance + Math.random() * (maxDistance - minDistance);
//     const randomAngle = Math.random() * 2 * Math.PI; // Random direction in radians
//     // Convert random distance into lat/lng offset
//     const offsetLat = (randomDistance * Math.cos(randomAngle)) / 111320; // 1 degree latitude ≈ 111320 meters
//     const offsetLng = (randomDistance * Math.sin(randomAngle)) / (111320 * Math.cos((Math.PI / 180) * 0)); // Longitude adjustment
//     return { offsetLat, offsetLng };
// };

// Define clusterer styles
const clusterStyles = [
    {
        url: `data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${defaultValues.icons.image}" /></svg>`,
        height: 50,
        width: 50,
        textColor: "#FFFFFF",
        textSize: 14,
    },
    {
        url: `data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${defaultValues.icons.image}" /></svg>`,
        height: 60,
        width: 60,
        textColor: "#FFFFFF",
        textSize: 16,
    },
    {
        url: `data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${defaultValues.icons.image}" /></svg>`,
        height: 70,
        width: 70,
        textColor: "#FFFFFF",
        textSize: 18,
    },
];

//This function is no longer needed since we migrated the info window
// const handleFavouriteButtonClick = async (artifact, favouriteArtifactsRef, setFavouriteArtifacts, user) => {
//     // marker || clsuter
//     const isFavourite = favouriteArtifactsRef.current.some((fav) => fav.artifact_id == artifact._id);

//     try {
//         if (isFavourite) {
//             const data = {
//                 artifact_id: artifact._id,
//             };
//             const response = await favouriteArtifactsController.removeFavouriteArtifact(data);

//             if (response.status === 200) {
//                 setFavouriteArtifacts((prev) => {
//                     const updated = prev.filter((item) => item.artifact_id !== artifact._id);
//                     favouriteArtifactsRef.current = updated;
//                     return updated;
//                 });
//             }
//         } else {
//             const data = {
//                 artifact_id: artifact._id,
//             };
//             const response = await favouriteArtifactsController.addFavouriteArtifact(data);
//             if (response.status === 201) {
//                 setFavouriteArtifacts((prev) => {
//                     const updated = [
//                         ...prev,
//                         {
//                             artifact_id: artifact._id,
//                         },
//                     ];
//                     favouriteArtifactsRef.current = updated;
//                     return updated;
//                 });
//             }
//         }
//     } catch (err) {
//         console.error("Error updating favourite status:", err);
//     }
// };

//Cluster info window component shifted to ClusterInfoWindow.jsx
//This function openClusterInfoWindow is no longer need in the code
// const openClusterInfoWindow = (
//     cluster,
//     currentClusterInfoWindow,
//     google,
//     favouriteArtifactsRef,
//     map,
//     setFavouriteArtifacts,
//     user,
//     cachedSrc,
//     timezone,
//     setArtifactToShare,
//     setIsShareModalOpen,
//     artifactInfowWindow,
//     setIsFullscreenOpen,
//     setFullscreenOpen,
// ) => {
//     const markers = cluster.markers;
//     const artifacts = markers.map((marker) => marker.artifactData);
//     let currentIndex = 0;
//     // console.log("open cluster info window ", cluster, "total favourites", favouriteArtifactsRef.current, favouriteArtifacts)
//     const updateInfoWindowContent = async () => {
//         const artifact = artifacts[currentIndex];
//         const newContent = await generateContentWithImage(artifact, true, currentIndex, artifacts.length, favouriteArtifactsRef, cachedSrc, timezone);

//         if (currentClusterInfoWindow) {
//             currentClusterInfoWindow.setContent(newContent);
//             addNavigationListeners();
//         }
//     };

//     const addNavigationListeners = () => {
//         setTimeout(() => {
//             const prevBtn = document.getElementById("prev-btn");
//             const nextBtn = document.getElementById("next-btn");
//             const closeButton = document.getElementById("custom-close-btn");
//             const favouriteBtn = document.getElementById("favourite-btn");
//             const shareBtn = document.getElementById("share-btn");
//             const downloadBtn = document.getElementById("download-btn");
//             const fullscreenBtn = document.getElementById("fullscreen-btn");
//             const playButtonOverlay = document.getElementById("play-button-overlay");

//             if (prevBtn) {
//                 prevBtn.onclick = () => {
//                     currentIndex = currentIndex > 0 ? currentIndex - 1 : artifacts.length - 1;
//                     updateInfoWindowContent();
//                 };
//             }

//             if (downloadBtn) {
//                 downloadBtn.onclick = () => {
//                     downloadArtifact(artifacts[currentIndex]._id);
//                 };
//             }

//             if (nextBtn) {
//                 nextBtn.onclick = () => {
//                     currentIndex = (currentIndex + 1) % artifacts.length;
//                     updateInfoWindowContent();
//                 };
//             }

//             if (closeButton) {
//                 closeButton.addEventListener("click", () => {
//                     currentClusterInfoWindow.close();
//                 });
//             }
//             if (favouriteBtn) {
//                 favouriteBtn.onclick = async () => {
//                     const artifact = artifacts[currentIndex];
//                     await handleFavouriteButtonClick(artifact, favouriteArtifactsRef, setFavouriteArtifacts, user);
//                     updateInfoWindowContent();
//                 };
//             }

//             if (shareBtn) {
//                 shareBtn.onclick = async () => {
//                     const artifact = artifacts[currentIndex];
//                     setArtifactToShare(artifact);
//                     setIsShareModalOpen(true);
//                 };
//             }

//             if (fullscreenBtn) {
//                 fullscreenBtn.onclick = async () => {
//                     const artifact = artifacts[currentIndex];
//                     setIsFullscreenOpen(artifact);
//                     setFullscreenOpen(true);
//                 };
//             }

//             if (playButtonOverlay) {
//                 playButtonOverlay.onclick = () => {
//                     setIsFullscreenOpen(artifacts[currentIndex]);
//                     setFullscreenOpen(true);
//                 };
//             }
//         }, 0);
//     };

//     const ClusterOffset = (cluster) => {
//         const overlay = new google.maps.OverlayView();
//         overlay.draw = function () {};
//         overlay.setMap(map);
//         const clusterPosition = cluster._position;
//         const point = overlay.getProjection().fromLatLngToDivPixel(clusterPosition);
//         if (!point) {
//             console.warn("Invalid cluster position");
//             return new google.maps.Size(0, 0);
//         }
//         const quadrant = `${point.y > 0 ? "b" : "t"}${point.x < 0 ? "l" : "r"}`;
//         console.log("Cluster position", quadrant);
//         if (quadrant == "tr") {
//             return new google.maps.Size(-300, 490);
//         } else if (quadrant == "tl") {
//             return new google.maps.Size(300, 540);
//         } else if (quadrant == "br") {
//             return new google.maps.Size(-220, 80);
//         } else if (quadrant == "bl") {
//             return new google.maps.Size(200, 40);
//         }
//     };

//     const createInfoWindow = async () => {
//         const initialContent = await generateContentWithImage(
//             artifacts[currentIndex],
//             true,
//             currentIndex,
//             artifacts.length,
//             favouriteArtifactsRef,
//             cachedSrc,
//             timezone,
//         );
//         const pixelOffset = ClusterOffset(cluster);
//         if (artifactInfowWindow) artifactInfowWindow.close();
//         if (currentClusterInfoWindow) currentClusterInfoWindow.close();
//         if (!currentClusterInfoWindow) {
//             currentClusterInfoWindow = new google.maps.InfoWindow({
//                 content: initialContent,
//                 pixelOffset: pixelOffset,
//             });
//         } else {
//             currentClusterInfoWindow.setContent(initialContent);
//             currentClusterInfoWindow.setOptions({ pixelOffset });
//         }
//         currentClusterInfoWindow.setPosition(cluster._position);
//         currentClusterInfoWindow.open(map);
//         addNavigationListeners();
//     };

//     createInfoWindow();
// };

//this is for setting the info window offset only

const handleInfoWindowOffset = (markerOrFeature, type, google, map) => {
    console.log("Setting info window location");
    const overlay = new google.maps.OverlayView();
    overlay.draw = function () { };
    overlay.setMap(map);

    let point = null;
    let isFeature = false;

    if (markerOrFeature instanceof google.maps.Marker) {
        point = overlay.getProjection().fromLatLngToDivPixel(markerOrFeature.getPosition());
    } else if (markerOrFeature instanceof google.maps.Data.Feature) {
        const coords = markerOrFeature.getGeometry().getArray();
        const midPoint = coords[Math.floor(coords.length / 2)];
        point = overlay.getProjection().fromLatLngToDivPixel(midPoint);
        //eslint-disable-next-line no-unused-vars
        isFeature = true;
    } else {
        console.warn("Invalid argument for marker or feature");
        return;
    }

    var quadrant = "";
    quadrant += point.y > 0 ? "b" : "t";
    quadrant += point.x < 0 ? "l" : "r";
    // console.log("Quadrant->", quadrant);
    var offset;
    if (quadrant === "tr") {
        offset = new google.maps.Size(
            type === "coordinate" ? -150 : type === "artifact" ? -370 : type === "audio" ? -390 : type === "ais" ? -150 : 0,
            type === "coordinate" ? 100 : type === "artifact" ? 370 : type === "audio" ? 250 : type === "ais" ? 200 : 0,
        );
    } else if (quadrant === "tl") {
        offset = new google.maps.Size(
            type === "coordinate" ? 150 : type === "artifact" ? 300 : type === "audio" ? 320 : type === "ais" ? 150 : 0,
            type === "coordinate" ? 100 : type === "artifact" ? 370 : type === "audio" ? 250 : type === "ais" ? 200 : 0,
        );
    } else if (quadrant === "br") {
        offset = new google.maps.Size(
            type === "coordinate" ? -130 : type === "artifact" ? -340 : type === "audio" ? -250 : type === "ais" ? -130 : 0,
            type === "coordinate" ? 0 : type === "artifact" ? 20 : type === "audio" ? 50 : type === "ais" ? 0 : 0,
        );
    } else if (quadrant === "bl") {
        offset = new google.maps.Size(
            type === "coordinate" ? 130 : type === "artifact" ? 300 : type === "audio" ? 130 : type === "ais" ? 130 : 0,
            type === "coordinate" ? 0 : type === "artifact" ? -40 : type === "audio" ? 50 : type === "ais" ? 0 : 0,
        );
    }
    // type === 'artifact' ? artifactInfowWindow.setOptions({ pixelOffset: offset }) : infoWindow.setOptions({ pixelOffset: offset });
    return offset;
};

// This is used for dashed the polyline on the land path according to the coordinates timestamp
const getFormattedPath = (allCoordinatesForPolyline, showFilteredCoordinates) => {
    if (!allCoordinatesForPolyline || allCoordinatesForPolyline.length === 0) {
        // console.warn("No coordinates provided");
        return [];
    }

    const TWO_HOURS = 2 * 60 * 60 * 1000; // 2 hours in milliseconds
    const formattedPaths = []; // Array to store formatted paths
    let currSegment = []; // Holds the current segment of coordinates
    let prevPoint = null;

    allCoordinatesForPolyline.forEach((coord, i) => {
        const currPoint = {
            lat: coord[2],
            lng: coord[3],
            timestamp: coord[1],
            isStationary: coord[4],
        };
        if (i === 0) {
            currSegment.push(currPoint);
            prevPoint = currPoint;
            return;
        }

        // Handle last coordinate
        if (allCoordinatesForPolyline.length === i + 1) {
            formattedPaths.push({
                path: [currSegment[currSegment.length - 1], currPoint],
                polylineType: defaultValues.polylineTypes.SOLID,
            });
            prevPoint = currPoint;
            return;
        }

        // Handle stationary points
        if (!showFilteredCoordinates && prevPoint.isStationary && currPoint.isStationary) {
            prevPoint.stationary_connected = true;
            prevPoint = currPoint;
            return;
        }

        // Handle points connected to stationary points
        if (!showFilteredCoordinates && prevPoint.stationary_connected) {
            formattedPaths.push({
                path: [prevPoint, currPoint],
                polylineType: defaultValues.polylineTypes.SOLID,
            });
            currSegment = [currPoint];
            prevPoint = currPoint;
            return;
        }

        const timeDifference = new Date(currPoint.timestamp) - new Date(prevPoint.timestamp);

        if (timeDifference > TWO_HOURS) {
            if (currSegment.length > 0) {
                formattedPaths.push({
                    path: [...currSegment],
                    polylineType: defaultValues.polylineTypes.SOLID,
                });
            }
            formattedPaths.push({
                path: [prevPoint, currPoint],
                polylineType: defaultValues.polylineTypes.DASHED,
            });
            currSegment = [currPoint];
        } else {
            // Add point to current segment
            currSegment.push(currPoint);
        }

        prevPoint = currPoint;
    });

    // Add the last segment if it exists
    if (currSegment.length > 0) {
        formattedPaths.push({
            path: [...currSegment],
            polylineType: defaultValues.polylineTypes.SOLID,
        });
    }

    return formattedPaths;
};

// This is for optimize or reduce the coordinates according to set datapointsDistance
const optimizeCoordinates = (coordinates, google, datapointsDistance) => {
    console.log("optimize the coordinates");
    const newCoordinates = [];
    coordinates.forEach((pointB, i) => {
        if (newCoordinates.length === 0 || i === coordinates.length - 1) return newCoordinates.push(pointB);
        const pointA = newCoordinates[newCoordinates.length - 1];
        const distance = google.maps.geometry.spherical.computeDistanceBetween(
            new google.maps.LatLng(pointA[2], pointA[3]),
            new google.maps.LatLng(pointB[2], pointB[3]),
        );
        if (distance > datapointsDistance) newCoordinates.push(pointB);
    });

    return newCoordinates;
};

const intervalizeCoordinates = (coordinates, intervalMins) => {
    let prev = null;
    return coordinates.filter((curr, i) => {
        if (i === 0) {
            prev = curr;
            return true;
        } else if (coordinates.length === i + 1) return true;

        const prevTs = new Date(prev[1]).getTime();
        const currTs = new Date(curr[1]).getTime();
        const timeDiffMins = (currTs - prevTs) / (1000 * 60);
        if (timeDiffMins >= intervalMins) {
            prev = curr;
            return true;
        }
        return false;
    });
};

// This is for draw the coordinates on the Map
const drawCoordinates = (
    user,
    filteredCoordinates,
    prevFilteredCoordinates,
    flightPaths,
    setFlightPaths,
    dataSetLayers,
    setDataSetLayers,
    vessels,
    coordinates,
    journeyStart,
    journeyEnd,
    map,
    infoWindow,
    datapointsDistance,
    showDatapoints,
    google,
    timezone,
    showFilteredCoordinates,
    // fromDate,
    // toDate,
    // unfilteredDataLayer,
    // setUnfilteredDataLayer,
    // showCoordinatesPoints,
    // devMode,
    interval,
    vesselInfo,
) => {
    const tts = Date.now();
    // const uuid = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const skipVessels = [];

    // console.log("[drawCoordinates] filteredCoordinates length", Object.keys(filteredCoordinates).length);

    // Add the vessel coordinates to skipVessels if they have not changed to avoid redrawing.
    Object.keys(filteredCoordinates).forEach((vesselId) => {
        if (
            flightPaths[vesselId] &&
            prevFilteredCoordinates.current[vesselId] &&
            prevFilteredCoordinates.current[vesselId] === filteredCoordinates[vesselId].length
        )
            skipVessels.push(vesselId);
    });

    // console.log("[drawCoordinates] flightPaths length", Object.keys(flightPaths).length);

    // Remove those flight paths that are not in skipVessels to redraw their updated coordinates.
    Object.keys(flightPaths)
        .filter((key) => flightPaths[key])
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            flightPaths[vesselId].forEach((flightPath) => {
                if (flightPath) {
                    flightPath.setMap(null);
                }
            });
            flightPaths[vesselId] = null; // clear the object reference
        });

    // console.log("[drawCoordinates] dataSetLayers length", Object.keys(dataSetLayers).length);

    // Clear existing data layers
    Object.keys(dataSetLayers)
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            const dataLayer = dataSetLayers[vesselId];
            dataLayer.forEach((feature) => dataLayer.remove(feature));
            dataLayer.setMap(null);
            delete dataSetLayers[vesselId];
        });

    // if (devMode) {
    //     Object.keys(unfilteredDataLayer)
    //         .filter((vesselName) => !skipVessels.includes(vesselName))
    //         .forEach((vesselName) => {
    //             const dataLayer = unfilteredDataLayer[vesselName];
    //             dataLayer.forEach((feature) => dataLayer.remove(feature));
    //             dataLayer.setMap(null);
    //             delete unfilteredDataLayer[vesselName];
    //         });
    // }

    if (!map || Object.keys(filteredCoordinates).length === 0) return console.warn("[drawCoordinates] Polyline could not be drawn");

    const journeyStartValue = dayjs(journeyStart).valueOf();
    const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

    Object.keys(filteredCoordinates)
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            const vesselCoordinates = filteredCoordinates[vesselId];
            const drawPolyline = coordinates[vesselId];

            const filterDrawPolyline = drawPolyline?.filter((c) => {
                const currentTimestamp = new Date(c[1]).getTime();
                return currentTimestamp >= journeyStartValue && currentTimestamp <= journeyEndValue;
            });

            const ts = Date.now();
            const formattedPaths = getFormattedPath(filterDrawPolyline, showFilteredCoordinates);
            console.log("[drawCoordinates]", vesselId, "time taken to get formatted paths", Date.now() - ts, "ms");
            if (vesselCoordinates.length > 0) {
                const CompleteFlightPaths = formattedPaths.map(({ path, polylineType }) => {
                    const flightPath = new google.maps.Polyline({
                        path: path.map((coord) => ({ lat: coord.lat, lng: coord.lng })),
                        geodesic: true,
                        strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                        strokeOpacity: polylineType == defaultValues.polylineTypes.SOLID ? 1.0 : 0, // Hide the base line for dashed lines
                        strokeWeight: 2,
                        zIndex: -999,
                        icons:
                            polylineType == defaultValues.polylineTypes.DASHED
                                ? [
                                    {
                                        icon: {
                                            path: "M 0,-2 0,2", // Custom dash path
                                            strokeOpacity: 1,
                                            scale: 2, // Size of the dashes
                                            strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000", // Match polyline color
                                        },
                                        offset: "0", // Start position of dashes
                                        repeat: "20px", // Length of each dash and gap
                                    },
                                ]
                                : polylineType === defaultValues.polylineTypes.DOTTED
                                    ? [
                                        {
                                            icon: {
                                                path: google.maps.SymbolPath.CIRCLE,
                                                fillOpacity: 1,
                                                scale: 2, // Dot size
                                                strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                                                fillColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                                            },
                                            offset: "0",
                                            repeat: "10px", // Distance between dots
                                        },
                                    ]
                                    : null,
                    });

                    flightPath.setMap(map);
                    return flightPath;
                });
                // if (devMode && showCoordinatesPoints && fromDate && toDate) {
                //     const from = new Date(fromDate);
                //     const to = new Date(toDate);
                //     const unfilteredCoordinatesDataLayer = new google.maps.Data();
                //     const unfilteredGeoJson = {
                //         type: "FeatureCollection",
                //         features: drawPolyline
                //             .filter((coord) => {
                //                 const coordTime = new Date(coord.timestamp).getTime();
                //                 return coordTime >= from.getTime() && coordTime <= to.getTime();
                //             })
                //             .map((coord, index) => ({
                //                 type: "Feature",
                //                 geometry: {
                //                     type: "Point",
                //                     coordinates: [coord.lng, coord.lat],
                //                 },
                //                 properties: {
                //                     isStationary: coord.isStationary,
                //                     timestamp: coord.timestamp,
                //                     index: index,
                //                     coordinate: coord,
                //                 },
                //             })),
                //     };

                //     unfilteredCoordinatesDataLayer.addGeoJson(unfilteredGeoJson);
                //     unfilteredCoordinatesDataLayer.setStyle((feature) => {
                //         const isStationary = feature.getProperty("isStationary");
                //         return {
                //             icon: {
                //                 path: google.maps.SymbolPath.CIRCLE,
                //                 scale: 3,
                //                 fillColor: isStationary ? "#FF0000" : "#0000FF",
                //                 fillOpacity: 0.8,
                //                 strokeColor: "#FFFFFF",
                //                 strokeWeight: 1,
                //             },
                //         };
                //     });

                //     unfilteredCoordinatesDataLayer.setMap(map);

                //     unfilteredCoordinatesDataLayer.addListener("mouseover", async (event) => {
                //         const feature = event.feature;
                //         const coordinate = feature.getProperty("coordinate");
                //         const isStationary = feature.getProperty("isStationary");

                //         const content = `
                //             <div style="color: #fff; align-items: center; padding: 15px;">
                //                 <strong>${vessels.find((v) => v.id === vesselId)?.name}</strong><br/>
                //                 <strong>Location:</strong> ${displayCoordinates([coordinate.lng, coordinate.lat], !!user?.use_MGRS)}<br/>
                //                 <strong>Time:</strong> ${dayjs(coordinate.timestamp).tz(timezone).format(defaultValues.dateTimeFormat())}<br/>
                //                 <strong>Status:</strong> ${isStationary ? "Stationary" : "Moving"}<br/>
                //                 <style>
                //                     .gm-style-iw-chr {
                //                         display: none !important;
                //                     }
                //                     .gm-style-iw-tc {
                //                         display: none !important;
                //                     }
                //                     .gm-style .gm-style-iw-c  {
                //                         background-color: #343B44 !important;
                //                         outline: none;
                //                         padding: 0;
                //                     }
                //                     .gm-style .gm-style-iw-d {
                //                         overflow:auto !important;
                //                     }
                //                     p {
                //                         margin: 0;
                //                         color:white
                //                     }
                //                     strong {
                //                         color:white
                //                     }
                //                 </style>
                //             </div>
                //         `;

                //         infoWindow.setContent(content);
                //         infoWindow.setPosition(event.latLng);
                //         infoWindow.open(map);
                //     });

                //     unfilteredCoordinatesDataLayer.addListener("mouseout", () => {
                //         infoWindow.close();
                //     });

                //     setUnfilteredDataLayer((prev) => ({
                //         ...prev,
                //         [vesselId]: unfilteredCoordinatesDataLayer,
                //     }));
                // }

                setFlightPaths((v) => ({
                    ...v,
                    [vesselId]: CompleteFlightPaths,
                }));
                // const tsIntervalize = Date.now();
                const filteredVesselCoordinates = intervalizeCoordinates(vesselCoordinates, interval);
                // console.log('[intervalizeCoordinates] time taken', Date.now() - tsIntervalize, 'ms')
                const optCoords = optimizeCoordinates(filteredVesselCoordinates, google, datapointsDistance).filter((_, i, self) =>
                    showDatapoints ? true : i === 0 || i === self.length - 1,
                );

                // console.log("[drawCoordinates] optCoords", optCoords);

                const geoJsonData = {
                    type: "FeatureCollection",
                    features: optCoords.map((c, i, self) => ({
                        type: "Feature",
                        geometry: {
                            type: "LineString",
                            coordinates: [
                                [c[3], c[2]],
                                [c[3], c[2]],
                            ],
                        },
                        properties: {
                            rotation:
                                i < self.length - 1
                                    ? c[4]
                                        ? google.maps.geometry.spherical.computeHeading(
                                            new google.maps.LatLng(c[2], c[3]),
                                            new google.maps.LatLng(self[i + 1][2], self[i + 1][3]),
                                        )
                                        : c[5]
                                    : 0,
                            coordinate: c,
                            isFirstCoordinate: i === 0,
                        },
                    })),
                };

                const dataLayer = new google.maps.Data();

                dataLayer.addGeoJson(geoJsonData);

                dataLayer.setStyle((feature) => ({
                    zIndex: 999,
                    strokeWeight: 15,
                    fillColor: "transparent",
                    strokeColor: "transparent",
                    icons: [
                        {
                            icon: {
                                path: feature.getProperty("isFirstCoordinate")
                                    ? google.maps.SymbolPath.CIRCLE
                                    : google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                                scale: feature.getProperty("isFirstCoordinate") ? 4 : 2,
                                strokeColor: "#000000",
                                strokeWeight: feature.getProperty("isFirstCoordinate") ? 1 : 0.5,
                                rotation: feature.getProperty("isFirstCoordinate") ? 0 : feature.getProperty("rotation"),
                                offset: "0%",
                                anchor: feature.getProperty("isFirstCoordinate") ? new google.maps.Point(0, 0) : new google.maps.Point(0, 3),
                                fillColor: defaultValues.polylineColors[vesselId] || "#0000FF",
                                fillOpacity: 1,
                            },
                        },
                    ],
                }));

                dataLayer.setMap(map);

                const getContent = (coordinate, isPinned) => {
                    let vesselTimezone = timezone;
                    let showTimezoneFormat = "";
                    const vessel = vesselInfo && vesselInfo.find((v) => v.vessel_id === vesselId);
                    if (vessel && vessel.timezone) {
                        vesselTimezone = vessel.timezone;
                        showTimezoneFormat = simplifyTimezone(vesselTimezone);
                    }
                    return `
                        <div style="color: #fff; align-items: center; padding: 15px;">
                            <strong>${vessels.find((v) => v.id === vesselId)?.name}</strong><br/>
                            <strong>Location:</strong> ${displayCoordinates([coordinate[3], coordinate[2]], !!user?.use_MGRS)}<br/>
                            <strong>Time:</strong> ${dayjs(coordinate[1]).tz(vesselTimezone).format(userValues.dateTimeFormat(user))} ${showTimezoneFormat} <br/>
                            <style>
                                .gm-style-iw-chr {
                                    display: ${isPinned ? 'flex' : 'none'} !important; justify-content: end; padding: 0 20px !important;
                                }
                                .gm-ui-hover-effect { width: 30px !important; height: 30px !important; }
                                .gm-ui-hover-effect span { width: 20px !important; height: 20px !important; background-color: #FFFFFF !important; }
                                .gm-style-iw-tc { display: none !important; }
                                .gm-style .gm-style-iw-c  {
                                    background-color: #343B44 !important;
                                    outline: none;
                                    padding: 0;
                                }
                                .gm-style .gm-style-iw-d {
                                    overflow:auto !important;
                                    padding: 0 !important;
                                    user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                                }
                                .gm-style .gm-style-iw-d * {
                                    user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                    -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                                }
                                p {
                                    margin: 0;
                                    color:white
                                }
                                strong {
                                    color:white
                                }
                            </style>
                        </div>
                    `;
                };

                const openInfoWindow = async (event, isPinned) => {
                    const feature = event.feature;
                    const coordinate = feature.getProperty("coordinate");
                    const content = getContent(coordinate, isPinned);
                    infoWindow.setContent(content);
                    infoWindow.setPosition(event.latLng);
                    infoWindow.open(map);
                    const offset = await handleInfoWindowOffset(feature, "coordinate", google, map);
                    infoWindow.setOptions({ pixelOffset: offset });
                };

                dataLayer.addListener("mouseover", async (event) => {
                    if (!infoWindow._isPinned) {
                        await openInfoWindow(event, false);
                    }
                });

                dataLayer.addListener("mouseout", () => {
                    if (!infoWindow._isPinned) {
                        infoWindow.close();
                    }
                });

                dataLayer.addListener("click", async (event) => {
                    infoWindow._isPinned = true;
                    await openInfoWindow(event, true);
                    google.maps.event.addListenerOnce(infoWindow, "closeclick", () => {
                        infoWindow._isPinned = false;
                    });
                });

                setDataSetLayers((prev) => ({
                    ...prev,
                    [vesselId]: dataLayer,
                }));
            }
        });
    console.log("[drawCoordinates] time taken to draw coordinates", Date.now() - tts, "ms");
};

const drawPolylines = ({ filteredCoordinates, prevFilteredCoordinates, flightPaths, map,
    journeyStart, journeyEnd, showFilteredCoordinates, coordinates, google, setFlightPaths }) => {
    const skipVessels = [];

    // Add the vessel coordinates to skipVessels if they have not changed to avoid redrawing.
    Object.keys(filteredCoordinates).forEach((vesselId) => {
        if (
            flightPaths[vesselId] &&
            prevFilteredCoordinates.current[vesselId] &&
            prevFilteredCoordinates.current[vesselId] === filteredCoordinates[vesselId].length
        )
            skipVessels.push(vesselId);
    });

    // Remove those flight paths that are not in skipVessels to redraw their updated coordinates.
    Object.keys(flightPaths)
        .filter((key) => flightPaths[key])
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            flightPaths[vesselId].forEach((flightPath) => {
                if (flightPath) {
                    flightPath.setMap(null);
                }
            });
            flightPaths[vesselId] = null; // clear the object reference
        });

    // Do not touch data point layers here; handled separately by drawDataPoints
    if (!map || Object.keys(filteredCoordinates).length === 0) return console.warn("[drawCoordinates] Polyline could not be drawn");

    const journeyStartValue = dayjs(journeyStart).valueOf();
    const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

    Object.keys(filteredCoordinates)
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            const vesselCoordinates = filteredCoordinates[vesselId];
            const drawPolyline = coordinates[vesselId];

            const filterDrawPolyline = drawPolyline?.filter((c) => {
                const currentTimestamp = new Date(c[1]).getTime();
                return currentTimestamp >= journeyStartValue && currentTimestamp <= journeyEndValue;
            });

            const ts = Date.now();
            const formattedPaths = getFormattedPath(filterDrawPolyline, showFilteredCoordinates);
            console.log("[drawPolyline]", vesselId, "time taken to get formatted paths", Date.now() - ts, "ms");
            if (vesselCoordinates.length > 0) {
                const CompleteFlightPaths = formattedPaths.map(({ path, polylineType }) => {
                    const flightPath = new google.maps.Polyline({
                        path: path.map((coord) => ({ lat: coord.lat, lng: coord.lng })),
                        geodesic: true,
                        strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                        strokeOpacity: polylineType == defaultValues.polylineTypes.SOLID ? 1.0 : 0, // Hide the base line for dashed lines
                        strokeWeight: 2,
                        zIndex: -999,
                        icons:
                            polylineType == defaultValues.polylineTypes.DASHED
                                ? [
                                    {
                                        icon: {
                                            path: "M 0,-2 0,2", // Custom dash path
                                            strokeOpacity: 1,
                                            scale: 2, // Size of the dashes
                                            strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000", // Match polyline color
                                        },
                                        offset: "0", // Start position of dashes
                                        repeat: "20px", // Length of each dash and gap
                                    },
                                ]
                                : polylineType === defaultValues.polylineTypes.DOTTED
                                    ? [
                                        {
                                            icon: {
                                                path: google.maps.SymbolPath.CIRCLE,
                                                fillOpacity: 1,
                                                scale: 2, // Dot size
                                                strokeColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                                                fillColor: defaultValues.polylineColors[vesselId] || "#FF0000",
                                            },
                                            offset: "0",
                                            repeat: "10px", // Distance between dots
                                        },
                                    ]
                                    : null,
                    });

                    flightPath.setMap(map);
                    return flightPath;
                });

                setFlightPaths((v) => ({
                    ...v,
                    [vesselId]: CompleteFlightPaths,
                }));
            }
        });

}
const drawDataPoints = ({
    user,
    filteredCoordinates,
    prevFilteredCoordinates,
    dataSetLayers,
    setDataSetLayers,
    map,
    infoWindow,
    datapointsDistance,
    showDatapoints,
    google,
    timezone,
    interval,
    vesselInfo,
    vessels,
}) => {
    const tts = Date.now();
    const skipVessels = [];

    // Add the vessel coordinates to skipVessels if they have not changed to avoid redrawing.
    Object.keys(filteredCoordinates).forEach((vesselId) => {
        if (
            prevFilteredCoordinates.current[vesselId] &&
            prevFilteredCoordinates.current[vesselId] === filteredCoordinates[vesselId].length
        )
            skipVessels.push(vesselId);
    });

    // Clear existing data layers only for updated vessels
    Object.keys(dataSetLayers)
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            const dataLayer = dataSetLayers[vesselId];
            dataLayer.forEach((feature) => dataLayer.remove(feature));
            dataLayer.setMap(null);
            delete dataSetLayers[vesselId];
        });

    if (!map || Object.keys(filteredCoordinates).length === 0) return console.warn("[drawDataPoints] No coordinates to draw");

    Object.keys(filteredCoordinates)
        .filter((vesselId) => !skipVessels.includes(vesselId))
        .forEach((vesselId) => {
            const vesselCoordinates = filteredCoordinates[vesselId];
            if (vesselCoordinates.length === 0) return;

            const filteredVesselCoordinates = intervalizeCoordinates(vesselCoordinates, interval);
            const optCoords = optimizeCoordinates(filteredVesselCoordinates, google, datapointsDistance).filter((_, i, self) =>
                showDatapoints ? true : i === 0 || i === self.length - 1,
            );

            const geoJsonData = {
                type: "FeatureCollection",
                features: optCoords.map((c, i, self) => ({
                    type: "Feature",
                    geometry: {
                        type: "LineString",
                        coordinates: [
                            [c[3], c[2]],
                            [c[3], c[2]],
                        ],
                    },
                    properties: {
                        rotation:
                            i < self.length - 1
                                ? c[4]
                                    ? google.maps.geometry.spherical.computeHeading(
                                        new google.maps.LatLng(c[2], c[3]),
                                        new google.maps.LatLng(self[i + 1][2], self[i + 1][3]),
                                    )
                                    : c[5]
                                : 0,
                        coordinate: c,
                        isFirstCoordinate: i === 0,
                    },
                })),
            };

            const dataLayer = new google.maps.Data();
            dataLayer.addGeoJson(geoJsonData);
            dataLayer.setStyle((feature) => ({
                zIndex: 999,
                strokeWeight: 15,
                fillColor: "transparent",
                strokeColor: "transparent",
                icons: [
                    {
                        icon: {
                            path: feature.getProperty("isFirstCoordinate")
                                ? google.maps.SymbolPath.CIRCLE
                                : google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                            scale: feature.getProperty("isFirstCoordinate") ? 4 : 2,
                            strokeColor: "#000000",
                            strokeWeight: feature.getProperty("isFirstCoordinate") ? 1 : 0.5,
                            rotation: feature.getProperty("isFirstCoordinate") ? 0 : feature.getProperty("rotation"),
                            offset: "0%",
                            anchor: feature.getProperty("isFirstCoordinate") ? new google.maps.Point(0, 0) : new google.maps.Point(0, 3),
                            fillColor: defaultValues.polylineColors[vesselId] || "#0000FF",
                            fillOpacity: 1,
                        },
                    },
                ],
            }));

            dataLayer.setMap(map);
            
            const getContent = (coordinate, isPinned) => {
                let vesselTimezone = timezone;
                let showTimezoneFormat = "";
                const vessel = vesselInfo && vesselInfo.find((v) => v.vessel_id === vesselId);
                if (vessel && vessel.timezone) {
                    vesselTimezone = vessel.timezone;
                    showTimezoneFormat = simplifyTimezone(vesselTimezone);
                }
                return `
                    <div style="color: #fff; align-items: center; padding: 15px;">
                        <strong>${vessels.find((v) => v.id === vesselId)?.name}</strong><br/>
                        <strong>Location:</strong> ${displayCoordinates([coordinate[3], coordinate[2]], !!user?.use_MGRS)}<br/>
                        <strong>Time:</strong> ${dayjs(coordinate[1]).tz(vesselTimezone).format(userValues.dateTimeFormat(user))} ${showTimezoneFormat} <br/>
                        <style>
                            .gm-style-iw-chr { display: ${isPinned ? 'flex' : 'none'} !important; justify-content: end; padding: 0 20px !important; }
                            .gm-ui-hover-effect { width: 30px !important; height: 30px !important; }
                            .gm-ui-hover-effect span { width: 20px !important; height: 20px !important; background-color: #FFFFFF !important; }
                            .gm-style-iw-tc { display: none !important; }
                            .gm-style .gm-style-iw-c  { background-color: #343B44 !important; outline: none; padding: 0; }
                            .gm-style .gm-style-iw-d { 
                                overflow:auto !important;
                                user-select: ${isPinned ? 'text' : 'none'} !important;
                                -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                            }
                            .gm-style .gm-style-iw-d * {
                                user-select: ${isPinned ? 'text' : 'none'} !important;
                                -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                            }
                            p { margin: 0; color:white }
                            strong { color:white }
                        </style>
                    </div>
                `;
            };

            const openInfoWindow = async (event, isPinned) => {
                const feature = event.feature;
                const coordinate = feature.getProperty("coordinate");
                const content = getContent(coordinate, isPinned);
                infoWindow.setContent(content);
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
                const offset = await handleInfoWindowOffset(feature, "coordinate", google, map);
                infoWindow.setOptions({ pixelOffset: offset });
            };

            dataLayer.addListener("mouseover", async (event) => {
                if (!infoWindow._isPinned) {
                    await openInfoWindow(event, false);
                }
            });

            dataLayer.addListener("mouseout", () => {
                if (!infoWindow._isPinned) {
                    infoWindow.close();
                }
            });

            dataLayer.addListener("click", async (event) => {
                infoWindow._isPinned = true;
                await openInfoWindow(event, true);
                google.maps.event.addListenerOnce(infoWindow, "closeclick", () => {
                    infoWindow._isPinned = false;
                });
            });

            setDataSetLayers((prev) => ({
                ...prev,
                [vesselId]: dataLayer,
            }));
        });

    console.log("[drawDataPoints] time taken", Date.now() - tts, "ms");
};
// This is for drawing AIS data points on the map
const drawAisData = ({
    user,
    filteredAisData,
    prevFilteredAisData,
    aisDataLayers,
    setAisDataLayers,
    // aisData,
    // vessels,
    map,
    infoWindow,
    google,
    timezone,
    vesselInfo,
    showAisData,
}) => {
    const tts = Date.now();
    const skipVessels = [];
    console.log("[drawAisData] filteredAisData length", Object.keys(filteredAisData).length);

    // If showAisData is false, clear all existing AIS data layers and return
    if (!showAisData) {
        Object.keys(aisDataLayers).forEach((vesselName) => {
            const dataLayer = aisDataLayers[vesselName];
            dataLayer.forEach((feature) => dataLayer.remove(feature));
            dataLayer.setMap(null);
            delete aisDataLayers[vesselName];
        });
        setAisDataLayers({});
        return;
    }

    // If showAisData is false, clear all existing AIS data layers and return
    // if (!showAisData) {
    //     if (aisLayer) {
    //         aisLayer.setMap(null);
    //         aisLayer = null;
    //     }
    //     setAisDataLayers(null);
    //     return;
    // }

    // Clear existing AIS data layers if no data to show
    if (Object.keys(filteredAisData).length === 0) {
        Object.keys(aisDataLayers).forEach((vesselName) => {
            const dataLayer = aisDataLayers[vesselName];
            dataLayer.forEach((feature) => dataLayer.remove(feature));
            dataLayer.setMap(null);
            delete aisDataLayers[vesselName];
        });
        setAisDataLayers({});
        return;
    }

    // Add the vessel AIS data to skipVessels if they have not changed to avoid redrawing.
    Object.keys(filteredAisData).forEach((vesselName) => {
        if (
            aisDataLayers[vesselName] &&
            prevFilteredAisData.current[vesselName] &&
            prevFilteredAisData.current[vesselName].length === filteredAisData[vesselName].length
        )
            skipVessels.push(vesselName);
    });
    // console.log("[drawAisData] aisDataLayers length", Object.keys(aisDataLayers).length);
    console.log("[drawAisData] skipVessels length", skipVessels.length);

    // Clear existing AIS data layers
    Object.keys(aisDataLayers)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            const dataLayer = aisDataLayers[vesselName];
            dataLayer.forEach((feature) => dataLayer.remove(feature));
            dataLayer.setMap(null);
            delete aisDataLayers[vesselName];
        });

    // Clear existing AIS data layers
    // if (aisLayer) {
    //     aisLayer.setMap(null);
    // }

    // if (!map || Object.keys(filteredAisData).length === 0) return console.warn("[drawAisData] AIS data could not be drawn");
    if (!map) return console.warn("[drawAisData] AIS data could not be drawn");

    // Object.keys(aisData)
    // .filter((vesselName) => !skipVessels.includes(vesselName))
    // .forEach((vesselName) => {
    //     const vesselAisData = filteredAisData[vesselName];
    // if (vesselAisData.length === 0) return;
    // console.log("drawAisData: Per vessel", vesselAisData);
    const updatedAisDataLayers = {};
    // console.log("drawAisData: filteredAisData", filteredAisData);
    Object.keys(filteredAisData)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            const aisData = filteredAisData[vesselName];

            console.log("drawAisData: Per vessel", aisData);
            if (aisData.length === 0) return;

            const geoJsonData = {
                type: "FeatureCollection",
                features: aisData.map((aisPoint, i) => {
                    // console.log("Ais point", aisPoint.details.message.nav_heading_true)
                    return {
                        type: "Feature",
                        geometry: {
                            type: "LineString",
                            coordinates: [
                                [aisPoint.location.coordinates[0], aisPoint.location.coordinates[1]],
                                [aisPoint.location.coordinates[0], aisPoint.location.coordinates[1]],
                            ],
                        },
                        properties: {
                            aisPoint: aisPoint,
                            index: i,
                            // vesselName: vesselName,
                            rotation: aisPoint.details.message.nav_heading_true,
                        },
                    };
                }),
            };

            const aisLayer = new google.maps.Data();

            aisLayer.addGeoJson(geoJsonData);

            const SHIP_PATH_24 = "M12 2L16 5V18L12 16L8 18V5Z";
            // const fillColor = defaultValues.polylineColors[vesselName] || "#8B5CF6";
            function radianToDegree({ rotationInRadians }) {
                return rotationInRadians * (180 / Math.PI);
            }

            aisLayer.setStyle((feat) => {
                const direction = radianToDegree({ rotationInRadians: feat.getProperty("rotation") });
                const isCircle = direction === 0;
                const aisData = feat.getProperty("aisPoint");
                const fillColor = stringToColor(aisData.details.message.design_ais_ship_type_name) || "#8B5CF6";

                return {
                    zIndex: 999,
                    strokeWeight: 15,
                    fillColor: "transparent",
                    strokeColor: "transparent",
                    icons: [
                        {
                            icon: {
                                path: isCircle ? google.maps.SymbolPath.CIRCLE : SHIP_PATH_24,
                                scale: isCircle ? 3 : 0.9, // circle bigger for better click target
                                strokeColor: "#000000",
                                strokeWeight: 1,
                                fillColor,
                                fillOpacity: 1,
                                // Adjust anchor so center is clickable
                                anchor: isCircle
                                    ? new google.maps.Point(0, 0) // center for circle
                                    : new google.maps.Point(12, 12), // tip for ship
                                rotation: isCircle ? 0 : direction,
                            },
                        },
                    ],
                };
            });

            aisLayer.setMap(map);

            const getAisContent = (aisPoint, isPinned) => {
                let vesselTimezone = timezone;
                let showTimezoneFormat = "";
                const vessel = vesselInfo && vesselInfo.find((v) => v.vessel_id === aisPoint.onboard_vessel_id);
                if (vessel && vessel.timezone) {
                    vesselTimezone = vessel.timezone;
                    showTimezoneFormat = simplifyTimezone(vesselTimezone);
                }
                return `
                    <div style="color: #fff; align-items: center; padding: 15px;">
                       ${aisPoint.metadata.mmsi ? `<strong>MMSI:</strong> ${aisPoint.metadata.mmsi}<br/>` : ""}
                        ${aisPoint.name ? `<strong>Name:</strong> ${aisPoint.name}<br/>` : ""}
                        ${aisPoint.details.message.sensor_ais_class ? `<strong>AIS Ship Message Class:</strong> ${aisPoint.details.message.sensor_ais_class}<br/>` : ""}
                        ${aisPoint.details.message.nav_state ? `<strong>AIS Ship  State :</strong> ${aisPoint.details.message.nav_state}<br/>` : ""}
                        ${aisPoint.details.message.design_ais_ship_type_name ? `<strong>AIS Ship Type:</strong> ${aisPoint.details.message.design_ais_ship_type_name}<br/>` : ""}
                        ${aisPoint.details.message.design_length_type ? `<strong>Ship Length Type:</strong> ${aisPoint.details.message.design_length_type}<br/>` : ""}
                        <strong>Location:</strong> ${displayCoordinates(aisPoint.location.coordinates, !!user?.use_MGRS)}<br/>
                        ${aisPoint.details.message.design_beam ? `<strong>Ship Beam:</strong> ${aisPoint.details.message.design_beam}<br/>` : ""}
                        ${aisPoint.details.message.design_length ? `<strong>Ship Length:</strong> ${aisPoint.details.message.design_length}<br/>` : ""}
                        ${aisPoint.details.message.nav_speed_over_ground ? `<strong>Speed over ground:</strong> ${aisPoint.details.message.nav_speed_over_ground}<br/>` : ""}
                        ${aisPoint.details.message.comm_callsign_vhf ? `<strong>VHF Call Sign:</strong> ${aisPoint.details.message.comm_callsign_vhf}<br/>` : ""}
                        ${aisPoint.portal?.ais_info?.data?.portal_registry_country?.country_name ? `<strong>Registry Country:</strong> ${aisPoint.portal.ais_info.data.portal_registry_country.country_name}<br/>` : ""}
                        <strong>Timestamp:</strong> ${dayjs(aisPoint.timestamp).tz(vesselTimezone).format(userValues.dateTimeFormat(user))} ${showTimezoneFormat}<br/>

                        <style>
                            .gm-style-iw-chr {
                                display: ${isPinned ? 'flex' : 'none'} !important; justify-content: end; padding: 0 20px !important;
                            }
                            .gm-ui-hover-effect { width: 30px !important; height: 30px !important; }
                            .gm-ui-hover-effect span { width: 20px !important; height: 20px !important; background-color: #FFFFFF !important; }
                            .gm-style-iw-tc {
                                display: none !important;
                            }
                            .gm-style .gm-style-iw-c  {
                                background-color: #343B44 !important;
                                outline: none;
                                padding: 0;
                            }
                            .gm-style .gm-style-iw-d {
                                overflow:auto !important;
                                user-select: ${isPinned ? 'text' : 'none'} !important;
                                -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                            }
                            .gm-style .gm-style-iw-d * {
                                user-select: ${isPinned ? 'text' : 'none'} !important;
                                -webkit-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -moz-user-select: ${isPinned ? 'text' : 'none'} !important;
                                -ms-user-select: ${isPinned ? 'text' : 'none'} !important;
                            }
                            p {
                                margin: 0;
                                color:white
                            }
                            strong {
                                color:white
                            }
                        </style>
                    </div>
                `;
            };

            const openAisInfoWindow = async (event, isPinned) => {
                const feature = event.feature;
                const aisPoint = feature.getProperty("aisPoint");
                const content = getAisContent(aisPoint, isPinned);
                infoWindow.setContent(content);
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
                const offset = await handleInfoWindowOffset(feature, "ais", google, map);
                infoWindow.setOptions({ pixelOffset: offset });
            };

            aisLayer.addListener("mouseover", async (event) => {
                if (!infoWindow._isPinned) {
                    await openAisInfoWindow(event, false);
                }
            });

            aisLayer.addListener("mouseout", () => {
                if (!infoWindow._isPinned) {
                    infoWindow.close();
                }
            });

            aisLayer.addListener("click", async (event) => {
                infoWindow._isPinned = true;
                await openAisInfoWindow(event, true);
                google.maps.event.addListenerOnce(infoWindow, "closeclick", () => {
                    infoWindow._isPinned = false;
                });
            });

            updatedAisDataLayers[vesselName] = aisLayer;
        });

    // setAisDataLayers(updatedAisDataLayers);
    setAisDataLayers((prev) => ({
        ...prev,
        ...updatedAisDataLayers,
    }));

    console.log("[drawAisData] time taken to draw AIS data", Date.now() - tts, "ms");
};

// This is for drawing audio data points on the map
const drawAudioData = ({
    user,
    filteredAudioData,
    prevFilteredAudioData,
    audioDataMarkers,
    setAudioDataMarkers,
    audioClustererRef,
    map,
    google,
    showAudioData,
    audioInfoWindow,
    currentAudioClusterInfoWindow,
    artifactInfowWindow,
    currentClusterInfoWindow,
    infoWindow,
}) => {
    const tts = Date.now();
    const skipVessels = [];

    // console.log("[drawAudioData] filteredAudioData length", Object.keys(filteredAudioData).length);

    // If showAudioData is false, clear all existing audio data markers and return
    if (!showAudioData) {
        Object.keys(audioDataMarkers).forEach((vesselName) => {
            audioDataMarkers[vesselName].forEach((marker) => {
                marker.setMap(null);
                google.maps.event.clearInstanceListeners(marker);
            });
            audioDataMarkers[vesselName] = [];
        });
        setAudioDataMarkers({});
        if (audioClustererRef.current) {
            audioClustererRef.current.clearMarkers(); // Remove all markers from the clusterer
            audioClustererRef.current = null; // Reset the clusterer reference
        }
        return;
    }

    // Add the vessel audio data to skipVessels if they have not changed to avoid redrawing.
    Object.keys(filteredAudioData).forEach((vesselName) => {
        if (
            audioDataMarkers[vesselName] &&
            prevFilteredAudioData.current[vesselName] &&
            prevFilteredAudioData.current[vesselName] === filteredAudioData[vesselName].length
        )
            skipVessels.push(vesselName);
    });

    // console.log("[drawAudioData] audioDataMarkers length", Object.keys(audioDataMarkers).length);

    // Clear existing audio data markers
    Object.keys(audioDataMarkers)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            audioDataMarkers[vesselName].forEach((marker) => {
                marker.setMap(null);
                google.maps.event.clearInstanceListeners(marker);
            });
            audioDataMarkers[vesselName] = [];
        });

    if (!map || Object.keys(filteredAudioData).length === 0) return console.warn("[drawAudioData] Audio data could not be drawn");

    const clusterAudioMarkers = Object.keys(audioDataMarkers)
        .map((k) => audioDataMarkers[k])
        .flat();

    // Generate markers and add them to the `clusterMarkers` array
    // console.log(
    //     "[drawAudioData] drawing audio data",
    //     Object.keys(filteredAudioData).length,
    //     Object.keys(filteredAudioData).reduce((acc, curr) => acc + filteredAudioData[curr].length, 0),
    // );
    Object.keys(filteredAudioData)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            const vesselAudioData = filteredAudioData[vesselName];
            if (vesselAudioData.length === 0) return;
            const createAudioSVG = (color) => `
            <svg width="11" height="14" viewBox="0 0 11 14" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.83301 0.150391H6.11621C6.56579 0.150472 6.99475 0.31964 7.30957 0.620117L10.3604 3.53418V3.53516C10.675 3.83549 10.8496 4.24195 10.8496 4.66504V12.25C10.8496 13.1258 10.1018 13.8494 9.16699 13.8496H1.83301C0.898152 13.8494 0.150391 13.1258 0.150391 12.25V1.75C0.150391 0.87421 0.898152 0.150559 1.83301 0.150391ZM7.58203 6.53809C7.25693 6.32348 6.81134 6.39623 6.58203 6.71191C6.35071 7.03085 6.43315 7.46589 6.7627 7.68457V7.68555C7.40898 8.11661 7.8252 8.82593 7.8252 9.625C7.8252 10.3743 7.45717 11.0473 6.88086 11.4814L6.76367 11.5645C6.43354 11.7827 6.34971 12.221 6.58203 12.5381C6.81052 12.8497 7.25919 12.9273 7.58301 12.7109C8.59776 12.0347 9.27051 10.9042 9.27051 9.625C9.27051 8.34582 8.60018 7.21539 7.58203 6.53906V6.53809ZM4.11621 7.28711C3.93403 7.28717 3.75943 7.36544 3.64355 7.50488L2.90918 8.38086H2.29199C1.96372 8.38086 1.68359 8.63878 1.68359 8.96875V10.2812C1.68359 10.6112 1.96372 10.8691 2.29199 10.8691H2.90918L3.64258 11.7432V11.7441C3.75845 11.8842 3.93358 11.9628 4.11621 11.9629H4.125C4.45327 11.9629 4.7334 11.705 4.7334 11.375V7.875C4.7334 7.54503 4.45327 7.28711 4.125 7.28711H4.11621ZM6.59082 8.30762C6.41732 8.17555 6.21371 8.18422 6.05957 8.28027C5.91019 8.37339 5.80873 8.54654 5.80859 8.74414V10.5059C5.80873 10.7036 5.91021 10.876 6.05957 10.9688C6.21345 11.0642 6.41652 11.0724 6.58984 10.9424H6.59082C6.99473 10.6345 7.25391 10.1593 7.25391 9.625C7.25391 9.09074 6.99473 8.61546 6.59082 8.30762ZM5.80859 4.15625C5.80859 4.60916 6.18847 4.96272 6.64551 4.96289H9.69824L9.42773 4.7041L6.06152 1.49121L5.80859 1.24902V4.15625Z" fill="${color}" stroke="black" stroke-width="0.3"/>
            </svg>
            `;
            const fillColor = defaultValues.polylineColors[vesselName] || "#8B5CF6";
            const markerIcon = `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(createAudioSVG(fillColor))}`;

            const markers = vesselAudioData.map((audioPoint) => {
                const marker = new google.maps.Marker({
                    position: {
                        lat: audioPoint.host_location.coordinates[1],
                        lng: audioPoint.host_location.coordinates[0],
                    },
                    icon: {
                        // path: google.maps.SymbolPath.CIRCLE,
                        url: markerIcon,
                        scaledSize: new google.maps.Size(20, 20), // Adjust size if needed
                        scale: 6,
                        strokeColor: "#FFFFFF",
                        strokeWeight: 2,
                        fillColor: defaultValues.polylineColors[vesselName] || "#8B5CF6", // Purple color for audio data
                        fillOpacity: 0.8,
                    },
                    zIndex: 1000,
                    map: null,
                });

                marker.audioArtifactData = audioPoint;

                marker.addListener("click", async () => {
                    const contentDiv = document.createElement("div");
                    const root = createRoot(contentDiv); // Use createRoot
                    root.render(
                        React.createElement(WrappedAudioInfoWindow, {
                            audioPoint: audioPoint,
                            audioInfoWindow: audioInfoWindow,
                            user: user,
                        }),
                    );
                    audioInfoWindow.setContent(contentDiv);
                    const offset = await handleInfoWindowOffset(marker, "audio", google, map);
                    audioInfoWindow.setOptions({ pixelOffset: offset });
                    audioInfoWindow.open(map, marker);
                });

                clusterAudioMarkers.push(marker);
                return marker;
            });

            // Save markers for this vessel
            setAudioDataMarkers((prev) => ({
                ...prev,
                [vesselName]: markers,
            }));
        });

    if (audioClustererRef.current) {
        audioClustererRef.current.clearMarkers(); // Clear existing markers
    }
    audioClustererRef.current = new MarkerClusterer({
        markers: clusterAudioMarkers,
        map,
        // renderer,
        zoomOnClick: false,
        styles: clusterStyles,
        algorithm: new SuperClusterAlgorithm({
            maxZoom: 22, // or your desired zoom
            radius: 30, // or your desired radius
        }),
        onClusterClick: () => { },
    });
    const ClusterOffset = (cluster) => {
        const overlay = new google.maps.OverlayView();
        overlay.draw = function () { };
        overlay.setMap(map);
        const clusterPosition = cluster._position;
        const point = overlay.getProjection().fromLatLngToDivPixel(clusterPosition);
        if (!point) {
            console.warn("Invalid cluster position");
            return new google.maps.Size(0, 0);
        }
        const quadrant = `${point.y > 0 ? "b" : "t"}${point.x < 0 ? "l" : "r"}`;
        // console.log("Cluster position", quadrant);
        if (quadrant == "tr") {
            return new google.maps.Size(-200, 250);
        } else if (quadrant == "tl") {
            return new google.maps.Size(200, 440);
        } else if (quadrant == "br") {
            return new google.maps.Size(-220, 80);
        } else if (quadrant == "bl") {
            return new google.maps.Size(200, 40);
        }
    };

    audioClustererRef.current.addListener("click", async (cluster) => {
        const markers = cluster.markers;

        const contentDiv = document.createElement("div");
        const pixelOffset = ClusterOffset(cluster);
        if (artifactInfowWindow) artifactInfowWindow.close();
        if (currentClusterInfoWindow) currentClusterInfoWindow.close();
        if (infoWindow) infoWindow.close();
        const root = createRoot(contentDiv); // Use createRoot
        root.render(
            React.createElement(WrappedAudioClusterInfoWindow, {
                markers: markers,
                currentAudioClusterInfoWindow: currentAudioClusterInfoWindow,
                user: user,
            }),
        );

        if (!currentAudioClusterInfoWindow) {
            currentAudioClusterInfoWindow = new google.maps.InfoWindow({
                content: contentDiv,
                pixelOffset: pixelOffset,
            });
        } else {
            currentAudioClusterInfoWindow.setContent(contentDiv);
            currentAudioClusterInfoWindow.setOptions({ pixelOffset });
        }
        currentAudioClusterInfoWindow.setPosition(cluster._position);
        currentAudioClusterInfoWindow.open(map);
    });

    console.log("[drawAudioData] time taken to draw audio data", Date.now() - tts, "ms");
};


// caches/singletons (top-level)
let __projectionOverlay = null;
const __svgIconCache = new Map(); // key: `${type}|${color}` -> { url, scaledSize }

function ensureProjectionOverlay(google, map) {
    if (!__projectionOverlay) {
        __projectionOverlay = new google.maps.OverlayView();
        __projectionOverlay.onAdd = function () { };
        __projectionOverlay.draw = function () { };
        __projectionOverlay.onRemove = function () { };
        __projectionOverlay.setMap(map);
    }
    return __projectionOverlay;
}

function getIcon(google, type, color) {
    const key = `${type}|${color}`;
    if (!__svgIconCache.has(key)) {
        const createVideoSVG = (c) => `
      <svg width="40" height="35" viewBox="0 0 40 35" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.1777 9.7661L17.1775 9.766C16.9126 9.60044 16.6082 9.5088 16.2959 9.5006C15.9837 9.4924 15.6749 9.56794 15.4017 9.71937C15.1284 9.87081 14.9007 10.0926 14.7422 10.3618C14.5836 10.6309 14.5 10.9376 14.5 11.25V24.1249C14.5 24.1249 14.5 24.125 14.5 24.125C14.5 24.4413 14.5856 24.7516 14.7479 25.0231C14.9102 25.2946 15.143 25.517 15.4216 25.6667C15.7002 25.8164 16.0142 25.8878 16.3302 25.8734C16.6461 25.8589 16.9523 25.759 17.216 25.5844L17.2161 25.5844L27.2161 18.9619L27.2167 18.9615C27.4603 18.7996 27.6594 18.5793 27.7959 18.3207C27.9324 18.0621 28.0019 17.7734 27.9981 17.481C27.9942 17.1886 27.9172 16.9018 27.774 16.6468C27.6308 16.3919 27.4259 16.1769 27.1782 16.0214L27.1777 16.0211L17.1777 9.7661ZM2.18414 2.18414C3.26247 1.1058 4.72501 0.5 6.25 0.5H33.75C35.275 0.5 36.7375 1.1058 37.8159 2.18414C38.8942 3.26247 39.5 4.725 39.5 6.25V28.75C39.5 30.275 38.8942 31.7375 37.8159 32.8159C36.7375 33.8942 35.275 34.5 33.75 34.5H6.25C4.725 34.5 3.26247 33.8942 2.18414 32.8159C1.1058 31.7375 0.5 30.275 0.5 28.75V6.25C0.5 4.72501 1.1058 3.26247 2.18414 2.18414Z" fill="${c}" stroke="#282C39"/>
      </svg>`;
        const createImageSVG = (c) => `
      <svg width="40" height="36" viewBox="0 0 40 36" xmlns="http://www.w3.org/2000/svg">
        <path d="M21.7871 13.543L15.374 22.9488L13.7263 20.8906L13.725 20.8891C13.2717 20.3273 12.5901 20 11.875 20C11.1636 20 10.4713 20.3246 10.0226 20.892C10.0223 20.8924 10.022 20.8929 10.0216 20.8933L5.02469 27.1395C5.02455 27.1396 5.02441 27.1398 5.02427 27.14C4.4496 27.8538 4.34146 28.8337 4.73708 29.6546C5.13248 30.475 5.96319 31 6.875 31H33.125C34.0038 31 34.8173 30.516 35.225 29.7303C35.6283 28.9533 35.5816 28.0138 35.0845 27.288C35.0845 27.2879 35.0844 27.2878 35.0844 27.2878L25.71 13.5386C25.2656 12.8869 24.5331 12.5 23.75 12.5C22.9711 12.5 22.2279 12.8836 21.7887 13.5406C21.7881 13.5414 21.7876 13.5422 21.7871 13.543ZM0.5 5.5C0.5 3.01833 2.51833 1 5 1H35C37.4817 1 39.5 3.01833 39.5 5.5V30.5C39.5 32.9817 37.4817 35 35 35H5C2.51833 35 0.5 32.9817 0.5 30.5V5.5ZM8.75 13.5C9.87717 13.5 10.9582 13.0522 11.7552 12.2552C12.5522 11.4582 13 10.3772 13 9.25C13 8.12283 12.5522 7.04183 11.7552 6.2448C10.9582 5.44777 9.87717 5 8.75 5C7.62283 5 6.54183 5.44777 5.7448 6.2448C4.94777 7.04183 4.5 8.12283 4.5 9.25C4.5 10.3772 4.94777 11.4582 5.7448 12.2552C6.54183 13.0522 7.62283 13.5 8.75 13.5Z" fill="${c}" stroke="#282C39"/>
      </svg>`;
        const svg = type === "video" ? createVideoSVG(color) : createImageSVG(color);
        __svgIconCache.set(key, {
            url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svg)}`,
            scaledSize: new google.maps.Size(20, 20),
        });
    }
    return __svgIconCache.get(key);
}

function getClusterPixelOffset(google, map, position) {
    const overlay = ensureProjectionOverlay(google, map);
    const proj = overlay.getProjection();
    if (!proj) return new google.maps.Size(0, 0);
    const point = proj.fromLatLngToDivPixel(position);
    const quadrant = `${point.y > 0 ? "b" : "t"}${point.x < 0 ? "l" : "r"}`;
    // console.log("cluster quadrant", quadrant)
    if (quadrant === "tr") return new google.maps.Size(-370, 370);
    if (quadrant === "tl") return new google.maps.Size(300, 370);
    if (quadrant === "br") return new google.maps.Size(-340, 20);
    return new google.maps.Size(300, -40); // "bl"
}


const drawArtifacts = (
    showArtifacts,
    artifactsMarkers,
    google,
    clustererRef,
    map,
    filteredArtifacts,
    prevFilteredArtifacts,
    artifactInfowWindow,
    currentClusterInfoWindow,
    currentAudioClusterInfoWindow,
    audioInfoWindow,
    user,
    timezone,
    vesselInfo,
    // cachedSrc,
) => {
    const tts = Date.now();
    // console.log('drawartifacts, artifactsmarkers', artifactsMarkers.current)
    // If `showArtifacts` is false, clear existing markers and the clusterer
    if (!showArtifacts) {
        // return;
        // Clear individual markers
        Object.keys(artifactsMarkers.current).forEach((vesselName) => {
            // console.log("clearing markers for", vesselName);
            artifactsMarkers.current[vesselName].forEach((marker) => {
                marker.setMap(null); // Remove marker from map
                google.maps.event.clearInstanceListeners(marker); // Clear event listeners
            });
            artifactsMarkers.current[vesselName] = []; // Clear the object reference
        });
        // Clear the clusterer
        if (clustererRef.current) {
            clustererRef.current.clearMarkers(); // Remove all markers from the clusterer
            clustererRef.current.setMap(null); // Remove the clusterer from the map
            // clustererRef.current.clearInstanceListeners(); // Remove the clusterer from the map
            clustererRef.current = null; // Reset the clusterer reference
        }
        return;
    }

    const skipVessels = [];

    Object.keys(filteredArtifacts).forEach((vesselName) => {
        if (
            artifactsMarkers.current[vesselName] &&
            artifactsMarkers.current[vesselName].length &&
            prevFilteredArtifacts.current[vesselName] &&
            prevFilteredArtifacts.current[vesselName] === filteredArtifacts[vesselName].length
        ) {
            skipVessels.push(vesselName);
        }
    });

    console.log("[drawArtifacts] skipVessels.length", skipVessels.length);

    // Clear existing markers before re-drawing
    Object.keys(artifactsMarkers.current)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            artifactsMarkers.current[vesselName].forEach((marker) => {
                marker.setMap(null);
                google.maps.event.clearInstanceListeners(marker);
            });
            artifactsMarkers.current[vesselName] = []; // Clear the references
        });

    if (Object.keys(filteredArtifacts).length === 0) {
        if (clustererRef.current) {
            clustererRef.current.clearMarkers();
            clustererRef.current.setMap(null);
            clustererRef.current = null;
        }
        return;
    }

    if (!map) return console.warn("Artifacts could not be drawn");

    const clusterMarkers = Object.keys(artifactsMarkers.current)
        .map((k) => artifactsMarkers.current[k])
        .flat();

    // Generate markers and add them to the `clusterMarkers` array
    // console.log(
    //     "[drawArtifacts] drawing artifacts",
    //     Object.keys(filteredArtifacts).length,
    //     Object.keys(filteredArtifacts).reduce((acc, curr) => acc + filteredArtifacts[curr].length, 0),
    // );
    Object.keys(filteredArtifacts)
        .filter((vesselName) => !skipVessels.includes(vesselName))
        .forEach((vesselName) => {
            const markers = filteredArtifacts[vesselName]
                .map((artifact) => {
                    if (!artifact.location) {
                        console.warn("[drawArtifacts] Invalid artifact location. Skipping:", artifact);
                        return null;
                    }
                    const fillColor = defaultValues.polylineColors[vesselName] || "#3873E4";
                    const icon = getIcon(
                        google,
                        artifact.video_exists ? "video" : "image",
                        fillColor
                    );

                    const adjustedLat = artifact.location.coordinates[1];
                    const adjustedLng = artifact.location.coordinates[0];
                    const marker = new google.maps.Marker({
                        position: { lat: adjustedLat, lng: adjustedLng },
                        icon,           // use cached icon
                        // draggable: true,
                        // cursor: "grab",
                        // title: "Click or Drag",
                        map: null, // Add to clusterer later
                    });

                    // Store artifact data for O(1) cluster-click lookup
                    marker.artifactId = artifact._id;
                    marker.artifact = artifact;
                    marker.addListener("click", async () => {
                        if (artifactInfowWindow) artifactInfowWindow.close();
                        if (currentClusterInfoWindow) currentClusterInfoWindow.close();
                        const contentDiv = document.createElement("div");

                        // google.maps.event.addListenerOnce(artifactInfowWindow, "domready", () => {
                        const root = createRoot(contentDiv); // Use createRoot
                        root.render(
                            React.createElement(WrappedInfoWindow, {
                                artifact: artifact,
                                artifactInfowWindow: artifactInfowWindow,
                                timezone: timezone,
                                vesselInfo,
                                user: user,
                            }),
                        );
                        // });
                        const offset = await handleInfoWindowOffset(marker, "artifact", google, map);
                        artifactInfowWindow.setOptions({ pixelOffset: offset });
                        artifactInfowWindow.setContent(contentDiv);
                        artifactInfowWindow.open(map, marker);
                    });

                    // marker.addListener("click", async () => {
                    //     const content = await generateContentWithImage(artifact, false, 0, 0, favouriteArtifactsRef, cachedSrc, timezone); // Fetch and display artifact content
                    //     if (artifactInfowWindow) artifactInfowWindow.close();
                    //     if (currentClusterInfoWindow) currentClusterInfoWindow.close();
                    //     artifactInfowWindow.setContent(content);
                    //     artifactInfowWindow.open(map, marker);
                    //     const offset = await handleInfoWindowOffset(marker, "artifact", google, map);
                    //     artifactInfowWindow.setOptions({ pixelOffset: offset });
                    //     google.maps.event.addListenerOnce(artifactInfowWindow, "domready", () => {
                    //         const closeButton = document.getElementById("custom-close-btn");
                    //         const favouriteBtn = document.getElementById("favourite-btn");
                    //         const shareBtn = document.getElementById("share-btn");
                    //         const downloadBtn = document.getElementById("download-btn");
                    //         const playButtonOverlay = document.getElementById("play-button-overlay");
                    //         const fullscreenBtn = document.getElementById("fullscreen-btn");

                    //         if (downloadBtn) {
                    //             downloadBtn.onclick = () => {
                    //                 downloadArtifact(artifact._id);
                    //             };
                    //         }

                    //         if (closeButton) {
                    //             closeButton.addEventListener("click", () => {
                    //                 artifactInfowWindow.close();
                    //             });
                    //         }

                    //         if (favouriteBtn) {
                    //             favouriteBtn.onclick = async () => {
                    //                 await handleFavouriteButtonClick(
                    //                     artifact,
                    //                     favouriteArtifactsRef,
                    //                     setFavouriteArtifacts,
                    //                     user,
                    //                     marker,
                    //                     artifactInfowWindow,
                    //                     cachedSrc,
                    //                 );
                    //                 // Refresh content without closing the info window
                    //                 const newContent = await generateContentWithImage(artifact, false, 0, 0, favouriteArtifactsRef, cachedSrc, timezone);
                    //                 artifactInfowWindow.setContent(newContent);
                    //                 // Reattach event listeners once the content is rendered
                    //                 google.maps.event.addListenerOnce(artifactInfowWindow, "domready", () => {
                    //                     // (Repeat listeners as above)
                    //                     const closeButton2 = document.getElementById("custom-close-btn");
                    //                     const favBtn2 = document.getElementById("favourite-btn");
                    //                     const downloadBtn2 = document.getElementById("download-btn");

                    //                     if (downloadBtn2) {
                    //                         downloadBtn2.onclick = () => {
                    //                             downloadArtifact(artifact._id);
                    //                         };
                    //                     }
                    //                     if (closeButton2) {
                    //                         closeButton2.addEventListener("click", () => {
                    //                             artifactInfowWindow.close();
                    //                         });
                    //                     }
                    //                     if (favBtn2) {
                    //                         favBtn2.onclick = async () => {
                    //                             // Allow further updates similarly
                    //                         };
                    //                     }
                    //                 });
                    //             };
                    //         }

                    //         if (shareBtn) {
                    //             shareBtn.onclick = async () => {
                    //                 setArtifactToShare(artifact);
                    //                 setIsShareModalOpen(true);
                    //             };
                    //         }

                    //         if (fullscreenBtn) {
                    //             fullscreenBtn.onclick = async () => {
                    //                 setIsFullscreenOpen(artifact);
                    //                 setFullscreenOpen(true);
                    //             };
                    //         }

                    //         if (playButtonOverlay) {
                    //             playButtonOverlay.onclick = () => {
                    //                 setIsFullscreenOpen(artifact);
                    //                 setFullscreenOpen(true);
                    //             };
                    //         }
                    //     });
                    // });

                    clusterMarkers.push(marker);
                    return marker;
                })
                .filter(Boolean);

            artifactsMarkers.current = {
                ...artifactsMarkers.current,
                [vesselName]: markers,
            };

            // console.log('drawartifacts updated artifactsMarkers', artifactsMarkers.current)
        });

    if (clustererRef.current) {
        clustererRef.current.clearMarkers(); // Clear existing markers
    }

    // const renderer = {
    //     render: ({ count, position }) => {
    //         // Calculate the size based on the count
    //         const size = Math.min(50 + (count / 2000) * 150, 200);

    //         return new google.maps.Marker({
    //             label: {
    //                 text: count.toString(),
    //                 color: "#000",
    //                 fontSize: "16px",
    //                 fontWeight: "500",
    //             },
    //             position,
    //             icon: {
    //                 url: `data:image/svg+xml;charset=UTF-8,${svg}`,
    //                 scaledSize: new google.maps.Size(size, size),
    //             },
    //             zIndex: google.maps.Marker.MAX_ZINDEX + 1,
    //         });
    //     },
    // };

    clustererRef.current = new MarkerClusterer({
        markers: clusterMarkers,
        map,
        // renderer,
        zoomOnClick: false,
        styles: clusterStyles,
        algorithm: new SuperClusterAlgorithm({
            maxZoom: 22, // or your desired zoom
            radius: 30, // or your desired radius
        }),
        onClusterClick: () => {},
    });

    // let previousZoom = map.getZoom();
    // google.maps.event.addListener(map, "zoom_changed", () => {
    //     const currentZoom = map.getZoom();
    //     const isZoomingIn = currentZoom > previousZoom;
    //     previousZoom = currentZoom;

    //     if (isZoomingIn) {
    //         const isArtifactInfoWindowOpen = artifactInfowWindow && artifactInfowWindow.getMap() ? true : false;
    //         const isClusterInfoWindowOpen = currentClusterInfoWindow && currentClusterInfoWindow.getMap() ? true : false;

    //         if (isArtifactInfoWindowOpen || isClusterInfoWindowOpen) {
    //             artifactInfowWindow && artifactInfowWindow.close();
    //             currentClusterInfoWindow && currentClusterInfoWindow.close();
    //             console.log("Info windows closed on zoom in");
    //         }
    //     }
    // });

    // clustererRef.current.addListener("click", (cluster) => {
    //     openClusterInfoWindow(
    //         cluster,
    //         currentClusterInfoWindow,
    //         google,
    //         favouriteArtifactsRef,
    //         map,
    //         setFavouriteArtifacts,
    //         user,
    //         cachedSrc,
    //         timezone,
    //         setArtifactToShare,
    //         setIsShareModalOpen,
    //         artifactInfowWindow,
    //         setIsFullscreenOpen,
    //         setFullscreenOpen,
    //     );
    // });

    // Reuse one overlay to compute offset (no per-click instantiation)
    const ClusterOffset = (cluster) => {
        const pixelOffset = getClusterPixelOffset(google, map, cluster._position);
        return pixelOffset;
    };

    clustererRef.current.addListener("click", async (cluster) => {
        const ts = new Date().getTime();
        // Get marker count and artifact IDs efficiently
        const markers = cluster.markers;
        const markerCount = markers.length;
        const artifactIds = [];

        // Extract artifact data directly from markers - O(k) operation
        const currentArtifacts = [];
        for (let i = 0; i < markers.length; i++) {
            const marker = markers[i];
            if (marker.artifact) {
                currentArtifacts.push(marker.artifact);
                if (marker.artifactId) {
                    artifactIds.push(marker.artifactId);
                }
            }
        }


        // Create info window content
        const contentDiv = document.createElement("div");
        const pixelOffset = ClusterOffset(cluster);
        if (artifactInfowWindow) artifactInfowWindow.close();
        if (currentClusterInfoWindow) currentClusterInfoWindow.close();
        if (currentAudioClusterInfoWindow) currentAudioClusterInfoWindow.close();
        if (audioInfoWindow) audioInfoWindow.close();
        // google.maps.event.addListenerOnce(currentClusterInfoWindow, "domready", () => {
        const root = createRoot(contentDiv); // Use createRoot
        root.render(
            React.createElement(WrappedClusterInfoWindow, {
                currentArtifacts,
                currentClusterInfoWindow: currentClusterInfoWindow,
                timezone: timezone,
                vesselInfo,
                user: user,
                ts
            }),
        );
        // });

        if (!currentClusterInfoWindow) {
            currentClusterInfoWindow = new google.maps.InfoWindow({
                content: contentDiv,
                pixelOffset: pixelOffset,
            });
        } else {
            currentClusterInfoWindow.setContent(contentDiv);
            currentClusterInfoWindow.setOptions({ pixelOffset });
        }
        currentClusterInfoWindow.setPosition(cluster._position);
        currentClusterInfoWindow.open(map);
        // Simple debug log without expensive object expansion
        console.log(`Cluster opened: ${currentArtifacts.length} artifacts in ${new Date().getTime() - ts}ms`);
    });
    console.log("[drawArtifacts] time taken to draw artifacts", Date.now() - tts, "ms");
};

// this is for stroe the coordinates into index DB
const storeCoordinates = (vesselId, coordinates) => {
    const store = vesselId + "_location";
    idb.addItems(store, coordinates).catch((err) => {
        console.error("error adding coordinates to indexeddb at store", store, err);
    });
};

// this is for stroe the artifacts into index DB
const storeArtifacts = (vesselId, artifacts) => {
    const store = vesselId + "_artifact";
    idb.addItems(store, artifacts).catch((err) => {
        console.error("error adding artifacts to indexeddb at store", store, err);
    });
};

const filterArtifactsNearHomePorts = async (artifactsMap, filterMode, vesselInfo) => {
    // console.log("[filterArtifactsNearHomePorts] artifactMap", artifactsMap, "filterMode", filterMode);
    if (!artifactsMap || Object.keys(artifactsMap).length === 0) {
        return artifactsMap;
    }

    if (filterMode === "ALL") {
        return artifactsMap;
    }

    if (!vesselInfo || vesselInfo.length === 0) {
        return artifactsMap;
    }

    try {
        const homePorts = vesselInfo
            .filter(vessel => vessel.home_port_location && vessel.home_port_location.coordinates)
            .map(vessel => ({
                lat: vessel.home_port_location.coordinates[1],
                lng: vessel.home_port_location.coordinates[0]
            }));

        if (!homePorts || homePorts.length === 0) {
            return artifactsMap;
        }

        const filteredMap = {};
        const HOME_PORT_RADIUS = defaultValues.HOME_PORT_RADIUS; // 0.5 km

        for (const vesselId in artifactsMap) {
            const vesselArtifacts = artifactsMap[vesselId];

            if (filterMode === "ONLY_NON_HOME_PORTS") {
                filteredMap[vesselId] = vesselArtifacts.filter((artifact) => {
                    return !homePorts.some((port) => {
                        const distance = calculateDistance(
                            { lat: artifact.location.coordinates[1], lng: artifact.location.coordinates[0] },
                            { lat: port.lat, lng: port.lng },
                        );
                        return distance <= HOME_PORT_RADIUS;
                    });
                });
            } else if (filterMode === "ONLY_HOME_PORTS") {
                filteredMap[vesselId] = vesselArtifacts.filter((artifact) => {
                    return homePorts.some((port) => {
                        const distance = calculateDistance(
                            { lat: artifact.location.coordinates[1], lng: artifact.location.coordinates[0] },
                            { lat: port.lat, lng: port.lng },
                        );
                        return distance <= HOME_PORT_RADIUS;
                    });
                });
            }
        }

        return filteredMap;
    } catch (error) {
        console.error("Error filtering vessel artifacts near home ports:", error);
        return artifactsMap;
    }
};

const filterArtifactsByAis = (artifactsMap, filterMode) => {
    // console.log("[filterArtifactsByAis] artifactMap", artifactsMap, "filterMode", filterMode);
    if (!artifactsMap || Object.keys(artifactsMap).length === 0) {
        return artifactsMap;
    }

    if (filterMode === "both") {
        return artifactsMap;
    }

    const filteredMap = {};
    for (const vesselId in artifactsMap) {
        const vesselArtifacts = artifactsMap[vesselId];

        if (filterMode === "ais_only") {
            filteredMap[vesselId] = vesselArtifacts.filter((artifact) => {
                return artifact?.portal?.has_ais === true;
            });
        } else if (filterMode === "non_ais_only") {
            filteredMap[vesselId] = vesselArtifacts.filter((artifact) => {
                return artifact?.portal?.has_ais !== true;
            });
        }
    }
    // console.log("[filterArtifactsByAis] filteredMap", filteredMap,);

    return filteredMap;
};

export {
    // generateContentWithImage,
    // generateRandomOffset,
    clusterStyles,
    // handleFavouriteButtonClick,
    // openClusterInfoWindow,
    handleInfoWindowOffset,
    getFormattedPath,
    optimizeCoordinates,
    drawCoordinates,
    drawAisData,
    drawAudioData,
    drawArtifacts,
    storeArtifacts,
    storeCoordinates,
    filterArtifactsNearHomePorts,
    filterArtifactsByAis,
    drawDataPoints,
    drawPolylines
};
