import { useMemo, useState } from "react";
import { Grid, Typography, Stack, Skeleton, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { useApp } from "../../../hooks/AppHook";
import SensorCard from "./SensorCard";

const Sensors = ({
    view = "Single",
    loadingStreams,
    streams,
    selectedStream,
    setSelectedStream,
    // fetchStreams,
    artifactIndicator,
    viewedArtifacts,
    regionGroups,
    vesselInfo = [],
}) => {
    const { screenSize } = useApp();
    // Group streams by RegionGroupId
    const groupedStreams = useMemo(() => {
        const groups = {};
        streams.forEach((stream) => {
            const groupId = stream.RegionGroupId || "Unregistered";
            if (!groups[groupId]) groups[groupId] = [];
            groups[groupId].push(stream);
        });
        return groups;
    }, [streams]);

    // Helper to get region group name from regionGroups list
    const getRegionGroupName = (groupId) => {
        if (groupId === "Unregistered") return "Unregistered";
        const group = regionGroups.find((g) => g._id === groupId);
        return group ? `${group.name} (UTC ${group.timezone})` : groupId;
    };

    const [expanded, setExpanded] = useState(false);
    const handleAccordionChange = (groupId) => (event, isExpanded) => {
        setExpanded(isExpanded ? groupId : false);
    };

    if (loadingStreams)
        return (
            <Stack direction={{ xs: "column", lg: "column" }} gap={"1px"} width={"100%"}>
                {Array.from({ length: screenSize.xs ? 4 : screenSize.md ? 7 : 3 }).map((_, index) => (
                    <Skeleton key={index} animation="wave" variant="rectangular" height={100} sx={{ width: { xs: "auto", lg: "auto" } }} />
                ))}
            </Stack>
        );

    if (streams.length === 0)
        return (
            <Typography color="#FFFFFF" padding="10px" textAlign="center" sx={{ backgroundColor: "primary.light" }}>
                No sensors found
            </Typography>
        );

    // Render region groups as accordions
    return (
        <Grid>
            {Object.keys(groupedStreams).map((groupId) => (
                <Accordion
                    key={groupId}
                    expanded={expanded === groupId}
                    onChange={handleAccordionChange(groupId)}
                    disableGutters
                    sx={{
                        "&.Mui-expanded": {
                            margin: 0, // explicitly remove margin on expanded
                        },
                        minWidth: "350px",
                        paddingRight: 2,
                    }}
                >
                    <AccordionSummary
                        expandIcon={expanded === groupId ? <RemoveIcon /> : <AddIcon />}
                        sx={{
                            backgroundColor: "#464F59",
                        }}
                    >
                        <Typography>{getRegionGroupName(groupId)}</Typography>
                    </AccordionSummary>
                    <AccordionDetails
                        sx={{
                            p: 0,
                            m: 0,
                            maxHeight: "400px",
                            overflowY: "auto",
                        }}
                    >
                        <Grid container flexDirection={"column"}>
                            {groupedStreams[groupId].map((stream) => (
                                <SensorCard
                                    key={stream.StreamName}
                                    stream={stream}
                                    view={view}
                                    selectedStream={selectedStream}
                                    onSelect={(stream) => {
                                        setSelectedStream(stream);
                                        // fetchStreams();
                                    }}
                                    artifactIndicator={artifactIndicator}
                                    viewedArtifacts={viewedArtifacts}
                                    vesselInfo={vesselInfo}
                                />
                            ))}
                        </Grid>
                    </AccordionDetails>
                </Accordion>
            ))}
        </Grid>
    );
};

export default Sensors;
