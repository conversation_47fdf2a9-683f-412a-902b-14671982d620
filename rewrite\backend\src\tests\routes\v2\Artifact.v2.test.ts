import request from 'supertest';
import app from '../../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../../mocks/auth.mock';
import vesselService from '../../../services/Vessel.service';
import { canAccessVessel, validateError } from '../../../utils/functions';
import { qmai } from '../../mocks/modules/db.mock';
import User from '../../../models/User';
import ApiKey from '../../../models/ApiKey';

jest.mock('../../../modules/db', () => require('../../mocks/modules/db.mock'));
jest.mock('../../../services/Vessel.service', () => require('../../mocks/services/vesselService.mock'));
jest.mock('../../../utils/functions', () => require('../../mocks/utils/functions.mock'));
jest.mock('../../../models/User', () => require('../../mocks/models/user.mock'));
jest.mock('../../../models/ApiKey', () => require('../../mocks/models/apiKey.mock'));
jest.mock('../../../queries/User', () => ({ getUser: jest.fn() }));

describe('Artifact V2 API', () => {
    describe('POST /api/v2/artifacts/:vesselId', () => {
        const runTests = (authMethod: 'user' | 'api-key', generateToken: (id: string) => string, userOrApiKey: any) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (vesselService.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439012' } as never);
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ _id: '1' }] as never),
                            limit: jest.fn().mockReturnThis(),
                        }),
                    });
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/v2/artifacts/507f1f77bcf86cd799439012');
                    expect(res.status).toBe(401);
                });

                it('should return 200 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                    (User.aggregate as jest.Mock).mockResolvedValue([
                        { ...userOrApiKey.nonAuthorized, organization: { _id: '507f1f77bcf86cd799439013', is_internal: true } }
                    ] as never);
                    const res = await request(app).post('/api/v2/artifacts/507f1f77bcf86cd799439012').set('Authorization', nonAuthToken);
                    expect(res.status).toBe(200);
                });

                it('should return 404 if vessel does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (vesselService.findById as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).post('/api/v2/artifacts/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 403 if cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);
                    const res = await request(app).post('/api/v2/artifacts/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if endTimestamp provided without startTimestamp', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: Date.now() });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and list artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(null as never),
                            limit: jest.fn().mockReturnThis(),
                        }),
                    });
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: Date.now() - 1000 });
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (qmai.collection as jest.Mock).mockImplementation(() => { throw new Error('db'); });
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });

                it('should honor excludeIds filter when provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: Date.now() - 1000, endTimestamp: Date.now(), excludeIds: ['507f1f77bcf86cd799439099'] });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with no time window provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({});
                    expect(res.status).toBe(200);
                });

                it('should return 400 when startTimestamp is not int', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: 'abc' });
                    expect([400, 500]).toContain(res.status);
                });

                it('should return 400 when endTimestamp is not int', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: 'abc' });
                    expect([400, 500]).toContain(res.status);
                });

                it('should return 400 when excludeIds is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'not-array' });
                    expect(res.status).toBe(400);
                });

                it('should apply swagger limit branch when Referer contains /docs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .set('Referer', 'http://localhost/docs')
                        .send({ startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(200);
                });

                it('should return 500 when toArray throws', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn(() => { throw new Error('db'); }),
                            limit: jest.fn().mockReturnThis(),
                        }),
                    });
                    const res = await request(app)
                        .post('/api/v2/artifacts/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(500);
                });
            });
        };

        const { generateUserToken, authorizedUser, nonAuthorizedUser } = require('../../data/Auth');
        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


