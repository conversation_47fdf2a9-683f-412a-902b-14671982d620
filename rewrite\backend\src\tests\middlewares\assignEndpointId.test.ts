import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext, expectNextCalled } from '../mocks/middlewares/auth.mock';
import assignEndpointId from '../../middlewares/assignEndpointId';

describe('assignEndpointId middleware', () => {
    let req: any;
    let res: any;
    let next: jest.Mock;

    beforeEach(() => {
        req = createMockRequest();
        res = createMockResponse();
        next = createMockNext();
    });

    it('should assign endpoint_id to request and call next', () => {
        const endpointId = 123;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle string endpoint_id', () => {
        const endpointId = 'test-endpoint';

        assignEndpointId(endpointId as any, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle zero endpoint_id', () => {
        const endpointId = 0;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle negative endpoint_id', () => {
        const endpointId = -1;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle null endpoint_id', () => {
        const endpointId = null;

        assignEndpointId(endpointId as any, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle undefined endpoint_id', () => {
        const endpointId = undefined;

        assignEndpointId(endpointId as any, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should overwrite existing endpoint_id', () => {
        req._endpoint_id = 'existing-endpoint';
        const endpointId = 456;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should not modify other request properties', () => {
        const originalBody = { test: 'data' };
        const originalQuery = { param: 'value' };
        req.body = originalBody;
        req.query = originalQuery;

        const endpointId = 789;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expect(req.body).toBe(originalBody);
        expect(req.query).toBe(originalQuery);
        expectNextCalled(next);
    });

    it('should handle large endpoint_id numbers', () => {
        const endpointId = 999999999;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });

    it('should handle floating point endpoint_id', () => {
        const endpointId = 123.45;

        assignEndpointId(endpointId, req, res, next);

        expect(req._endpoint_id).toBe(endpointId);
        expectNextCalled(next);
    });
});
