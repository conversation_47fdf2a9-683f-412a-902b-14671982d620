import { expect, jest } from '@jest/globals';

const mockJest = jest.fn() as any;
export const createMockUser = (overrides: any = {}) => ({
    _id: '507f1f77bcf86cd799439011',
    name: 'Test User',
    email: '<EMAIL>',
    password: 'hashedPassword',
    is_deleted: false,
    is_active: true,
    jwt_tokens: ['mock-jwt-token'],
    role_id: 1,
    organization_id: '507f1f77bcf86cd799439012',
    creation_timestamp: new Date().toISOString(),
    ...overrides
});

export const createMockDeletedUser = (overrides: any = {}) =>
    createMockUser({ is_deleted: true, ...overrides });

export const createMockInactiveUser = (overrides: any = {}) =>
    createMockUser({ is_active: false, ...overrides });

export const createUserModelMock = () => ({
    find: mockJest,
    findOne: mockJest,
    findById: mockJest,
    findOneAndUpdate: mockJest,
    findOneAndDelete: mockJest,
    create: mockJest,
    updateOne: mockJest,
    updateMany: mockJest,
    deleteOne: mockJest,
    deleteMany: mockJest,
    aggregate: mockJest,
    countDocuments: mockJest,
    distinct: mockJest,
    save: mockJest,
    markModified: mockJest,
    toObject: mockJest,
    toJSON: mockJest,
    schema: {}
});

export const createMockUserDocument = (data: any = {}) => {
    const userData = createMockUser(data);
    
    return {
        ...userData,
        save: mockJest.mockResolvedValue(userData),
        remove: mockJest.mockResolvedValue(userData),
        deleteOne: mockJest.mockResolvedValue({ deletedCount: 1 }),
        markModified: mockJest,
        toObject: mockJest.mockReturnValue(userData),
        toJSON: mockJest.mockReturnValue(userData),
        isModified: mockJest.mockReturnValue(false),
        isNew: false
    };
};

export const createUserSchemaMock = () => ({
    pre: mockJest,
    post: mockJest,
    index: mockJest,
    methods: {},
    statics: {},
    paths: {
        name: { type: String, required: true },
        email: { type: String, required: true, unique: true },
        password: { type: String, required: true },
        is_deleted: { type: Boolean, default: false },
        is_active: { type: Boolean, default: true },
        jwt_tokens: [{ type: String }],
        role_id: { type: Number, required: true },
        organization_id: { type: String, required: true },
        creation_timestamp: { 
            type: Date, 
            required: true, 
            default: () => new Date().toISOString() 
        }
    }
});

export const setupUserTest = () => {
    const mockModel = createUserModelMock();
    const mockUser = createMockUser();
    const mockDocument = createMockUserDocument();
    
    return {
        mockModel,
        mockUser,
        mockDocument
    };
};

export const expectUserCreated = (mockModel: any, userData: any) => {
    expect(mockModel.create).toHaveBeenCalledWith(userData);
};

export const expectUserFound = (mockModel: any, query: any) => {
    expect(mockModel.findOne).toHaveBeenCalledWith(query);
};

export const expectUserUpdated = (mockModel: any, query: any, update: any) => {
    expect(mockModel.updateOne).toHaveBeenCalledWith(query, update);
};

export const expectUserDeleted = (mockModel: any, query: any) => {
    expect(mockModel.deleteOne).toHaveBeenCalledWith(query);
};

export const createValidUserData = (overrides: any = {}) => ({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'securePassword123',
    role_id: 1,
    organization_id: '507f1f77bcf86cd799439012',
    ...overrides
});

export const createInvalidUserData = (field: string) => {
    const validData = createValidUserData();
    
    switch (field) {
        case 'email':
            return { ...validData, email: 'invalid-email' };
        case 'name':
            return { ...validData, name: '' };
        case 'password':
            return { ...validData, password: '' };
        case 'role_id':
            return { ...validData, role_id: null };
        case 'organization_id':
            return { ...validData, organization_id: '' };
        default:
            return validData;
    }
};

export const cleanupUserTest = () => {
    jest.clearAllMocks();
};

export default createUserModelMock();