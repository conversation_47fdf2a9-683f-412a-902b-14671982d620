import { describe, it, expect } from '@jest/globals';
import { userEndpointRestrictions } from '../../utils/userEndpointRestrictions';

describe('userEndpointRestrictions', () => {
    it('should export userEndpointRestrictions object with correct structure', () => {
        expect(userEndpointRestrictions).toBeDefined();
        expect(typeof userEndpointRestrictions).toBe('object');
    });

    it('should contain endpoint ID keys as numbers', () => {
        const keys = Object.keys(userEndpointRestrictions);
        expect(keys.length).toBeGreaterThan(0);
        keys.forEach(key => {
            expect(typeof parseInt(key)).toBe('number');
        });
    });

    it('should contain expected endpoint IDs', () => {
        expect(userEndpointRestrictions).toHaveProperty('2303');
        expect(userEndpointRestrictions).toHaveProperty('2304');
    });

    it('should contain arrays of organization IDs for each endpoint', () => {
        expect(Array.isArray(userEndpointRestrictions[2303])).toBe(true);
        expect(Array.isArray(userEndpointRestrictions[2304])).toBe(true);
    });

    it('should contain valid organization ID strings', () => {
        const orgIds = userEndpointRestrictions[2303];
        expect(orgIds.length).toBeGreaterThan(0);
        orgIds.forEach(id => {
            expect(typeof id).toBe('string');
            expect(id.length).toBe(24);
        });
    });

    it('should have same organization IDs for both endpoints', () => {
        expect(userEndpointRestrictions[2303]).toEqual(userEndpointRestrictions[2304]);
    });

    it('should contain expected organization IDs', () => {
        const expectedIds = [
            "66b38701d207b4ef9ceea276",
            "66942a54a7f848634a00990a",
            "672125fd396bed3b70170018",
            "681caa9c9f43051a774d27e0",
            "67acad0473d382c912b960e2"
        ];
        expect(userEndpointRestrictions[2303]).toEqual(expectedIds);
    });
});
