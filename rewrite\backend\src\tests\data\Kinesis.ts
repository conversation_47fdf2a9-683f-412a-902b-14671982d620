const streamsList = [
    {
        "DeviceName": null,
        "StreamName": "prototype-24",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-24/1724069687123",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "ww1ljAmwqPscFzFHDxRO",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:14:47.123Z",
        "DataRetentionInHours": 1,
        "Tags": {},
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-25",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-25/1724069706666",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "xGPWabBw6AS63YRyFOzh",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:15:06.666Z",
        "DataRetentionInHours": 1,
        "Tags": {},
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-32",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-32/1724069756053",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "bj1nRPzYoTNugPhjeomk",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:15:56.053Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Malapascua MRRV-4403",
            "Thumbnail": "https://portal.quartermaster.us/4403.jpg"
        },
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-33",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-33/1724069779670",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "VoYAl4N1oHPfWR9QQI8h",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:16:19.670Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Teresa Magbanua MRRV-9701",
            "Thumbnail": "https://portal.quartermaster.us/9701.jpeg"
        },
        "IsLive": true
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-36",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-36/1724069813231",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "0VagnvNF87ywfD71vMgo",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:16:53.231Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Cabra MRRV-4409",
            "Thumbnail": "https://portal.quartermaster.us/4409.jpeg"
        },
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-37",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-37/1724069836072",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "Jn67enedwnagUh69RKkN",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:17:16.072Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Cape Engaño MRRV-4411",
            "Thumbnail": "https://portal.quartermaster.us/4411.jpeg"
        },
        "IsLive": true
    }
];

const regionsList = [
    { _id: 'region1', value: 'us-east-1', is_live: true },
    { _id: 'region2', value: 'us-west-2', is_live: true },
    { _id: 'region3', value: 'eu-west-1', is_live: false }
];

const vesselsList = [
    {
        _id: 'vessel1',
        unit_id: 'prototype-24',
        name: 'Test Vessel 1',
        is_active: true,
        thumbnail_compressed_s3_key: 'thumb1.jpg',
        region_group_id: 'regionGroup1'
    },
    {
        _id: 'vessel2',
        unit_id: 'prototype-25',
        name: 'Test Vessel 2',
        is_active: false,
        thumbnail_compressed_s3_key: null,
        region_group_id: null
    }
];

const streamData = {
    _id: 'stream1',
    unit_id: 'prototype-24',
    name: 'Test Stream'
};

const streamTags = {
    Name: 'Test Vessel Name',
    Thumbnail: 'https://example.com/thumb.jpg'
};

const screenshotData = Buffer.from('fake-screenshot-data');
const clipData = {
    Payload: Buffer.from('fake-clip-data'),
    ContentType: 'video/mp4'
};

const streamingUrl = 'https://example.com/stream.m3u8';

export { 
    streamsList, 
    regionsList, 
    vesselsList, 
    streamData, 
    streamTags, 
    screenshotData, 
    clipData, 
    streamingUrl 
};