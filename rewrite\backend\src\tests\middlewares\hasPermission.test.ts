import { describe, it, beforeEach, expect } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext, expectForbiddenResponse, expectNextCalled, expectNextNotCalled } from '../mocks/middlewares/auth.mock';
import hasPermission from '../../middlewares/hasPermission';
import { NextFunction } from 'express';

describe('hasPermission middleware', () => {
    let req: any;
    let res: any;
    let next: NextFunction;

    beforeEach(() => {
        req = createMockRequest();
        res = createMockResponse();
        next = createMockNext();
    });

    it('should call next if user has all required permissions', () => {
        req.user = {
            permissions: [
                { permission_id: 1 },
                { permission_id: 2 }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });

    it('should return 403 if user is missing a required permission', () => {
        req.user = {
            permissions: [
                { permission_id: 1 }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectForbiddenResponse(res, 'You cannot access this resource');
        expectNextNotCalled(next as any);
    });

    it('should call next if api_key is present', () => {
        req.api_key = { _id: 'some-api-key' };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });

    it('should return 403 if neither user nor api_key is present', () => {
        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectForbiddenResponse(res, 'Login to access this resource');
        expectNextNotCalled(next as any);
    });

    it('should call next if user has more permissions than required', () => {
        req.user = {
            permissions: [
                { permission_id: 1 },
                { permission_id: 2 },
                { permission_id: 3 }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });

    it('should return 403 if user has no permissions', () => {
        req.user = {
            permissions: []
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectForbiddenResponse(res, 'You cannot access this resource');
        expectNextNotCalled(next as any);
    });

    it('should return 403 if user has undefined permissions', () => {
        req.user = {
            permissions: undefined
        };

        const permissions = [1, 2];

        expect(() => hasPermission(permissions, req, res, next)).toThrow();
    });

    it('should return 403 if user has null permissions', () => {
        req.user = {
            permissions: null
        };

        const permissions = [1, 2];

        expect(() => hasPermission(permissions, req, res, next)).toThrow();
    });

    it('should call next if no permissions are required', () => {
        req.user = {
            permissions: [
                { permission_id: 1 }
            ]
        };

        const permissions: number[] = [];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });

    it('should handle user with permissions array containing objects with different structure', () => {
        req.user = {
            permissions: [
                { permission_id: 1, name: 'read' },
                { permission_id: 2, name: 'write' }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });

    it('should handle api_key with additional properties', () => {
        req.api_key = { 
            _id: 'some-api-key',
            name: 'Test API Key',
            is_active: true
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expectNextCalled(next as any);
    });
});
