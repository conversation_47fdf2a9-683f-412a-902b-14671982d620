import { jest } from '@jest/globals';

class MockJsonWebTokenError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'JsonWebTokenError';
    }
}

class MockTokenExpiredError extends Error {
    constructor(message: string, expiredAt: Date) {
        super(message);
        this.name = 'TokenExpiredError';
        (this as any).expiredAt = expiredAt;
    }
}

class MockNotBeforeError extends Error {
    constructor(message: string, date: Date) {
        super(message);
        this.name = 'NotBeforeError';
        (this as any).date = date;
    }
}

const mockJwt = {
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn().mockReturnValue({ id: 'mock-user-id' }),
    decode: jest.fn().mockReturnValue({ id: 'mock-user-id' }),
    JsonWebTokenError: MockJsonWebTokenError,
    TokenExpiredError: MockTokenExpiredError,
    NotBeforeError: MockNotBeforeError
};

export default mockJwt;
