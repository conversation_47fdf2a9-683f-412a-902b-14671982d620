import request from 'supertest';
import app from '../../../server';
jest.mock('../../../modules/db', () => require('../../mocks/modules/db.mock'));
import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';
import jwt from 'jsonwebtoken';
import { getStaticMap } from '../../../utils/staticMap';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../../utils/staticMap', () => require('../../mocks/modules/staticMap.mock'));

describe('Notification Summary V2 API', () => {
    describe('GET /api/v2/summaryReports/map', () => {
        let verifySpy: ReturnType<typeof jest.spyOn>;
        beforeEach(() => {
            jest.resetAllMocks();
            verifySpy = jest.spyOn(jwt as any, 'verify').mockImplementation((...args: unknown[]) => {
                const token = args[0] as string;
                try { return JSON.parse(token); } catch { return {}; }
            });
        });
        afterEach(() => { verifySpy.mockRestore(); });

        it('should return 400 if token is missing', async () => {
            const res = await request(app).get('/api/v2/summaryReports/map');
            expect(res.status).toBe(400);
        });

        it('should return 200 and serve map image successfully', async () => {
            const token = JSON.stringify({ markers: [{ geometry: { coordinates: [0,0] } }] });
            (getStaticMap as jest.Mock).mockResolvedValue({
                mimeType: 'image/png',
                source: {
                    headers: { get: jest.fn().mockReturnValue('0') },
                    body: new ReadableStream({ start(controller){ controller.close(); } })
                }
            } as never);

            const res = await request(app).get(`/api/v2/summaryReports/map?token=${token}`);
            expect(res.status).toBe(200);
        });

        it('should return 500 when markers missing', async () => {
            const token = JSON.stringify({ markers: null });
            const res = await request(app).get(`/api/v2/summaryReports/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 when mapData invalid', async () => {
            const token = JSON.stringify({ markers: [{ geometry: { coordinates: [0,0] } }] });
            (getStaticMap as jest.Mock).mockResolvedValue(null as never);

            const res = await request(app).get(`/api/v2/summaryReports/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 when source missing', async () => {
            const token = JSON.stringify({ markers: [{ geometry: { coordinates: [0,0] } }] });
            (getStaticMap as jest.Mock).mockResolvedValue({ mimeType: 'image/png', source: null } as never);

            const res = await request(app).get(`/api/v2/summaryReports/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 when getStaticMap throws', async () => {
            const token = JSON.stringify({ markers: [{ geometry: { coordinates: [0,0] } }] });
            (getStaticMap as jest.Mock).mockImplementation(() => { throw new Error('map'); });

            const res = await request(app).get(`/api/v2/summaryReports/map?token=${token}`);
            expect(res.status).toBe(500);
        });
    });
});


