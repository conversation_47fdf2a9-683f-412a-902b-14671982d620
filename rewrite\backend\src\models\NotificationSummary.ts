import mongoose from "mongoose";
import User from "./User";
import db from "../modules/db";
import { INotificationSummary } from "../interfaces/Notification";

const NotificationSummarySchema = new mongoose.Schema({
    unit_id: { type: [String], required: false }, // kept for backward compatibility
    vessel_ids: {
        type: [mongoose.Schema.Types.Mixed],
        required: true,
        validate: {
            validator: function (arr: (string | mongoose.Types.ObjectId)[]) {
                return arr.every((item: string | mongoose.Types.ObjectId) => item === "all" || mongoose.Types.ObjectId.isValid(item));
            },
            message: 'vessel_ids must contain valid ObjectIds or "all"',
        },
    },
    preference: { type: [String], required: true, enum: ["daily", "weekly", "monthly"] },
    receivers: [
        {
            type: String,
            default: [],
        },
    ],
    title: { type: [String], required: true },
    created_by: { type: mongoose.Schema.Types.ObjectId, ref: User },
    created_at: { type: Date, default: () => new Date().toISOString() },
    is_enabled: { type: Boolean, default: true },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});

const NotificationSummary = db.qm.model<INotificationSummary>("NotificationSummary", NotificationSummarySchema, "notifications_summary");

export default NotificationSummary;
