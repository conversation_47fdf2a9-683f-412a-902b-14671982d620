import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import request from "supertest";
import app from '../../server';
import { apiEndpointsList } from '../../utils/endpointIds';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));

describe('API Endpoints API', () => {

    describe('GET /api/apiEndpoints', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/apiEndpoints');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).get('/api/apiEndpoints').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch the list of API endpoints if the user is authorized or 403 for api-key authorization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).get('/api/apiEndpoints').set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(res.body).toBeInstanceOf(Array);
                        expect(res.body).toEqual(apiEndpointsList);
                        ['endpoint_id', 'name', 'category', 'is_public', 'is_accessible'].forEach(prop => {
                            expect(res.body[0]).toHaveProperty(prop);
                        });
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });


});
