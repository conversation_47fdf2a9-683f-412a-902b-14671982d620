import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import { rolesList } from '../data/Roles';
import { permissionsList } from '../data/Permissions';
import { setupAuthorizedAuthMocks, setupAuthMocks } from '../mocks/auth.mock';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import Role from '../../models/Role';
import Permission from '../../models/Permission';
import mongoose from 'mongoose';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Role', () => require('../mocks/models/role.mock'));
jest.mock('../../models/Permission', () => require('../mocks/models/permission.mock'));
jest.mock('../../modules/email', () => require('../mocks/modules/email.mock'));
jest.mock('../../modules/ioEmitter', () => require('../mocks/modules/ioEmitter.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));

describe('Roles API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('GET /api/roles', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/roles');
                    expect(res.status).toBe(401);
                });

                it('should return the roles list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.aggregate as jest.Mock).mockResolvedValueOnce(rolesList as never);
                    const res = await request(app).get('/api/roles').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'role_id', 'role_name', 'denied_permissions', 'editable', 'deletable', 'creation_timestamp'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.aggregate as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);
                    const res = await request(app).get('/api/roles').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/roles', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/roles');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', nonAuthToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if role_name is missing or invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: '' });
                    expect(res.status).toBe(400);
                });

                it('should create a role successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.create as jest.Mock).mockResolvedValueOnce({ role_name: 'admin', role_id: -1 } as never);

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.create as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/roles/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/roles/123456');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/roles/123456')
                        .set('Authorization', nonAuthToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if request body is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: '', denied_permissions: '' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if request body has invalid permissions array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: 'valid-role', denied_permissions: ['1', '2'] });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if role does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });
                    expect(res.status).toBe(404);
                });

                it('should return 400 if role is not editable', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: false } as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid permissions in denied_permissions array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: true, denied_permissions: [] } as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: true }] as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [99] });

                    expect(res.status).toBe(400);
                });

                it('should return 403 if trying to modify a forbidden permission (branch 1)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: true, denied_permissions: [1] } as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: false }, { permission_id: 2, assignable: true }] as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [2] });

                    expect(res.status).toBe(403);
                });

                it('should return 403 if trying to modify a forbidden permission (branch 2)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: true, denied_permissions: [] } as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: false }, { permission_id: 2, assignable: true }] as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [2, 1] });

                    expect(res.status).toBe(403);
                });

                it('should update role successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: true, role_name: 'original_role', denied_permissions: [], save: jest.fn().mockResolvedValue(true as never) } as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: true }, { permission_id: 2, assignable: true }] as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role', denied_permissions: [1] });

                    expect(res.status).toBe(200);
                });

                it('should update role name successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ editable: true, role_name: 'original_role', denied_permissions: [], save: jest.fn().mockResolvedValue(true as never) } as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: true }, { permission_id: 2, assignable: true }] as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user has equal or higher hierarchy number than the role', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockRole = { _id: '507f1f77bcf86cd799439011', editable: true, hierarchy_number: 0, save: jest.fn().mockResolvedValue({} as never) } as never;

                    (Role.findById as jest.Mock).mockResolvedValueOnce(mockRole);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe("You cannot update this role as it exceeds your hierarchy level");
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/roles/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/roles/123456');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app)
                        .delete('/api/roles/123456')
                        .set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .delete('/api/roles/invalid_id')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if role does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce(null as never);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 400 if role is not deletable', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ deletable: false } as never);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if role is assigned to a user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ deletable: true, role_id: 'some_role_id' } as never);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should delete role successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockResolvedValueOnce({ deletable: true, _id: '507f1f77bcf86cd799439011', role_id: 'some_role_id' } as never);
                    (User.findOne as jest.Mock).mockResolvedValueOnce([authorizedUser] as never).mockResolvedValueOnce(null as never);
                    (Role.findOneAndDelete as jest.Mock).mockResolvedValueOnce(true as never);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.findById as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user has equal or higher hierarchy number than the role', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockRole = { _id: '507f1f77bcf86cd799439011', hierarchy_number: 0, deletable: true } as never;

                    (Role.findById as jest.Mock).mockResolvedValueOnce(mockRole);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("You cannot remove this role as it exceeds your hierarchy level");
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/roles/permissionUpdate', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/roles/permissionUpdate');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', nonAuthToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                });

                it('should return 400 if roles_permissions is missing or empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [] });
                    expect(res.status).toBe(400);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (Role.find as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(500);
                });

                it('should return 403 if user tries to update a role with equal or higher hierarchy level', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (Role.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 1, editable: true }] as never);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update any role as it exceeds your hierarchy level');
                });

                it('should return 403 if no roles are editable', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    (Role.find as jest.Mock).mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 3, editable: false }] as never);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('No roles are permitted to be edited');
                });

                it('should return 403 if roles contain invalid permission IDs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const roles = rolesList.map((role) => ({
                        ...role,
                        _id: String(role._id),
                        hierarchy_number: 3,
                        toObject: () => role
                    })) as never;

                    (Role.find as jest.Mock).mockResolvedValueOnce(roles);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: true }] as never);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011', denied_permissions: [99] }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('The role(s) have invalid permission IDs');
                });

                it('should update role permissions successfully if id not matched', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const role = rolesList[0];
                    role._id = '507f1f77bcf86cd799439022';
                    role['hierarchy_number'] = 3;
                    role['editable'] = true;
                    role['toObject'] = () => role;
                    const roles = [role] as never;
                    (Role.find as jest.Mock).mockResolvedValueOnce(roles);
                    (Permission.find as jest.Mock).mockResolvedValueOnce([{ permission_id: 1, assignable: true }] as never);
                    (Role.bulkWrite as jest.Mock).mockResolvedValueOnce({ modifiedCount: 1 } as never);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439022', denied_permissions: [1] }] });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Role Permissions Updated');
                });

                it("should return 400 with a descriptive message if an invalid value is provided for 'denied_permissions'", async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const invalidRoleId = "invalidObjectId";

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({
                            roles_permissions: [{
                                _id: invalidRoleId,
                                denied_permissions: [100]
                            }]
                        });

                    expect(res.status).toBe(400);
                });

                it("should return 403 if roles contains non assignable permission id", async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const validRoleId = "507f1f77bcf86cd799439011";
                    const nonMatchingRoleId = "507f1f77bcf86cd799439022";
                    const role = rolesList[0];
                    role._id = validRoleId;
                    role['hierarchy_number'] = 3;
                    role['editable'] = true;
                    role['denied_permissions'] = [200, 100];
                    role['toObject'] = () => role;
                    const roles = [role] as never;
                    const nonMatchingRole = rolesList[1];
                    nonMatchingRole._id = nonMatchingRoleId;
                    nonMatchingRole['hierarchy_number'] = 3;
                    nonMatchingRole['editable'] = true;
                    nonMatchingRole['denied_permissions'] = [300];
                    nonMatchingRole['toObject'] = () => nonMatchingRole;
                    const nonMatchingRoles = [nonMatchingRole] as never;
                    (Role.find as jest.Mock).mockResolvedValueOnce([
                        roles,
                        nonMatchingRoles
                    ] as never);

                    (Permission.find as jest.Mock).mockResolvedValueOnce(permissionsList as never);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({
                            roles_permissions: [
                                { _id: validRoleId, denied_permissions: [100] }
                            ]
                        });

                    expect(res.status).toBe(403);
                });

                it('should return 403 if role does not contain all non-assignable permission IDs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const role = {
                        _id: '507f1f77bcf86cd799439022',
                        hierarchy_number: 3,
                        editable: true,
                        denied_permissions: [1, 2]
                    };
                    role['toObject'] = () => role;

                    const permissions = [
                        { permission_id: 1, assignable: true },
                        { permission_id: 2, assignable: true },
                        { permission_id: 3, assignable: true },
                        { permission_id: 4, assignable: false }
                    ];

                    (Role.find as jest.Mock).mockResolvedValueOnce([role] as never);
                    (Permission.find as jest.Mock).mockResolvedValueOnce(permissions as never);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({
                            roles_permissions: [{
                                _id: '507f1f77bcf86cd799439022',
                                denied_permissions: [1, 2]
                            }]
                        });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('The role does not contain non-assignable permission IDs');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/roles/reorder', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/roles/reorder');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if roles array is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({ roles: [] });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Roles must be a non-empty array');
                });

                it('should return 400 if role ID is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: 'invalidObjectId', hierarchy_number: 2 }]
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Invalid value \'invalidObjectId\' provided for field \'roles[0]._id\'');
                });

                it('should return 400 if hierarchy_number is not an integer greater than 0', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: new mongoose.Types.ObjectId().toString(), hierarchy_number: 0 }] as never
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Hierarchy number must be an integer greater than 0');
                });

                it('should return 403 if the user is not authorized to update any roles', async () => {
                    userOrApiKey.authorized.role.hierarchy_number = 4;
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.find as jest.Mock).mockResolvedValueOnce([
                        { _id: new mongoose.Types.ObjectId(), hierarchy_number: 2 }
                    ] as never);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: new mongoose.Types.ObjectId().toString(), hierarchy_number: 3 }] as never
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update any roles');
                    userOrApiKey.authorized.role.hierarchy_number = 1;
                });

                it('should return 403 if the user is not authorized to update the specified roles', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.find as jest.Mock).mockResolvedValueOnce([
                        { _id: new mongoose.Types.ObjectId("678947f96f36057641892649"), hierarchy_number: 2 }
                    ] as never);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: new mongoose.Types.ObjectId("678947f96f36057641892648").toString(), hierarchy_number: 3 }]
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update these roles');
                });

                it('should successfully update the hierarchy number of allowed roles', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.find as jest.Mock).mockResolvedValueOnce([
                        { _id: '678947f96f36057641892649', hierarchy_number: 2 },
                        { _id: '678947f96f36057641892647', hierarchy_number: 1 },
                        { _id: '678947f96f36057641892648', hierarchy_number: 3 },
                        { _id: '678947f96f36057641892645', hierarchy_number: 5 }
                    ] as never);
                    (Role.bulkWrite as jest.Mock).mockResolvedValueOnce({ modifiedCount: 1 } as never);

                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [
                                { _id: '678947f96f36057641892649', hierarchy_number: 3 },
                                { _id: '678947f96f36057641892647', hierarchy_number: 4 },
                                { _id: '678947f96f36057641892648', hierarchy_number: 2 },
                                { _id: '678947f96f36057641892645', hierarchy_number: 5 }
                            ]
                        });

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Hierarchy numbers updated successfully for allowed roles');
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Role.find as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: new mongoose.Types.ObjectId().toString(), hierarchy_number: 5 }]
                        });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
