import request from 'supertest';
import app from '../../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthorizedAuthMocks } from '../../mocks/auth.mock';
import User from '../../../models/User';
import ApiKey from '../../../models/ApiKey';
import vesselService from '../../../services/Vessel.service';
import vesselLocationService from '../../../services/VesselLocation.service';
import { canAccessVessel, getLocationsCollections, validateError } from '../../../utils/functions';

jest.mock('../../../modules/db', () => require('../../mocks/modules/db.mock'));
jest.mock('../../../services/Vessel.service', () => require('../../mocks/services/vesselService.mock'));
jest.mock('../../../services/VesselLocation.service', () => require('../../mocks/services/vesselLocation.mock'));
jest.mock('../../../utils/functions', () => require('../../mocks/utils/functions.mock'));
jest.mock('../../../models/User', () => require('../../mocks/models/user.mock'));
jest.mock('../../../models/ApiKey', () => require('../../mocks/models/apiKey.mock'));
jest.mock('../../../queries/User', () => ({ getUser: jest.fn() }));

describe('VesselLocation V2 API', () => {
    describe('POST /api/v2/vesselLocations/:vesselId', () => {
        const runTests = (authMethod: 'user' | 'api-key', generateToken: (id: string) => string, userOrApiKey: any) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    (getLocationsCollections as jest.Mock).mockResolvedValue([
                        {
                            aggregate: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([
                                    [{ _id: '1', timestamp: new Date().toISOString(), groundSpeed: 1, isStationary: false, latitude: 1, longitude: 2 }],
                                    [{ _id: '2', timestamp: new Date().toISOString(), groundSpeed: 1, isStationary: false, latitude: 1, longitude: 2 }],
                                ] as never),
                                limit: jest.fn().mockReturnThis(),
                            }),
                        },
                    ] as never);
                    (vesselService.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439012' } as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/v2/vesselLocations/507f1f77bcf86cd799439012');
                    expect(res.status).toBe(401);
                });

                it('should return 404 if vessel does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (vesselService.findById as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).post('/api/v2/vesselLocations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 403 if cannot access vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);
                    const res = await request(app).post('/api/v2/vesselLocations/507f1f77bcf86cd799439012').set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if endTimestamp provided without startTimestamp', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: Date.now() });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and list locations for range', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should apply swagger limit when Referer contains /docs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .set('Referer', 'http://localhost/docs')
                        .send({ startTimestamp: Date.now() - 1000 });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with lastKnown single object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (vesselLocationService.findLastKnownLocation as jest.Mock).mockResolvedValue({ _id: '1' } as never);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 1 });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual({ _id: '1' });
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    (getLocationsCollections as jest.Mock).mockImplementation(() => { throw new Error('db'); });
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });

                it('should return 200 with excludeIds applied', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: Date.now() - 1000, endTimestamp: Date.now(), excludeIds: ['507f1f77bcf86cd799439099'] });
                    expect(res.status).toBe(200);
                });

                it('should return 400 for invalid validators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ lastKnown: 5 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid validators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ startTimestamp: 'abc' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid validators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ endTimestamp: 'xyz' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid validators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/v2/vesselLocations/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'not-array' });
                    expect(res.status).toBe(400);
                });
            });
        };

        const { generateUserToken, authorizedUser, nonAuthorizedUser } = require('../../data/Auth');
        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


